{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useDropdownItem } from '@restart/ui/DropdownItem';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    eventKey,\n    disabled = false,\n    onClick,\n    active,\n    as: Component = Anchor,\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown-item');\n  const [dropdownItemProps, meta] = useDropdownItem({\n    key: eventKey,\n    href: props.href,\n    disabled,\n    onClick,\n    active\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ...dropdownItemProps,\n    ref: ref,\n    className: classNames(className, prefix, meta.isActive && 'active', disabled && 'disabled')\n  });\n});\nDropdownItem.displayName = 'DropdownItem';\nexport default DropdownItem;", "map": {"version": 3, "names": ["classNames", "React", "useDropdownItem", "<PERSON><PERSON>", "useBootstrapPrefix", "jsx", "_jsx", "DropdownItem", "forwardRef", "_ref", "ref", "bsPrefix", "className", "eventKey", "disabled", "onClick", "active", "as", "Component", "props", "prefix", "dropdownItemProps", "meta", "key", "href", "isActive", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/DropdownItem.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useDropdownItem } from '@restart/ui/DropdownItem';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownItem = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  eventKey,\n  disabled = false,\n  onClick,\n  active,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown-item');\n  const [dropdownItemProps, meta] = useDropdownItem({\n    key: eventKey,\n    href: props.href,\n    disabled,\n    onClick,\n    active\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ...dropdownItemProps,\n    ref: ref,\n    className: classNames(className, prefix, meta.isActive && 'active', disabled && 'disabled')\n  });\n});\nDropdownItem.displayName = 'DropdownItem';\nexport default DropdownItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAAC,IAAA,EAShDC,GAAG,KAAK;EAAA,IATyC;IAClDC,QAAQ;IACRC,SAAS;IACTC,QAAQ;IACRC,QAAQ,GAAG,KAAK;IAChBC,OAAO;IACPC,MAAM;IACNC,EAAE,EAAEC,SAAS,GAAGf,MAAM;IACtB,GAAGgB;EACL,CAAC,GAAAV,IAAA;EACC,MAAMW,MAAM,GAAGhB,kBAAkB,CAACO,QAAQ,EAAE,eAAe,CAAC;EAC5D,MAAM,CAACU,iBAAiB,EAAEC,IAAI,CAAC,GAAGpB,eAAe,CAAC;IAChDqB,GAAG,EAAEV,QAAQ;IACbW,IAAI,EAAEL,KAAK,CAACK,IAAI;IAChBV,QAAQ;IACRC,OAAO;IACPC;EACF,CAAC,CAAC;EACF,OAAO,aAAaV,IAAI,CAACY,SAAS,EAAE;IAClC,GAAGC,KAAK;IACR,GAAGE,iBAAiB;IACpBX,GAAG,EAAEA,GAAG;IACRE,SAAS,EAAEZ,UAAU,CAACY,SAAS,EAAEQ,MAAM,EAAEE,IAAI,CAACG,QAAQ,IAAI,QAAQ,EAAEX,QAAQ,IAAI,UAAU;EAC5F,CAAC,CAAC;AACJ,CAAC,CAAC;AACFP,YAAY,CAACmB,WAAW,GAAG,cAAc;AACzC,eAAenB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}