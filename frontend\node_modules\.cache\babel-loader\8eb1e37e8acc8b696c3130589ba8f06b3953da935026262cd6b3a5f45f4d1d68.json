{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\pages\\\\dashboard\\\\Order.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Stepper, Step, StepLabel, Button, Paper, Card, CardContent, CardMedia, Chip, Alert, TextField, Divider } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport printingService from '../../services/printingService';\nimport FileUpload from '../../components/dashboard/FileUpload';\nimport ErrorBoundary from '../../components/common/ErrorBoundary';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst steps = ['Select Category', 'Choose Product', 'Configure Order', 'Upload Files', 'Review & Submit'];\nconst Order = () => {\n  _s();\n  const navigate = useNavigate();\n  const [activeStep, setActiveStep] = useState(0);\n  const [categories, setCategories] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState(null);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [orderItems, setOrderItems] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Product configuration state\n  const [quantity, setQuantity] = useState(100);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [priceCalculation, setPriceCalculation] = useState(null);\n  const [calculatingPrice, setCalculatingPrice] = useState(false);\n\n  // Order and file upload state\n  const [createdOrder, setCreatedOrder] = useState(null);\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [submittingOrder, setSubmittingOrder] = useState(false);\n  useEffect(() => {\n    loadCategories();\n  }, []);\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const data = await printingService.getCategories();\n      setCategories(data);\n    } catch (err) {\n      setError('Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadProducts = async categorySlug => {\n    try {\n      setLoading(true);\n      const data = await printingService.getProducts(categorySlug);\n      setProducts(data);\n    } catch (err) {\n      setError('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCategorySelect = async category => {\n    setSelectedCategory(category);\n    await loadProducts(category.slug);\n    setActiveStep(1);\n  };\n  const handleProductSelect = product => {\n    setSelectedProduct(product);\n    // Reset configuration when selecting a new product\n    setQuantity(product.min_quantity || 100);\n    setSelectedOptions({});\n    setPriceCalculation(null);\n    setActiveStep(2);\n  };\n\n  // Calculate price when quantity or options change\n  const calculatePrice = async () => {\n    if (!selectedProduct) return;\n\n    // Convert quantity to number for API call\n    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n    if (isNaN(numericQuantity) || numericQuantity < (selectedProduct.min_quantity || 1)) return;\n    setCalculatingPrice(true);\n    try {\n      const result = await printingService.calculatePrice(selectedProduct.id, numericQuantity, selectedOptions);\n      setPriceCalculation(result);\n    } catch (err) {\n      setError('Failed to calculate price');\n      console.error('Price calculation error:', err);\n    } finally {\n      setCalculatingPrice(false);\n    }\n  };\n\n  // Effect to calculate price when quantity or options change\n  useEffect(() => {\n    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n    if (selectedProduct && !isNaN(numericQuantity) && numericQuantity >= (selectedProduct.min_quantity || 1)) {\n      const timeoutId = setTimeout(() => {\n        calculatePrice();\n      }, 300); // Debounce price calculation\n\n      return () => clearTimeout(timeoutId);\n    } else {\n      setPriceCalculation(null);\n    }\n  }, [selectedProduct, quantity, selectedOptions]);\n  const handleNext = async () => {\n    // If moving from configuration step, save the order item and create order\n    if (activeStep === 2 && selectedProduct && priceCalculation) {\n      const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n      const orderItem = {\n        product_id: selectedProduct.id,\n        quantity: numericQuantity,\n        selected_options: selectedOptions,\n        specifications: selectedProduct.specifications\n      };\n      setOrderItems([orderItem]);\n\n      // Create the order when moving to file upload step\n      await createOrder([orderItem]);\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n  const createOrder = async items => {\n    try {\n      setLoading(true);\n      const orderData = {\n        items,\n        special_instructions: '',\n        delivery_method: 'standard'\n      };\n      const order = await printingService.createOrder(orderData);\n      setCreatedOrder(order);\n    } catch (err) {\n      setError(err.message || 'Failed to create order');\n      console.error('Order creation error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFilesUploaded = files => {\n    setUploadedFiles(prev => [...prev, ...files]);\n  };\n  const handleFileUploadError = error => {\n    setError(error);\n  };\n  const handleSubmitOrder = async () => {\n    if (!createdOrder) {\n      setError('No order to submit');\n      return;\n    }\n    try {\n      setSubmittingOrder(true);\n      // Order is already created, just navigate to success or orders page\n      navigate('/dashboard/orders');\n    } catch (err) {\n      setError(err.message || 'Failed to submit order');\n    } finally {\n      setSubmittingOrder(false);\n    }\n  };\n  const renderStepContent = step => {\n    var _selectedProduct$opti;\n    switch (step) {\n      case 0:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Select a Printing Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: {\n                xs: '1fr',\n                sm: 'repeat(2, 1fr)',\n                md: 'repeat(3, 1fr)'\n              },\n              gap: 3\n            },\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                cursor: 'pointer',\n                '&:hover': {\n                  elevation: 4\n                }\n              },\n              onClick: () => handleCategorySelect(category),\n              children: [category.image && /*#__PURE__*/_jsxDEV(CardMedia, {\n                component: \"img\",\n                height: \"140\",\n                image: category.image,\n                alt: category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  gutterBottom: true,\n                  variant: \"h6\",\n                  component: \"div\",\n                  children: category.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: category.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this), category.products_count !== undefined && /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `${category.products_count} products`,\n                  size: \"small\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, category.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this);\n      case 1:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [\"Choose a Product from \", selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: {\n                xs: '1fr',\n                sm: 'repeat(2, 1fr)',\n                md: 'repeat(3, 1fr)'\n              },\n              gap: 3\n            },\n            children: products.map(product => /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                cursor: 'pointer',\n                '&:hover': {\n                  elevation: 4\n                }\n              },\n              onClick: () => handleProductSelect(product),\n              children: [product.image && /*#__PURE__*/_jsxDEV(CardMedia, {\n                component: \"img\",\n                height: \"140\",\n                image: product.image,\n                alt: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  gutterBottom: true,\n                  variant: \"h6\",\n                  component: \"div\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: product.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  children: product.formatted_base_price\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: [\"Min: \", product.min_quantity, \" | Production: \", product.production_time_days, \" days\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Configure Your Order\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), selectedProduct && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: {\n                xs: 'column',\n                md: 'row'\n              },\n              gap: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: selectedProduct.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: selectedProduct.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  children: \"Specifications:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 2\n                  },\n                  children: Object.entries(selectedProduct.specifications || {}).map(([key, value]) => /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      mb: 0.5\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [key, \":\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 27\n                    }, this), \" \", value]\n                  }, key, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Production Time: \", selectedProduct.production_time_days, \" days\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Order Configuration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  label: \"Quantity\",\n                  type: \"text\",\n                  value: quantity,\n                  onChange: e => {\n                    const inputValue = e.target.value;\n\n                    // Allow empty input for better user experience while typing\n                    if (inputValue === '') {\n                      setQuantity('');\n                      return;\n                    }\n\n                    // Only allow numeric characters\n                    if (!/^\\d+$/.test(inputValue)) {\n                      return;\n                    }\n                    const numericValue = parseInt(inputValue, 10);\n                    const minQty = selectedProduct.min_quantity || 1;\n                    const maxQty = selectedProduct.max_quantity;\n\n                    // Apply validation constraints\n                    let validatedQuantity = Math.max(minQty, numericValue);\n                    if (maxQty && validatedQuantity > maxQty) {\n                      validatedQuantity = maxQty;\n                    }\n                    setQuantity(validatedQuantity);\n                  },\n                  onBlur: e => {\n                    // Ensure we have a valid number when user leaves the field\n                    const currentValue = e.target.value;\n                    if (currentValue === '' || isNaN(parseInt(currentValue, 10))) {\n                      setQuantity(selectedProduct.min_quantity || 1);\n                    }\n                  },\n                  onKeyDown: e => {\n                    // Allow navigation keys, backspace, delete, tab, escape, enter\n                    if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'Home', 'End'].includes(e.key)) {\n                      return;\n                    }\n\n                    // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z\n                    if (e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) {\n                      return;\n                    }\n\n                    // Only allow numeric characters\n                    if (!/^\\d$/.test(e.key)) {\n                      e.preventDefault();\n                    }\n                  },\n                  slotProps: {\n                    htmlInput: {\n                      inputMode: 'numeric',\n                      pattern: '[0-9]*',\n                      autoComplete: 'off',\n                      style: {\n                        fontSize: '16px',\n                        // Prevents zoom on mobile devices\n                        textAlign: 'left'\n                      }\n                    }\n                  },\n                  helperText: `Min: ${selectedProduct.min_quantity || 1}${selectedProduct.max_quantity ? `, Max: ${selectedProduct.max_quantity}` : ''} • Type quantity directly`,\n                  fullWidth: true,\n                  sx: {\n                    mb: 3,\n                    '& .MuiOutlinedInput-root': {\n                      '&:hover fieldset': {\n                        borderColor: 'primary.main'\n                      },\n                      '&.Mui-focused fieldset': {\n                        borderColor: 'primary.main'\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this), ((_selectedProduct$opti = selectedProduct.options) === null || _selectedProduct$opti === void 0 ? void 0 : _selectedProduct$opti.quantity_pricing) && /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    gutterBottom: true,\n                    children: \"Pricing Tiers:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      flexDirection: 'column',\n                      gap: 1\n                    },\n                    children: selectedProduct.options.quantity_pricing.map((tier, index) => {\n                      var _selectedProduct$opti2;\n                      const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n                      const isCurrentTier = !isNaN(numericQuantity) && numericQuantity >= tier.min_quantity && (index === selectedProduct.options.quantity_pricing.length - 1 || numericQuantity < ((_selectedProduct$opti2 = selectedProduct.options.quantity_pricing[index + 1]) === null || _selectedProduct$opti2 === void 0 ? void 0 : _selectedProduct$opti2.min_quantity));\n                      return /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          p: 1.5,\n                          borderRadius: 1,\n                          border: '1px solid',\n                          borderColor: isCurrentTier ? 'primary.main' : 'grey.300',\n                          backgroundColor: isCurrentTier ? 'primary.50' : 'transparent',\n                          transition: 'all 0.2s ease-in-out'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: isCurrentTier ? 600 : 400,\n                            color: isCurrentTier ? 'primary.main' : 'text.primary'\n                          },\n                          children: [tier.min_quantity, \"+ units: RM \", tier.price_per_unit.toFixed(2), \" each\", isCurrentTier && /*#__PURE__*/_jsxDEV(Chip, {\n                            label: \"Current\",\n                            size: \"small\",\n                            color: \"primary\",\n                            sx: {\n                              ml: 1,\n                              height: 20\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 467,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 458,\n                          columnNumber: 33\n                        }, this)\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 447,\n                        columnNumber: 31\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    my: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    gutterBottom: true,\n                    children: \"Price Summary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 23\n                  }, this), calculatingPrice ? /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: \"Calculating...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 493,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 25\n                  }, this) : priceCalculation ? /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: [\"Unit Price: RM \", priceCalculation.unit_price.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: [\"Quantity: \", typeof quantity === 'string' ? parseInt(quantity, 10) || 0 : quantity]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 500,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      color: \"primary\",\n                      sx: {\n                        mt: 1\n                      },\n                      children: [\"Total: \", priceCalculation.formatted_total_price]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 503,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      sx: {\n                        mt: 1,\n                        display: 'block'\n                      },\n                      children: [\"Estimated production time: \", priceCalculation.production_time_days, \" days\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 25\n                  }, this) : (() => {\n                    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n                    return !isNaN(numericQuantity) && numericQuantity >= ((selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.min_quantity) || 1);\n                  })() ? /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: \"Loading pricing...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"error\",\n                    children: [\"Please enter a valid quantity (minimum: \", (selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.min_quantity) || 1, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Upload Artwork Files\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Upload your artwork files for printing. You can upload multiple files and specify the file type for each upload.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this), createdOrder ? /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n            fallback: /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Failed to load file upload component. Please refresh the page and try again.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 19\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(FileUpload, {\n              orderId: createdOrder.id,\n              onFilesUploaded: handleFilesUploaded,\n              onError: handleFileUploadError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: \"Creating order... Please wait.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this);\n      case 4:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Review Your Order\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this), createdOrder && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: {\n                xs: '1fr',\n                md: '1fr 1fr'\n              },\n              gap: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Order Summary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                gutterBottom: true,\n                children: [\"Order #\", createdOrder.order_number]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 19\n              }, this), selectedProduct && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Product:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: selectedProduct.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: \"Quantity:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: quantity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 23\n                }, this), priceCalculation && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    sx: {\n                      mt: 1\n                    },\n                    children: \"Total Price:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    color: \"primary\",\n                    children: priceCalculation.formatted_total_price\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: createdOrder.status || 'Pending',\n                  color: \"warning\",\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: [\"Uploaded Files (\", uploadedFiles.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 19\n              }, this), uploadedFiles.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                children: uploadedFiles.map((file, index) => /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 1,\n                    p: 1,\n                    bgcolor: 'grey.50',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    noWrap: true,\n                    children: file.original_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [file.formatted_file_size, \" \\u2022 \", file.file_type_label]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 27\n                  }, this)]\n                }, file.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"warning\",\n                children: \"No files uploaded yet. You can still submit the order and upload files later.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                gridColumn: '1 / -1',\n                textAlign: 'center',\n                mt: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                size: \"large\",\n                onClick: handleSubmitOrder,\n                disabled: submittingOrder,\n                children: submittingOrder ? 'Submitting...' : 'Submit Order'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this);\n      default:\n        return 'Unknown step';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Create New Order\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Stepper, {\n        activeStep: activeStep,\n        sx: {\n          mb: 4\n        },\n        children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n          children: /*#__PURE__*/_jsxDEV(StepLabel, {\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this)\n        }, label, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: 400\n        },\n        children: renderStepContent(activeStep)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'row',\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          disabled: activeStep === 0,\n          onClick: handleBack,\n          sx: {\n            mr: 1\n          },\n          children: \"Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flex: '1 1 auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 11\n        }, this), activeStep < steps.length - 1 && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNext,\n          disabled: loading || activeStep === 0 && !selectedCategory || activeStep === 1 && !selectedProduct || activeStep === 2 && (!priceCalculation || quantity < ((selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.min_quantity) || 1)) || activeStep === 3 && !createdOrder,\n          children: loading ? 'Creating Order...' : 'Next'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 13\n        }, this), activeStep === steps.length - 1 && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSubmitOrder,\n          disabled: submittingOrder || !createdOrder,\n          children: submittingOrder ? 'Submitting...' : 'Complete Order'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 674,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 663,\n    columnNumber: 5\n  }, this);\n};\n_s(Order, \"vu7fyd7lvfRhQ6sPiNrFY7QdNP4=\", false, function () {\n  return [useNavigate];\n});\n_c = Order;\nexport default Order;\nvar _c;\n$RefreshReg$(_c, \"Order\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Paper", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Chip", "<PERSON><PERSON>", "TextField", "Divider", "useNavigate", "printingService", "FileUpload", "Error<PERSON>ou<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "steps", "Order", "_s", "navigate", "activeStep", "setActiveStep", "categories", "setCategories", "products", "setProducts", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedProduct", "setSelectedProduct", "orderItems", "setOrderItems", "loading", "setLoading", "error", "setError", "quantity", "setQuantity", "selectedOptions", "setSelectedOptions", "priceCalculation", "setPriceCalculation", "calculatingPrice", "setCalculatingPrice", "createdOrder", "setCreated<PERSON><PERSON>r", "uploadedFiles", "setUploadedFiles", "submittingOrder", "setSubmittingOrder", "loadCategories", "data", "getCategories", "err", "loadProducts", "categorySlug", "getProducts", "handleCategorySelect", "category", "slug", "handleProductSelect", "product", "min_quantity", "calculatePrice", "numericQuantity", "parseInt", "isNaN", "result", "id", "console", "timeoutId", "setTimeout", "clearTimeout", "handleNext", "orderItem", "product_id", "selected_options", "specifications", "createOrder", "prevActiveStep", "handleBack", "items", "orderData", "special_instructions", "delivery_method", "order", "message", "handleFilesUploaded", "files", "prev", "handleFileUploadError", "handleSubmitOrder", "renderStepContent", "step", "_selectedProduct$opti", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "display", "gridTemplateColumns", "xs", "sm", "md", "gap", "map", "cursor", "elevation", "onClick", "image", "component", "height", "alt", "name", "color", "description", "products_count", "undefined", "label", "size", "mt", "mb", "formatted_base_price", "production_time_days", "flexDirection", "flex", "p", "Object", "entries", "key", "value", "type", "onChange", "e", "inputValue", "target", "test", "numericValue", "min<PERSON>ty", "max<PERSON>ty", "max_quantity", "validatedQuantity", "Math", "max", "onBlur", "currentValue", "onKeyDown", "includes", "ctrl<PERSON>ey", "toLowerCase", "preventDefault", "slotProps", "htmlInput", "inputMode", "pattern", "autoComplete", "style", "fontSize", "textAlign", "helperText", "fullWidth", "borderColor", "options", "quantity_pricing", "tier", "index", "_selectedProduct$opti2", "isCurrentTier", "length", "borderRadius", "border", "backgroundColor", "transition", "fontWeight", "price_per_unit", "toFixed", "ml", "my", "alignItems", "unit_price", "formatted_total_price", "fallback", "severity", "orderId", "onFilesUploaded", "onError", "order_number", "status", "file", "bgcolor", "noWrap", "original_name", "formatted_file_size", "file_type_label", "gridColumn", "disabled", "justifyContent", "minHeight", "pt", "mr", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/pages/dashboard/Order.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Stepper,\n  Step,\n  StepLabel,\n  Button,\n  Paper,\n  Card,\n  CardContent,\n  CardMedia,\n  Chip,\n  Alert,\n  TextField,\n\n  Divider,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n} from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport printingService, { PrintingCategory, PrintingProduct, OrderItem, PriceCalculation, PrintingOrder, OrderFile } from '../../services/printingService';\nimport FileUpload from '../../components/dashboard/FileUpload';\nimport ErrorBoundary from '../../components/common/ErrorBoundary';\n\nconst steps = ['Select Category', 'Choose Product', 'Configure Order', 'Upload Files', 'Review & Submit'];\n\nconst Order: React.FC = () => {\n  const navigate = useNavigate();\n  const [activeStep, setActiveStep] = useState(0);\n  const [categories, setCategories] = useState<PrintingCategory[]>([]);\n  const [products, setProducts] = useState<PrintingProduct[]>([]);\n  const [selectedCategory, setSelectedCategory] = useState<PrintingCategory | null>(null);\n  const [selectedProduct, setSelectedProduct] = useState<PrintingProduct | null>(null);\n  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Product configuration state\n  const [quantity, setQuantity] = useState<number | string>(100);\n  const [selectedOptions, setSelectedOptions] = useState<Record<string, any>>({});\n  const [priceCalculation, setPriceCalculation] = useState<PriceCalculation | null>(null);\n  const [calculatingPrice, setCalculatingPrice] = useState(false);\n\n  // Order and file upload state\n  const [createdOrder, setCreatedOrder] = useState<PrintingOrder | null>(null);\n  const [uploadedFiles, setUploadedFiles] = useState<OrderFile[]>([]);\n  const [submittingOrder, setSubmittingOrder] = useState(false);\n\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const data = await printingService.getCategories();\n      setCategories(data);\n    } catch (err) {\n      setError('Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadProducts = async (categorySlug: string) => {\n    try {\n      setLoading(true);\n      const data = await printingService.getProducts(categorySlug);\n      setProducts(data);\n    } catch (err) {\n      setError('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCategorySelect = async (category: PrintingCategory) => {\n    setSelectedCategory(category);\n    await loadProducts(category.slug);\n    setActiveStep(1);\n  };\n\n  const handleProductSelect = (product: PrintingProduct) => {\n    setSelectedProduct(product);\n    // Reset configuration when selecting a new product\n    setQuantity(product.min_quantity || 100);\n    setSelectedOptions({});\n    setPriceCalculation(null);\n    setActiveStep(2);\n  };\n\n  // Calculate price when quantity or options change\n  const calculatePrice = async () => {\n    if (!selectedProduct) return;\n\n    // Convert quantity to number for API call\n    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n    if (isNaN(numericQuantity) || numericQuantity < (selectedProduct.min_quantity || 1)) return;\n\n    setCalculatingPrice(true);\n    try {\n      const result = await printingService.calculatePrice(\n        selectedProduct.id,\n        numericQuantity,\n        selectedOptions\n      );\n      setPriceCalculation(result);\n    } catch (err) {\n      setError('Failed to calculate price');\n      console.error('Price calculation error:', err);\n    } finally {\n      setCalculatingPrice(false);\n    }\n  };\n\n  // Effect to calculate price when quantity or options change\n  useEffect(() => {\n    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n    if (selectedProduct && !isNaN(numericQuantity) && numericQuantity >= (selectedProduct.min_quantity || 1)) {\n      const timeoutId = setTimeout(() => {\n        calculatePrice();\n      }, 300); // Debounce price calculation\n\n      return () => clearTimeout(timeoutId);\n    } else {\n      setPriceCalculation(null);\n    }\n  }, [selectedProduct, quantity, selectedOptions]);\n\n  const handleNext = async () => {\n    // If moving from configuration step, save the order item and create order\n    if (activeStep === 2 && selectedProduct && priceCalculation) {\n      const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n      const orderItem: OrderItem = {\n        product_id: selectedProduct.id,\n        quantity: numericQuantity,\n        selected_options: selectedOptions,\n        specifications: selectedProduct.specifications,\n      };\n      setOrderItems([orderItem]);\n\n      // Create the order when moving to file upload step\n      await createOrder([orderItem]);\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  const createOrder = async (items: OrderItem[]) => {\n    try {\n      setLoading(true);\n      const orderData = {\n        items,\n        special_instructions: '',\n        delivery_method: 'standard',\n      };\n\n      const order = await printingService.createOrder(orderData);\n      setCreatedOrder(order);\n    } catch (err: any) {\n      setError(err.message || 'Failed to create order');\n      console.error('Order creation error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFilesUploaded = (files: OrderFile[]) => {\n    setUploadedFiles(prev => [...prev, ...files]);\n  };\n\n  const handleFileUploadError = (error: string) => {\n    setError(error);\n  };\n\n  const handleSubmitOrder = async () => {\n    if (!createdOrder) {\n      setError('No order to submit');\n      return;\n    }\n\n    try {\n      setSubmittingOrder(true);\n      // Order is already created, just navigate to success or orders page\n      navigate('/dashboard/orders');\n    } catch (err: any) {\n      setError(err.message || 'Failed to submit order');\n    } finally {\n      setSubmittingOrder(false);\n    }\n  };\n\n  const renderStepContent = (step: number) => {\n    switch (step) {\n      case 0:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Select a Printing Category\n            </Typography>\n            <Box\n              sx={{\n                display: 'grid',\n                gridTemplateColumns: {\n                  xs: '1fr',\n                  sm: 'repeat(2, 1fr)',\n                  md: 'repeat(3, 1fr)'\n                },\n                gap: 3\n              }}\n            >\n              {categories.map((category) => (\n                <Card\n                  key={category.id}\n                  sx={{ cursor: 'pointer', '&:hover': { elevation: 4 } }}\n                  onClick={() => handleCategorySelect(category)}\n                >\n                  {category.image && (\n                    <CardMedia\n                      component=\"img\"\n                      height=\"140\"\n                      image={category.image}\n                      alt={category.name}\n                    />\n                  )}\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h6\" component=\"div\">\n                      {category.name}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {category.description}\n                    </Typography>\n                    {category.products_count !== undefined && (\n                      <Chip\n                        label={`${category.products_count} products`}\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    )}\n                  </CardContent>\n                </Card>\n              ))}\n            </Box>\n          </Box>\n        );\n\n      case 1:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Choose a Product from {selectedCategory?.name}\n            </Typography>\n            <Box\n              sx={{\n                display: 'grid',\n                gridTemplateColumns: {\n                  xs: '1fr',\n                  sm: 'repeat(2, 1fr)',\n                  md: 'repeat(3, 1fr)'\n                },\n                gap: 3\n              }}\n            >\n              {products.map((product) => (\n                <Card\n                  key={product.id}\n                  sx={{ cursor: 'pointer', '&:hover': { elevation: 4 } }}\n                  onClick={() => handleProductSelect(product)}\n                >\n                  {product.image && (\n                    <CardMedia\n                      component=\"img\"\n                      height=\"140\"\n                      image={product.image}\n                      alt={product.name}\n                    />\n                  )}\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h6\" component=\"div\">\n                      {product.name}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                      {product.description}\n                    </Typography>\n                    <Typography variant=\"h6\" color=\"primary\">\n                      {product.formatted_base_price}\n                    </Typography>\n                    <Typography variant=\"caption\" display=\"block\">\n                      Min: {product.min_quantity} | Production: {product.production_time_days} days\n                    </Typography>\n                  </CardContent>\n                </Card>\n              ))}\n            </Box>\n          </Box>\n        );\n\n      case 2:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Configure Your Order\n            </Typography>\n\n            {selectedProduct && (\n              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>\n                {/* Product Summary */}\n                <Box sx={{ flex: 1 }}>\n                  <Paper sx={{ p: 3 }}>\n                    <Typography variant=\"h6\" gutterBottom>\n                      {selectedProduct.name}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                      {selectedProduct.description}\n                    </Typography>\n\n                    {/* Specifications */}\n                    <Typography variant=\"subtitle2\" gutterBottom>\n                      Specifications:\n                    </Typography>\n                    <Box sx={{ mb: 2 }}>\n                      {Object.entries(selectedProduct.specifications || {}).map(([key, value]) => (\n                        <Typography key={key} variant=\"body2\" sx={{ mb: 0.5 }}>\n                          <strong>{key}:</strong> {value}\n                        </Typography>\n                      ))}\n                    </Box>\n\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Production Time: {selectedProduct.production_time_days} days\n                    </Typography>\n                  </Paper>\n                </Box>\n\n                {/* Configuration Form */}\n                <Box sx={{ flex: 1 }}>\n                  <Paper sx={{ p: 3 }}>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Order Configuration\n                    </Typography>\n\n                    {/* Quantity Input - Enhanced for Manual Text Entry */}\n                    <TextField\n                      label=\"Quantity\"\n                      type=\"text\"\n                      value={quantity}\n                      onChange={(e) => {\n                        const inputValue = e.target.value;\n\n                        // Allow empty input for better user experience while typing\n                        if (inputValue === '') {\n                          setQuantity('');\n                          return;\n                        }\n\n                        // Only allow numeric characters\n                        if (!/^\\d+$/.test(inputValue)) {\n                          return;\n                        }\n\n                        const numericValue = parseInt(inputValue, 10);\n                        const minQty = selectedProduct.min_quantity || 1;\n                        const maxQty = selectedProduct.max_quantity;\n\n                        // Apply validation constraints\n                        let validatedQuantity = Math.max(minQty, numericValue);\n                        if (maxQty && validatedQuantity > maxQty) {\n                          validatedQuantity = maxQty;\n                        }\n\n                        setQuantity(validatedQuantity);\n                      }}\n                      onBlur={(e) => {\n                        // Ensure we have a valid number when user leaves the field\n                        const currentValue = e.target.value;\n                        if (currentValue === '' || isNaN(parseInt(currentValue, 10))) {\n                          setQuantity(selectedProduct.min_quantity || 1);\n                        }\n                      }}\n                      onKeyDown={(e) => {\n                        // Allow navigation keys, backspace, delete, tab, escape, enter\n                        if ([\n                          'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',\n                          'Backspace', 'Delete', 'Tab', 'Escape', 'Enter',\n                          'Home', 'End'\n                        ].includes(e.key)) {\n                          return;\n                        }\n\n                        // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z\n                        if (e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) {\n                          return;\n                        }\n\n                        // Only allow numeric characters\n                        if (!/^\\d$/.test(e.key)) {\n                          e.preventDefault();\n                        }\n                      }}\n                      slotProps={{\n                        htmlInput: {\n                          inputMode: 'numeric',\n                          pattern: '[0-9]*',\n                          autoComplete: 'off',\n                          style: {\n                            fontSize: '16px', // Prevents zoom on mobile devices\n                            textAlign: 'left'\n                          }\n                        }\n                      }}\n                      helperText={`Min: ${selectedProduct.min_quantity || 1}${selectedProduct.max_quantity ? `, Max: ${selectedProduct.max_quantity}` : ''} • Type quantity directly`}\n                      fullWidth\n                      sx={{\n                        mb: 3,\n                        '& .MuiOutlinedInput-root': {\n                          '&:hover fieldset': {\n                            borderColor: 'primary.main',\n                          },\n                          '&.Mui-focused fieldset': {\n                            borderColor: 'primary.main',\n                          }\n                        }\n                      }}\n                    />\n\n                    {/* Quantity-based Pricing Tiers */}\n                    {selectedProduct.options?.quantity_pricing && (\n                      <Box sx={{ mb: 3 }}>\n                        <Typography variant=\"subtitle1\" gutterBottom>\n                          Pricing Tiers:\n                        </Typography>\n                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n                          {selectedProduct.options.quantity_pricing.map((tier: any, index: number) => {\n                            const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n                            const isCurrentTier = !isNaN(numericQuantity) && numericQuantity >= tier.min_quantity &&\n                              (index === selectedProduct.options.quantity_pricing.length - 1 ||\n                               numericQuantity < selectedProduct.options.quantity_pricing[index + 1]?.min_quantity);\n\n                            return (\n                              <Box\n                                key={index}\n                                sx={{\n                                  p: 1.5,\n                                  borderRadius: 1,\n                                  border: '1px solid',\n                                  borderColor: isCurrentTier ? 'primary.main' : 'grey.300',\n                                  backgroundColor: isCurrentTier ? 'primary.50' : 'transparent',\n                                  transition: 'all 0.2s ease-in-out'\n                                }}\n                              >\n                                <Typography\n                                  variant=\"body2\"\n                                  sx={{\n                                    fontWeight: isCurrentTier ? 600 : 400,\n                                    color: isCurrentTier ? 'primary.main' : 'text.primary'\n                                  }}\n                                >\n                                  {tier.min_quantity}+ units: RM {tier.price_per_unit.toFixed(2)} each\n                                  {isCurrentTier && (\n                                    <Chip\n                                      label=\"Current\"\n                                      size=\"small\"\n                                      color=\"primary\"\n                                      sx={{ ml: 1, height: 20 }}\n                                    />\n                                  )}\n                                </Typography>\n                              </Box>\n                            );\n                          })}\n                        </Box>\n                      </Box>\n                    )}\n\n                    <Divider sx={{ my: 2 }} />\n\n                    {/* Price Calculation */}\n                    <Box>\n                      <Typography variant=\"h6\" gutterBottom>\n                        Price Summary\n                      </Typography>\n\n\n                      {calculatingPrice ? (\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <Typography variant=\"body2\">Calculating...</Typography>\n                        </Box>\n                      ) : priceCalculation ? (\n                        <Box>\n                          <Typography variant=\"body1\">\n                            Unit Price: RM {priceCalculation.unit_price.toFixed(2)}\n                          </Typography>\n                          <Typography variant=\"body1\">\n                            Quantity: {typeof quantity === 'string' ? parseInt(quantity, 10) || 0 : quantity}\n                          </Typography>\n                          <Typography variant=\"h5\" color=\"primary\" sx={{ mt: 1 }}>\n                            Total: {priceCalculation.formatted_total_price}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1, display: 'block' }}>\n                            Estimated production time: {priceCalculation.production_time_days} days\n                          </Typography>\n                        </Box>\n                      ) : (() => {\n                          const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n                          return !isNaN(numericQuantity) && numericQuantity >= (selectedProduct?.min_quantity || 1);\n                        })() ? (\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Loading pricing...\n                        </Typography>\n                      ) : (\n                        <Typography variant=\"body2\" color=\"error\">\n                          Please enter a valid quantity (minimum: {selectedProduct?.min_quantity || 1})\n                        </Typography>\n                      )}\n                    </Box>\n                  </Paper>\n                </Box>\n              </Box>\n            )}\n          </Box>\n        );\n\n      case 3:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Upload Artwork Files\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              Upload your artwork files for printing. You can upload multiple files and specify the file type for each upload.\n            </Typography>\n\n            {createdOrder ? (\n              <ErrorBoundary\n                fallback={\n                  <Alert severity=\"error\" sx={{ mb: 2 }}>\n                    Failed to load file upload component. Please refresh the page and try again.\n                  </Alert>\n                }\n              >\n                <FileUpload\n                  orderId={createdOrder.id}\n                  onFilesUploaded={handleFilesUploaded}\n                  onError={handleFileUploadError}\n                />\n              </ErrorBoundary>\n            ) : (\n              <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                Creating order... Please wait.\n              </Alert>\n            )}\n          </Box>\n        );\n\n      case 4:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Review Your Order\n            </Typography>\n\n            {createdOrder && (\n              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>\n                {/* Order Summary */}\n                <Paper sx={{ p: 3 }}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Order Summary\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                    Order #{createdOrder.order_number}\n                  </Typography>\n\n                  {selectedProduct && (\n                    <Box sx={{ mt: 2 }}>\n                      <Typography variant=\"subtitle2\">Product:</Typography>\n                      <Typography variant=\"body2\">{selectedProduct.name}</Typography>\n\n                      <Typography variant=\"subtitle2\" sx={{ mt: 1 }}>Quantity:</Typography>\n                      <Typography variant=\"body2\">{quantity}</Typography>\n\n                      {priceCalculation && (\n                        <>\n                          <Typography variant=\"subtitle2\" sx={{ mt: 1 }}>Total Price:</Typography>\n                          <Typography variant=\"h6\" color=\"primary\">\n                            {priceCalculation.formatted_total_price}\n                          </Typography>\n                        </>\n                      )}\n\n                      <Typography variant=\"subtitle2\" sx={{ mt: 1 }}>Status:</Typography>\n                      <Chip\n                        label={createdOrder.status || 'Pending'}\n                        color=\"warning\"\n                        size=\"small\"\n                      />\n                    </Box>\n                  )}\n                </Paper>\n\n                {/* Files Summary */}\n                <Paper sx={{ p: 3 }}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Uploaded Files ({uploadedFiles.length})\n                  </Typography>\n\n                  {uploadedFiles.length > 0 ? (\n                    <Box>\n                      {uploadedFiles.map((file, index) => (\n                        <Box key={file.id} sx={{ mb: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>\n                          <Typography variant=\"body2\" noWrap>\n                            {file.original_name}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {file.formatted_file_size} • {file.file_type_label}\n                          </Typography>\n                        </Box>\n                      ))}\n                    </Box>\n                  ) : (\n                    <Alert severity=\"warning\">\n                      No files uploaded yet. You can still submit the order and upload files later.\n                    </Alert>\n                  )}\n                </Paper>\n\n                {/* Submit Button */}\n                <Box sx={{ gridColumn: '1 / -1', textAlign: 'center', mt: 2 }}>\n                  <Button\n                    variant=\"contained\"\n                    size=\"large\"\n                    onClick={handleSubmitOrder}\n                    disabled={submittingOrder}\n                  >\n                    {submittingOrder ? 'Submitting...' : 'Submit Order'}\n                  </Button>\n                </Box>\n              </Box>\n            )}\n          </Box>\n        );\n\n      default:\n        return 'Unknown step';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <Typography>Loading...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Create New Order\n      </Typography>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      <Paper sx={{ p: 3 }}>\n        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>\n          {steps.map((label) => (\n            <Step key={label}>\n              <StepLabel>{label}</StepLabel>\n            </Step>\n          ))}\n        </Stepper>\n\n        <Box sx={{ minHeight: 400 }}>\n          {renderStepContent(activeStep)}\n        </Box>\n\n        <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>\n          <Button\n            color=\"inherit\"\n            disabled={activeStep === 0}\n            onClick={handleBack}\n            sx={{ mr: 1 }}\n          >\n            Back\n          </Button>\n          <Box sx={{ flex: '1 1 auto' }} />\n          {activeStep < steps.length - 1 && (\n            <Button\n              onClick={handleNext}\n              disabled={\n                loading ||\n                (activeStep === 0 && !selectedCategory) ||\n                (activeStep === 1 && !selectedProduct) ||\n                (activeStep === 2 && (!priceCalculation || quantity < (selectedProduct?.min_quantity || 1))) ||\n                (activeStep === 3 && !createdOrder)\n              }\n            >\n              {loading ? 'Creating Order...' : 'Next'}\n            </Button>\n          )}\n          {activeStep === steps.length - 1 && (\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSubmitOrder}\n              disabled={submittingOrder || !createdOrder}\n            >\n              {submittingOrder ? 'Submitting...' : 'Complete Order'}\n            </Button>\n          )}\n        </Box>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default Order;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,SAAS,EAETC,OAAO,QAKF,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAoG,gCAAgC;AAC1J,OAAOC,UAAU,MAAM,uCAAuC;AAC9D,OAAOC,aAAa,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,KAAK,GAAG,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,cAAc,EAAE,iBAAiB,CAAC;AAEzG,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAqB,EAAE,CAAC;EACpE,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAoB,EAAE,CAAC;EAC/D,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAA0B,IAAI,CAAC;EACvF,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAyB,IAAI,CAAC;EACpF,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAkB,GAAG,CAAC;EAC9D,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAsB,CAAC,CAAC,CAAC;EAC/E,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAA0B,IAAI,CAAC;EACvF,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAuB,IAAI,CAAC;EAC5E,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAc,EAAE,CAAC;EACnE,MAAM,CAACwD,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACdyD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkB,IAAI,GAAG,MAAM1C,eAAe,CAAC2C,aAAa,CAAC,CAAC;MAClD7B,aAAa,CAAC4B,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZlB,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,YAAY,GAAG,MAAOC,YAAoB,IAAK;IACnD,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkB,IAAI,GAAG,MAAM1C,eAAe,CAAC+C,WAAW,CAACD,YAAY,CAAC;MAC5D9B,WAAW,CAAC0B,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZlB,QAAQ,CAAC,yBAAyB,CAAC;IACrC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,oBAAoB,GAAG,MAAOC,QAA0B,IAAK;IACjE/B,mBAAmB,CAAC+B,QAAQ,CAAC;IAC7B,MAAMJ,YAAY,CAACI,QAAQ,CAACC,IAAI,CAAC;IACjCtC,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,MAAMuC,mBAAmB,GAAIC,OAAwB,IAAK;IACxDhC,kBAAkB,CAACgC,OAAO,CAAC;IAC3B;IACAxB,WAAW,CAACwB,OAAO,CAACC,YAAY,IAAI,GAAG,CAAC;IACxCvB,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACtBE,mBAAmB,CAAC,IAAI,CAAC;IACzBpB,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAM0C,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACnC,eAAe,EAAE;;IAEtB;IACA,MAAMoC,eAAe,GAAG,OAAO5B,QAAQ,KAAK,QAAQ,GAAG6B,QAAQ,CAAC7B,QAAQ,EAAE,EAAE,CAAC,GAAGA,QAAQ;IACxF,IAAI8B,KAAK,CAACF,eAAe,CAAC,IAAIA,eAAe,IAAIpC,eAAe,CAACkC,YAAY,IAAI,CAAC,CAAC,EAAE;IAErFnB,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACF,MAAMwB,MAAM,GAAG,MAAM1D,eAAe,CAACsD,cAAc,CACjDnC,eAAe,CAACwC,EAAE,EAClBJ,eAAe,EACf1B,eACF,CAAC;MACDG,mBAAmB,CAAC0B,MAAM,CAAC;IAC7B,CAAC,CAAC,OAAOd,GAAG,EAAE;MACZlB,QAAQ,CAAC,2BAA2B,CAAC;MACrCkC,OAAO,CAACnC,KAAK,CAAC,0BAA0B,EAAEmB,GAAG,CAAC;IAChD,CAAC,SAAS;MACRV,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACAlD,SAAS,CAAC,MAAM;IACd,MAAMuE,eAAe,GAAG,OAAO5B,QAAQ,KAAK,QAAQ,GAAG6B,QAAQ,CAAC7B,QAAQ,EAAE,EAAE,CAAC,GAAGA,QAAQ;IACxF,IAAIR,eAAe,IAAI,CAACsC,KAAK,CAACF,eAAe,CAAC,IAAIA,eAAe,KAAKpC,eAAe,CAACkC,YAAY,IAAI,CAAC,CAAC,EAAE;MACxG,MAAMQ,SAAS,GAAGC,UAAU,CAAC,MAAM;QACjCR,cAAc,CAAC,CAAC;MAClB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;MAET,OAAO,MAAMS,YAAY,CAACF,SAAS,CAAC;IACtC,CAAC,MAAM;MACL7B,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC,EAAE,CAACb,eAAe,EAAEQ,QAAQ,EAAEE,eAAe,CAAC,CAAC;EAEhD,MAAMmC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B;IACA,IAAIrD,UAAU,KAAK,CAAC,IAAIQ,eAAe,IAAIY,gBAAgB,EAAE;MAC3D,MAAMwB,eAAe,GAAG,OAAO5B,QAAQ,KAAK,QAAQ,GAAG6B,QAAQ,CAAC7B,QAAQ,EAAE,EAAE,CAAC,GAAGA,QAAQ;MACxF,MAAMsC,SAAoB,GAAG;QAC3BC,UAAU,EAAE/C,eAAe,CAACwC,EAAE;QAC9BhC,QAAQ,EAAE4B,eAAe;QACzBY,gBAAgB,EAAEtC,eAAe;QACjCuC,cAAc,EAAEjD,eAAe,CAACiD;MAClC,CAAC;MACD9C,aAAa,CAAC,CAAC2C,SAAS,CAAC,CAAC;;MAE1B;MACA,MAAMI,WAAW,CAAC,CAACJ,SAAS,CAAC,CAAC;IAChC;IAEArD,aAAa,CAAE0D,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB3D,aAAa,CAAE0D,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;EAED,MAAMD,WAAW,GAAG,MAAOG,KAAkB,IAAK;IAChD,IAAI;MACFhD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiD,SAAS,GAAG;QAChBD,KAAK;QACLE,oBAAoB,EAAE,EAAE;QACxBC,eAAe,EAAE;MACnB,CAAC;MAED,MAAMC,KAAK,GAAG,MAAM5E,eAAe,CAACqE,WAAW,CAACI,SAAS,CAAC;MAC1DrC,eAAe,CAACwC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOhC,GAAQ,EAAE;MACjBlB,QAAQ,CAACkB,GAAG,CAACiC,OAAO,IAAI,wBAAwB,CAAC;MACjDjB,OAAO,CAACnC,KAAK,CAAC,uBAAuB,EAAEmB,GAAG,CAAC;IAC7C,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsD,mBAAmB,GAAIC,KAAkB,IAAK;IAClDzC,gBAAgB,CAAC0C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGD,KAAK,CAAC,CAAC;EAC/C,CAAC;EAED,MAAME,qBAAqB,GAAIxD,KAAa,IAAK;IAC/CC,QAAQ,CAACD,KAAK,CAAC;EACjB,CAAC;EAED,MAAMyD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC/C,YAAY,EAAE;MACjBT,QAAQ,CAAC,oBAAoB,CAAC;MAC9B;IACF;IAEA,IAAI;MACFc,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACA9B,QAAQ,CAAC,mBAAmB,CAAC;IAC/B,CAAC,CAAC,OAAOkC,GAAQ,EAAE;MACjBlB,QAAQ,CAACkB,GAAG,CAACiC,OAAO,IAAI,wBAAwB,CAAC;IACnD,CAAC,SAAS;MACRrC,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAM2C,iBAAiB,GAAIC,IAAY,IAAK;IAAA,IAAAC,qBAAA;IAC1C,QAAQD,IAAI;MACV,KAAK,CAAC;QACJ,oBACEhF,OAAA,CAACnB,GAAG;UAAAqG,QAAA,gBACFlF,OAAA,CAAClB,UAAU;YAACqG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxF,OAAA,CAACnB,GAAG;YACF4G,EAAE,EAAE;cACFC,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE;gBACnBC,EAAE,EAAE,KAAK;gBACTC,EAAE,EAAE,gBAAgB;gBACpBC,EAAE,EAAE;cACN,CAAC;cACDC,GAAG,EAAE;YACP,CAAE;YAAAb,QAAA,EAEDzE,UAAU,CAACuF,GAAG,CAAEnD,QAAQ,iBACvB7C,OAAA,CAACZ,IAAI;cAEHqG,EAAE,EAAE;gBAAEQ,MAAM,EAAE,SAAS;gBAAE,SAAS,EAAE;kBAAEC,SAAS,EAAE;gBAAE;cAAE,CAAE;cACvDC,OAAO,EAAEA,CAAA,KAAMvD,oBAAoB,CAACC,QAAQ,CAAE;cAAAqC,QAAA,GAE7CrC,QAAQ,CAACuD,KAAK,iBACbpG,OAAA,CAACV,SAAS;gBACR+G,SAAS,EAAC,KAAK;gBACfC,MAAM,EAAC,KAAK;gBACZF,KAAK,EAAEvD,QAAQ,CAACuD,KAAM;gBACtBG,GAAG,EAAE1D,QAAQ,CAAC2D;cAAK;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACF,eACDxF,OAAA,CAACX,WAAW;gBAAA6F,QAAA,gBACVlF,OAAA,CAAClB,UAAU;kBAACsG,YAAY;kBAACD,OAAO,EAAC,IAAI;kBAACkB,SAAS,EAAC,KAAK;kBAAAnB,QAAA,EAClDrC,QAAQ,CAAC2D;gBAAI;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACbxF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,OAAO;kBAACsB,KAAK,EAAC,gBAAgB;kBAAAvB,QAAA,EAC/CrC,QAAQ,CAAC6D;gBAAW;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,EACZ3C,QAAQ,CAAC8D,cAAc,KAAKC,SAAS,iBACpC5G,OAAA,CAACT,IAAI;kBACHsH,KAAK,EAAE,GAAGhE,QAAQ,CAAC8D,cAAc,WAAY;kBAC7CG,IAAI,EAAC,OAAO;kBACZrB,EAAE,EAAE;oBAAEsB,EAAE,EAAE;kBAAE;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA,GA1BT3C,QAAQ,CAACU,EAAE;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2BZ,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACExF,OAAA,CAACnB,GAAG;UAAAqG,QAAA,gBACFlF,OAAA,CAAClB,UAAU;YAACqG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,GAAC,wBACd,EAACrE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2F,IAAI;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACbxF,OAAA,CAACnB,GAAG;YACF4G,EAAE,EAAE;cACFC,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE;gBACnBC,EAAE,EAAE,KAAK;gBACTC,EAAE,EAAE,gBAAgB;gBACpBC,EAAE,EAAE;cACN,CAAC;cACDC,GAAG,EAAE;YACP,CAAE;YAAAb,QAAA,EAEDvE,QAAQ,CAACqF,GAAG,CAAEhD,OAAO,iBACpBhD,OAAA,CAACZ,IAAI;cAEHqG,EAAE,EAAE;gBAAEQ,MAAM,EAAE,SAAS;gBAAE,SAAS,EAAE;kBAAEC,SAAS,EAAE;gBAAE;cAAE,CAAE;cACvDC,OAAO,EAAEA,CAAA,KAAMpD,mBAAmB,CAACC,OAAO,CAAE;cAAAkC,QAAA,GAE3ClC,OAAO,CAACoD,KAAK,iBACZpG,OAAA,CAACV,SAAS;gBACR+G,SAAS,EAAC,KAAK;gBACfC,MAAM,EAAC,KAAK;gBACZF,KAAK,EAAEpD,OAAO,CAACoD,KAAM;gBACrBG,GAAG,EAAEvD,OAAO,CAACwD;cAAK;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CACF,eACDxF,OAAA,CAACX,WAAW;gBAAA6F,QAAA,gBACVlF,OAAA,CAAClB,UAAU;kBAACsG,YAAY;kBAACD,OAAO,EAAC,IAAI;kBAACkB,SAAS,EAAC,KAAK;kBAAAnB,QAAA,EAClDlC,OAAO,CAACwD;gBAAI;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACbxF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,OAAO;kBAACsB,KAAK,EAAC,gBAAgB;kBAAChB,EAAE,EAAE;oBAAEuB,EAAE,EAAE;kBAAE,CAAE;kBAAA9B,QAAA,EAC9DlC,OAAO,CAAC0D;gBAAW;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACbxF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,IAAI;kBAACsB,KAAK,EAAC,SAAS;kBAAAvB,QAAA,EACrClC,OAAO,CAACiE;gBAAoB;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACbxF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,SAAS;kBAACO,OAAO,EAAC,OAAO;kBAAAR,QAAA,GAAC,OACvC,EAAClC,OAAO,CAACC,YAAY,EAAC,iBAAe,EAACD,OAAO,CAACkE,oBAAoB,EAAC,OAC1E;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,GAzBTxC,OAAO,CAACO,EAAE;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BX,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACExF,OAAA,CAACnB,GAAG;UAAAqG,QAAA,gBACFlF,OAAA,CAAClB,UAAU;YAACqG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZzE,eAAe,iBACdf,OAAA,CAACnB,GAAG;YAAC4G,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEyB,aAAa,EAAE;gBAAEvB,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAC;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAE/ElF,OAAA,CAACnB,GAAG;cAAC4G,EAAE,EAAE;gBAAE2B,IAAI,EAAE;cAAE,CAAE;cAAAlC,QAAA,eACnBlF,OAAA,CAACb,KAAK;gBAACsG,EAAE,EAAE;kBAAE4B,CAAC,EAAE;gBAAE,CAAE;gBAAAnC,QAAA,gBAClBlF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAF,QAAA,EAClCnE,eAAe,CAACyF;gBAAI;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACbxF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,OAAO;kBAACsB,KAAK,EAAC,gBAAgB;kBAAChB,EAAE,EAAE;oBAAEuB,EAAE,EAAE;kBAAE,CAAE;kBAAA9B,QAAA,EAC9DnE,eAAe,CAAC2F;gBAAW;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAGbxF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAAAF,QAAA,EAAC;gBAE7C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbxF,OAAA,CAACnB,GAAG;kBAAC4G,EAAE,EAAE;oBAAEuB,EAAE,EAAE;kBAAE,CAAE;kBAAA9B,QAAA,EAChBoC,MAAM,CAACC,OAAO,CAACxG,eAAe,CAACiD,cAAc,IAAI,CAAC,CAAC,CAAC,CAACgC,GAAG,CAAC,CAAC,CAACwB,GAAG,EAAEC,KAAK,CAAC,kBACrEzH,OAAA,CAAClB,UAAU;oBAAWqG,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEuB,EAAE,EAAE;oBAAI,CAAE;oBAAA9B,QAAA,gBACpDlF,OAAA;sBAAAkF,QAAA,GAASsC,GAAG,EAAC,GAAC;oBAAA;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACiC,KAAK;kBAAA,GADfD,GAAG;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAER,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENxF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,OAAO;kBAACsB,KAAK,EAAC,gBAAgB;kBAAAvB,QAAA,GAAC,mBAChC,EAACnE,eAAe,CAACmG,oBAAoB,EAAC,OACzD;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNxF,OAAA,CAACnB,GAAG;cAAC4G,EAAE,EAAE;gBAAE2B,IAAI,EAAE;cAAE,CAAE;cAAAlC,QAAA,eACnBlF,OAAA,CAACb,KAAK;gBAACsG,EAAE,EAAE;kBAAE4B,CAAC,EAAE;gBAAE,CAAE;gBAAAnC,QAAA,gBAClBlF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAF,QAAA,EAAC;gBAEtC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAGbxF,OAAA,CAACP,SAAS;kBACRoH,KAAK,EAAC,UAAU;kBAChBa,IAAI,EAAC,MAAM;kBACXD,KAAK,EAAElG,QAAS;kBAChBoG,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMC,UAAU,GAAGD,CAAC,CAACE,MAAM,CAACL,KAAK;;oBAEjC;oBACA,IAAII,UAAU,KAAK,EAAE,EAAE;sBACrBrG,WAAW,CAAC,EAAE,CAAC;sBACf;oBACF;;oBAEA;oBACA,IAAI,CAAC,OAAO,CAACuG,IAAI,CAACF,UAAU,CAAC,EAAE;sBAC7B;oBACF;oBAEA,MAAMG,YAAY,GAAG5E,QAAQ,CAACyE,UAAU,EAAE,EAAE,CAAC;oBAC7C,MAAMI,MAAM,GAAGlH,eAAe,CAACkC,YAAY,IAAI,CAAC;oBAChD,MAAMiF,MAAM,GAAGnH,eAAe,CAACoH,YAAY;;oBAE3C;oBACA,IAAIC,iBAAiB,GAAGC,IAAI,CAACC,GAAG,CAACL,MAAM,EAAED,YAAY,CAAC;oBACtD,IAAIE,MAAM,IAAIE,iBAAiB,GAAGF,MAAM,EAAE;sBACxCE,iBAAiB,GAAGF,MAAM;oBAC5B;oBAEA1G,WAAW,CAAC4G,iBAAiB,CAAC;kBAChC,CAAE;kBACFG,MAAM,EAAGX,CAAC,IAAK;oBACb;oBACA,MAAMY,YAAY,GAAGZ,CAAC,CAACE,MAAM,CAACL,KAAK;oBACnC,IAAIe,YAAY,KAAK,EAAE,IAAInF,KAAK,CAACD,QAAQ,CAACoF,YAAY,EAAE,EAAE,CAAC,CAAC,EAAE;sBAC5DhH,WAAW,CAACT,eAAe,CAACkC,YAAY,IAAI,CAAC,CAAC;oBAChD;kBACF,CAAE;kBACFwF,SAAS,EAAGb,CAAC,IAAK;oBAChB;oBACA,IAAI,CACF,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EACjD,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAC/C,MAAM,EAAE,KAAK,CACd,CAACc,QAAQ,CAACd,CAAC,CAACJ,GAAG,CAAC,EAAE;sBACjB;oBACF;;oBAEA;oBACA,IAAII,CAAC,CAACe,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACD,QAAQ,CAACd,CAAC,CAACJ,GAAG,CAACoB,WAAW,CAAC,CAAC,CAAC,EAAE;sBACxE;oBACF;;oBAEA;oBACA,IAAI,CAAC,MAAM,CAACb,IAAI,CAACH,CAAC,CAACJ,GAAG,CAAC,EAAE;sBACvBI,CAAC,CAACiB,cAAc,CAAC,CAAC;oBACpB;kBACF,CAAE;kBACFC,SAAS,EAAE;oBACTC,SAAS,EAAE;sBACTC,SAAS,EAAE,SAAS;sBACpBC,OAAO,EAAE,QAAQ;sBACjBC,YAAY,EAAE,KAAK;sBACnBC,KAAK,EAAE;wBACLC,QAAQ,EAAE,MAAM;wBAAE;wBAClBC,SAAS,EAAE;sBACb;oBACF;kBACF,CAAE;kBACFC,UAAU,EAAE,QAAQvI,eAAe,CAACkC,YAAY,IAAI,CAAC,GAAGlC,eAAe,CAACoH,YAAY,GAAG,UAAUpH,eAAe,CAACoH,YAAY,EAAE,GAAG,EAAE,2BAA4B;kBAChKoB,SAAS;kBACT9D,EAAE,EAAE;oBACFuB,EAAE,EAAE,CAAC;oBACL,0BAA0B,EAAE;sBAC1B,kBAAkB,EAAE;wBAClBwC,WAAW,EAAE;sBACf,CAAC;sBACD,wBAAwB,EAAE;wBACxBA,WAAW,EAAE;sBACf;oBACF;kBACF;gBAAE;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGD,EAAAP,qBAAA,GAAAlE,eAAe,CAAC0I,OAAO,cAAAxE,qBAAA,uBAAvBA,qBAAA,CAAyByE,gBAAgB,kBACxC1J,OAAA,CAACnB,GAAG;kBAAC4G,EAAE,EAAE;oBAAEuB,EAAE,EAAE;kBAAE,CAAE;kBAAA9B,QAAA,gBACjBlF,OAAA,CAAClB,UAAU;oBAACqG,OAAO,EAAC,WAAW;oBAACC,YAAY;oBAAAF,QAAA,EAAC;kBAE7C;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbxF,OAAA,CAACnB,GAAG;oBAAC4G,EAAE,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEyB,aAAa,EAAE,QAAQ;sBAAEpB,GAAG,EAAE;oBAAE,CAAE;oBAAAb,QAAA,EAC3DnE,eAAe,CAAC0I,OAAO,CAACC,gBAAgB,CAAC1D,GAAG,CAAC,CAAC2D,IAAS,EAAEC,KAAa,KAAK;sBAAA,IAAAC,sBAAA;sBAC1E,MAAM1G,eAAe,GAAG,OAAO5B,QAAQ,KAAK,QAAQ,GAAG6B,QAAQ,CAAC7B,QAAQ,EAAE,EAAE,CAAC,GAAGA,QAAQ;sBACxF,MAAMuI,aAAa,GAAG,CAACzG,KAAK,CAACF,eAAe,CAAC,IAAIA,eAAe,IAAIwG,IAAI,CAAC1G,YAAY,KAClF2G,KAAK,KAAK7I,eAAe,CAAC0I,OAAO,CAACC,gBAAgB,CAACK,MAAM,GAAG,CAAC,IAC7D5G,eAAe,KAAA0G,sBAAA,GAAG9I,eAAe,CAAC0I,OAAO,CAACC,gBAAgB,CAACE,KAAK,GAAG,CAAC,CAAC,cAAAC,sBAAA,uBAAnDA,sBAAA,CAAqD5G,YAAY,EAAC;sBAEvF,oBACEjD,OAAA,CAACnB,GAAG;wBAEF4G,EAAE,EAAE;0BACF4B,CAAC,EAAE,GAAG;0BACN2C,YAAY,EAAE,CAAC;0BACfC,MAAM,EAAE,WAAW;0BACnBT,WAAW,EAAEM,aAAa,GAAG,cAAc,GAAG,UAAU;0BACxDI,eAAe,EAAEJ,aAAa,GAAG,YAAY,GAAG,aAAa;0BAC7DK,UAAU,EAAE;wBACd,CAAE;wBAAAjF,QAAA,eAEFlF,OAAA,CAAClB,UAAU;0BACTqG,OAAO,EAAC,OAAO;0BACfM,EAAE,EAAE;4BACF2E,UAAU,EAAEN,aAAa,GAAG,GAAG,GAAG,GAAG;4BACrCrD,KAAK,EAAEqD,aAAa,GAAG,cAAc,GAAG;0BAC1C,CAAE;0BAAA5E,QAAA,GAEDyE,IAAI,CAAC1G,YAAY,EAAC,cAAY,EAAC0G,IAAI,CAACU,cAAc,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,OAC/D,EAACR,aAAa,iBACZ9J,OAAA,CAACT,IAAI;4BACHsH,KAAK,EAAC,SAAS;4BACfC,IAAI,EAAC,OAAO;4BACZL,KAAK,EAAC,SAAS;4BACfhB,EAAE,EAAE;8BAAE8E,EAAE,EAAE,CAAC;8BAAEjE,MAAM,EAAE;4BAAG;0BAAE;4BAAAjB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3B,CACF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACS;sBAAC,GA1BRoE,KAAK;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2BP,CAAC;oBAEV,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAEDxF,OAAA,CAACN,OAAO;kBAAC+F,EAAE,EAAE;oBAAE+E,EAAE,EAAE;kBAAE;gBAAE;kBAAAnF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAG1BxF,OAAA,CAACnB,GAAG;kBAAAqG,QAAA,gBACFlF,OAAA,CAAClB,UAAU;oBAACqG,OAAO,EAAC,IAAI;oBAACC,YAAY;oBAAAF,QAAA,EAAC;kBAEtC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EAGZ3D,gBAAgB,gBACf7B,OAAA,CAACnB,GAAG;oBAAC4G,EAAE,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAE+E,UAAU,EAAE,QAAQ;sBAAE1E,GAAG,EAAE;oBAAE,CAAE;oBAAAb,QAAA,eACzDlF,OAAA,CAAClB,UAAU;sBAACqG,OAAO,EAAC,OAAO;sBAAAD,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,GACJ7D,gBAAgB,gBAClB3B,OAAA,CAACnB,GAAG;oBAAAqG,QAAA,gBACFlF,OAAA,CAAClB,UAAU;sBAACqG,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAC,iBACX,EAACvD,gBAAgB,CAAC+I,UAAU,CAACJ,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACbxF,OAAA,CAAClB,UAAU;sBAACqG,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAC,YAChB,EAAC,OAAO3D,QAAQ,KAAK,QAAQ,GAAG6B,QAAQ,CAAC7B,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,GAAGA,QAAQ;oBAAA;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC,eACbxF,OAAA,CAAClB,UAAU;sBAACqG,OAAO,EAAC,IAAI;sBAACsB,KAAK,EAAC,SAAS;sBAAChB,EAAE,EAAE;wBAAEsB,EAAE,EAAE;sBAAE,CAAE;sBAAA7B,QAAA,GAAC,SAC/C,EAACvD,gBAAgB,CAACgJ,qBAAqB;oBAAA;sBAAAtF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACbxF,OAAA,CAAClB,UAAU;sBAACqG,OAAO,EAAC,SAAS;sBAACsB,KAAK,EAAC,gBAAgB;sBAAChB,EAAE,EAAE;wBAAEsB,EAAE,EAAE,CAAC;wBAAErB,OAAO,EAAE;sBAAQ,CAAE;sBAAAR,QAAA,GAAC,6BACzD,EAACvD,gBAAgB,CAACuF,oBAAoB,EAAC,OACpE;oBAAA;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,GACJ,CAAC,MAAM;oBACP,MAAMrC,eAAe,GAAG,OAAO5B,QAAQ,KAAK,QAAQ,GAAG6B,QAAQ,CAAC7B,QAAQ,EAAE,EAAE,CAAC,GAAGA,QAAQ;oBACxF,OAAO,CAAC8B,KAAK,CAACF,eAAe,CAAC,IAAIA,eAAe,KAAK,CAAApC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,YAAY,KAAI,CAAC,CAAC;kBAC3F,CAAC,EAAE,CAAC,gBACJjD,OAAA,CAAClB,UAAU;oBAACqG,OAAO,EAAC,OAAO;oBAACsB,KAAK,EAAC,gBAAgB;oBAAAvB,QAAA,EAAC;kBAEnD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,gBAEbxF,OAAA,CAAClB,UAAU;oBAACqG,OAAO,EAAC,OAAO;oBAACsB,KAAK,EAAC,OAAO;oBAAAvB,QAAA,GAAC,0CACA,EAAC,CAAAnE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,YAAY,KAAI,CAAC,EAAC,GAC9E;kBAAA;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACExF,OAAA,CAACnB,GAAG;UAAAqG,QAAA,gBACFlF,OAAA,CAAClB,UAAU;YAACqG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxF,OAAA,CAAClB,UAAU;YAACqG,OAAO,EAAC,OAAO;YAACsB,KAAK,EAAC,gBAAgB;YAAChB,EAAE,EAAE;cAAEuB,EAAE,EAAE;YAAE,CAAE;YAAA9B,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZzD,YAAY,gBACX/B,OAAA,CAACF,aAAa;YACZ8K,QAAQ,eACN5K,OAAA,CAACR,KAAK;cAACqL,QAAQ,EAAC,OAAO;cAACpF,EAAE,EAAE;gBAAEuB,EAAE,EAAE;cAAE,CAAE;cAAA9B,QAAA,EAAC;YAEvC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;YAAAN,QAAA,eAEDlF,OAAA,CAACH,UAAU;cACTiL,OAAO,EAAE/I,YAAY,CAACwB,EAAG;cACzBwH,eAAe,EAAErG,mBAAoB;cACrCsG,OAAO,EAAEnG;YAAsB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC,gBAEhBxF,OAAA,CAACR,KAAK;YAACqL,QAAQ,EAAC,SAAS;YAACpF,EAAE,EAAE;cAAEuB,EAAE,EAAE;YAAE,CAAE;YAAA9B,QAAA,EAAC;UAEzC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACExF,OAAA,CAACnB,GAAG;UAAAqG,QAAA,gBACFlF,OAAA,CAAClB,UAAU;YAACqG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZzD,YAAY,iBACX/B,OAAA,CAACnB,GAAG;YAAC4G,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,mBAAmB,EAAE;gBAAEC,EAAE,EAAE,KAAK;gBAAEE,EAAE,EAAE;cAAU,CAAC;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAEtFlF,OAAA,CAACb,KAAK;cAACsG,EAAE,EAAE;gBAAE4B,CAAC,EAAE;cAAE,CAAE;cAAAnC,QAAA,gBAClBlF,OAAA,CAAClB,UAAU;gBAACqG,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAF,QAAA,EAAC;cAEtC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbxF,OAAA,CAAClB,UAAU;gBAACqG,OAAO,EAAC,OAAO;gBAACsB,KAAK,EAAC,gBAAgB;gBAACrB,YAAY;gBAAAF,QAAA,GAAC,SACvD,EAACnD,YAAY,CAACkJ,YAAY;cAAA;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,EAEZzE,eAAe,iBACdf,OAAA,CAACnB,GAAG;gBAAC4G,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAA7B,QAAA,gBACjBlF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,WAAW;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrDxF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAEnE,eAAe,CAACyF;gBAAI;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAE/DxF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,WAAW;kBAACM,EAAE,EAAE;oBAAEsB,EAAE,EAAE;kBAAE,CAAE;kBAAA7B,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrExF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAE3D;gBAAQ;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,EAElD7D,gBAAgB,iBACf3B,OAAA,CAAAE,SAAA;kBAAAgF,QAAA,gBACElF,OAAA,CAAClB,UAAU;oBAACqG,OAAO,EAAC,WAAW;oBAACM,EAAE,EAAE;sBAAEsB,EAAE,EAAE;oBAAE,CAAE;oBAAA7B,QAAA,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxExF,OAAA,CAAClB,UAAU;oBAACqG,OAAO,EAAC,IAAI;oBAACsB,KAAK,EAAC,SAAS;oBAAAvB,QAAA,EACrCvD,gBAAgB,CAACgJ;kBAAqB;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA,eACb,CACH,eAEDxF,OAAA,CAAClB,UAAU;kBAACqG,OAAO,EAAC,WAAW;kBAACM,EAAE,EAAE;oBAAEsB,EAAE,EAAE;kBAAE,CAAE;kBAAA7B,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnExF,OAAA,CAACT,IAAI;kBACHsH,KAAK,EAAE9E,YAAY,CAACmJ,MAAM,IAAI,SAAU;kBACxCzE,KAAK,EAAC,SAAS;kBACfK,IAAI,EAAC;gBAAO;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGRxF,OAAA,CAACb,KAAK;cAACsG,EAAE,EAAE;gBAAE4B,CAAC,EAAE;cAAE,CAAE;cAAAnC,QAAA,gBAClBlF,OAAA,CAAClB,UAAU;gBAACqG,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAF,QAAA,GAAC,kBACpB,EAACjD,aAAa,CAAC8H,MAAM,EAAC,GACxC;cAAA;gBAAA1E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEZvD,aAAa,CAAC8H,MAAM,GAAG,CAAC,gBACvB/J,OAAA,CAACnB,GAAG;gBAAAqG,QAAA,EACDjD,aAAa,CAAC+D,GAAG,CAAC,CAACmF,IAAI,EAAEvB,KAAK,kBAC7B5J,OAAA,CAACnB,GAAG;kBAAe4G,EAAE,EAAE;oBAAEuB,EAAE,EAAE,CAAC;oBAAEK,CAAC,EAAE,CAAC;oBAAE+D,OAAO,EAAE,SAAS;oBAAEpB,YAAY,EAAE;kBAAE,CAAE;kBAAA9E,QAAA,gBAC1ElF,OAAA,CAAClB,UAAU;oBAACqG,OAAO,EAAC,OAAO;oBAACkG,MAAM;oBAAAnG,QAAA,EAC/BiG,IAAI,CAACG;kBAAa;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACbxF,OAAA,CAAClB,UAAU;oBAACqG,OAAO,EAAC,SAAS;oBAACsB,KAAK,EAAC,gBAAgB;oBAAAvB,QAAA,GACjDiG,IAAI,CAACI,mBAAmB,EAAC,UAAG,EAACJ,IAAI,CAACK,eAAe;kBAAA;oBAAAnG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA,GANL2F,IAAI,CAAC5H,EAAE;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOZ,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENxF,OAAA,CAACR,KAAK;gBAACqL,QAAQ,EAAC,SAAS;gBAAA3F,QAAA,EAAC;cAE1B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGRxF,OAAA,CAACnB,GAAG;cAAC4G,EAAE,EAAE;gBAAEgG,UAAU,EAAE,QAAQ;gBAAEpC,SAAS,EAAE,QAAQ;gBAAEtC,EAAE,EAAE;cAAE,CAAE;cAAA7B,QAAA,eAC5DlF,OAAA,CAACd,MAAM;gBACLiG,OAAO,EAAC,WAAW;gBACnB2B,IAAI,EAAC,OAAO;gBACZX,OAAO,EAAErB,iBAAkB;gBAC3B4G,QAAQ,EAAEvJ,eAAgB;gBAAA+C,QAAA,EAEzB/C,eAAe,GAAG,eAAe,GAAG;cAAc;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV;QACE,OAAO,cAAc;IACzB;EACF,CAAC;EAED,IAAIrE,OAAO,EAAE;IACX,oBACEnB,OAAA,CAACnB,GAAG;MAAC6G,OAAO,EAAC,MAAM;MAACiG,cAAc,EAAC,QAAQ;MAAClB,UAAU,EAAC,QAAQ;MAACmB,SAAS,EAAC,OAAO;MAAA1G,QAAA,eAC/ElF,OAAA,CAAClB,UAAU;QAAAoG,QAAA,EAAC;MAAU;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAEV;EAEA,oBACExF,OAAA,CAACnB,GAAG;IAAAqG,QAAA,gBACFlF,OAAA,CAAClB,UAAU;MAACqG,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZnE,KAAK,iBACJrB,OAAA,CAACR,KAAK;MAACqL,QAAQ,EAAC,OAAO;MAACpF,EAAE,EAAE;QAAEuB,EAAE,EAAE;MAAE,CAAE;MAAA9B,QAAA,EACnC7D;IAAK;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAEDxF,OAAA,CAACb,KAAK;MAACsG,EAAE,EAAE;QAAE4B,CAAC,EAAE;MAAE,CAAE;MAAAnC,QAAA,gBAClBlF,OAAA,CAACjB,OAAO;QAACwB,UAAU,EAAEA,UAAW;QAACkF,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE,CAAE;QAAA9B,QAAA,EAC5C/E,KAAK,CAAC6F,GAAG,CAAEa,KAAK,iBACf7G,OAAA,CAAChB,IAAI;UAAAkG,QAAA,eACHlF,OAAA,CAACf,SAAS;YAAAiG,QAAA,EAAE2B;UAAK;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC,GADrBqB,KAAK;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEVxF,OAAA,CAACnB,GAAG;QAAC4G,EAAE,EAAE;UAAEmG,SAAS,EAAE;QAAI,CAAE;QAAA1G,QAAA,EACzBH,iBAAiB,CAACxE,UAAU;MAAC;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eAENxF,OAAA,CAACnB,GAAG;QAAC4G,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEyB,aAAa,EAAE,KAAK;UAAE0E,EAAE,EAAE;QAAE,CAAE;QAAA3G,QAAA,gBACxDlF,OAAA,CAACd,MAAM;UACLuH,KAAK,EAAC,SAAS;UACfiF,QAAQ,EAAEnL,UAAU,KAAK,CAAE;UAC3B4F,OAAO,EAAEhC,UAAW;UACpBsB,EAAE,EAAE;YAAEqG,EAAE,EAAE;UAAE,CAAE;UAAA5G,QAAA,EACf;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxF,OAAA,CAACnB,GAAG;UAAC4G,EAAE,EAAE;YAAE2B,IAAI,EAAE;UAAW;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAChCjF,UAAU,GAAGJ,KAAK,CAAC4J,MAAM,GAAG,CAAC,iBAC5B/J,OAAA,CAACd,MAAM;UACLiH,OAAO,EAAEvC,UAAW;UACpB8H,QAAQ,EACNvK,OAAO,IACNZ,UAAU,KAAK,CAAC,IAAI,CAACM,gBAAiB,IACtCN,UAAU,KAAK,CAAC,IAAI,CAACQ,eAAgB,IACrCR,UAAU,KAAK,CAAC,KAAK,CAACoB,gBAAgB,IAAIJ,QAAQ,IAAI,CAAAR,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,YAAY,KAAI,CAAC,CAAC,CAAE,IAC3F1C,UAAU,KAAK,CAAC,IAAI,CAACwB,YACvB;UAAAmD,QAAA,EAEA/D,OAAO,GAAG,mBAAmB,GAAG;QAAM;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CACT,EACAjF,UAAU,KAAKJ,KAAK,CAAC4J,MAAM,GAAG,CAAC,iBAC9B/J,OAAA,CAACd,MAAM;UACLiG,OAAO,EAAC,WAAW;UACnBsB,KAAK,EAAC,SAAS;UACfN,OAAO,EAAErB,iBAAkB;UAC3B4G,QAAQ,EAAEvJ,eAAe,IAAI,CAACJ,YAAa;UAAAmD,QAAA,EAE1C/C,eAAe,GAAG,eAAe,GAAG;QAAgB;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACnF,EAAA,CAvrBID,KAAe;EAAA,QACFT,WAAW;AAAA;AAAAoM,EAAA,GADxB3L,KAAe;AAyrBrB,eAAeA,KAAK;AAAC,IAAA2L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}