{"ast": null, "code": "import{createTheme}from'@mui/material/styles';// Material Dashboard React theme configuration\nconst materialDashboardTheme=createTheme({palette:{primary:{main:'#1976d2',light:'#42a5f5',dark:'#1565c0',contrastText:'#ffffff'},secondary:{main:'#dc004e',light:'#ff5983',dark:'#9a0036',contrastText:'#ffffff'},background:{default:'#f5f5f5',paper:'#ffffff'},text:{primary:'#333333',secondary:'#666666'},error:{main:'#f44336'},warning:{main:'#ff9800'},info:{main:'#2196f3'},success:{main:'#4caf50'}},typography:{fontFamily:'\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',h1:{fontSize:'2.5rem',fontWeight:300,lineHeight:1.2},h2:{fontSize:'2rem',fontWeight:300,lineHeight:1.2},h3:{fontSize:'1.75rem',fontWeight:400,lineHeight:1.2},h4:{fontSize:'1.5rem',fontWeight:400,lineHeight:1.2},h5:{fontSize:'1.25rem',fontWeight:400,lineHeight:1.2},h6:{fontSize:'1rem',fontWeight:500,lineHeight:1.2},body1:{fontSize:'1rem',lineHeight:1.5},body2:{fontSize:'0.875rem',lineHeight:1.43}},shape:{borderRadius:8},spacing:8,components:{MuiButton:{styleOverrides:{root:{textTransform:'none',borderRadius:8,padding:'8px 16px'},contained:{boxShadow:'0 2px 4px rgba(0,0,0,0.1)','&:hover':{boxShadow:'0 4px 8px rgba(0,0,0,0.15)'}}}},MuiCard:{styleOverrides:{root:{boxShadow:'0 2px 8px rgba(0,0,0,0.1)',borderRadius:12}}},MuiPaper:{styleOverrides:{root:{boxShadow:'0 2px 8px rgba(0,0,0,0.1)'}}},MuiDrawer:{styleOverrides:{paper:{backgroundColor:'#ffffff',borderRight:'1px solid #e0e0e0'}}},MuiAppBar:{styleOverrides:{root:{backgroundColor:'#ffffff',color:'#333333',boxShadow:'0 2px 4px rgba(0,0,0,0.1)'}}},MuiListItemButton:{styleOverrides:{root:{borderRadius:8,margin:'4px 8px','&.Mui-selected':{backgroundColor:'#e3f2fd','&:hover':{backgroundColor:'#bbdefb'}}}}}}});export default materialDashboardTheme;", "map": {"version": 3, "names": ["createTheme", "materialDashboardTheme", "palette", "primary", "main", "light", "dark", "contrastText", "secondary", "background", "default", "paper", "text", "error", "warning", "info", "success", "typography", "fontFamily", "h1", "fontSize", "fontWeight", "lineHeight", "h2", "h3", "h4", "h5", "h6", "body1", "body2", "shape", "borderRadius", "spacing", "components", "MuiB<PERSON>on", "styleOverrides", "root", "textTransform", "padding", "contained", "boxShadow", "MuiCard", "MuiPaper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "borderRight", "MuiAppBar", "color", "MuiListItemButton", "margin"], "sources": ["C:/laragon/www/frontend/src/theme/materialDashboardTheme.ts"], "sourcesContent": ["import { createTheme } from '@mui/material/styles';\n\n// Material Dashboard React theme configuration\nconst materialDashboardTheme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0',\n      contrastText: '#ffffff',\n    },\n    secondary: {\n      main: '#dc004e',\n      light: '#ff5983',\n      dark: '#9a0036',\n      contrastText: '#ffffff',\n    },\n    background: {\n      default: '#f5f5f5',\n      paper: '#ffffff',\n    },\n    text: {\n      primary: '#333333',\n      secondary: '#666666',\n    },\n    error: {\n      main: '#f44336',\n    },\n    warning: {\n      main: '#ff9800',\n    },\n    info: {\n      main: '#2196f3',\n    },\n    success: {\n      main: '#4caf50',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontSize: '2.5rem',\n      fontWeight: 300,\n      lineHeight: 1.2,\n    },\n    h2: {\n      fontSize: '2rem',\n      fontWeight: 300,\n      lineHeight: 1.2,\n    },\n    h3: {\n      fontSize: '1.75rem',\n      fontWeight: 400,\n      lineHeight: 1.2,\n    },\n    h4: {\n      fontSize: '1.5rem',\n      fontWeight: 400,\n      lineHeight: 1.2,\n    },\n    h5: {\n      fontSize: '1.25rem',\n      fontWeight: 400,\n      lineHeight: 1.2,\n    },\n    h6: {\n      fontSize: '1rem',\n      fontWeight: 500,\n      lineHeight: 1.2,\n    },\n    body1: {\n      fontSize: '1rem',\n      lineHeight: 1.5,\n    },\n    body2: {\n      fontSize: '0.875rem',\n      lineHeight: 1.43,\n    },\n  },\n  shape: {\n    borderRadius: 8,\n  },\n  spacing: 8,\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          borderRadius: 8,\n          padding: '8px 16px',\n        },\n        contained: {\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n          '&:hover': {\n            boxShadow: '0 4px 8px rgba(0,0,0,0.15)',\n          },\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          borderRadius: 12,\n        },\n      },\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n        },\n      },\n    },\n    MuiDrawer: {\n      styleOverrides: {\n        paper: {\n          backgroundColor: '#ffffff',\n          borderRight: '1px solid #e0e0e0',\n        },\n      },\n    },\n    MuiAppBar: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#ffffff',\n          color: '#333333',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n        },\n      },\n    },\n    MuiListItemButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: 8,\n          margin: '4px 8px',\n          '&.Mui-selected': {\n            backgroundColor: '#e3f2fd',\n            '&:hover': {\n              backgroundColor: '#bbdefb',\n            },\n          },\n        },\n      },\n    },\n  },\n});\n\nexport default materialDashboardTheme;\n"], "mappings": "AAAA,OAASA,WAAW,KAAQ,sBAAsB,CAElD;AACA,KAAM,CAAAC,sBAAsB,CAAGD,WAAW,CAAC,CACzCE,OAAO,CAAE,CACPC,OAAO,CAAE,CACPC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SAAS,CACfC,YAAY,CAAE,SAChB,CAAC,CACDC,SAAS,CAAE,CACTJ,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SAAS,CACfC,YAAY,CAAE,SAChB,CAAC,CACDE,UAAU,CAAE,CACVC,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,SACT,CAAC,CACDC,IAAI,CAAE,CACJT,OAAO,CAAE,SAAS,CAClBK,SAAS,CAAE,SACb,CAAC,CACDK,KAAK,CAAE,CACLT,IAAI,CAAE,SACR,CAAC,CACDU,OAAO,CAAE,CACPV,IAAI,CAAE,SACR,CAAC,CACDW,IAAI,CAAE,CACJX,IAAI,CAAE,SACR,CAAC,CACDY,OAAO,CAAE,CACPZ,IAAI,CAAE,SACR,CACF,CAAC,CACDa,UAAU,CAAE,CACVC,UAAU,CAAE,4CAA4C,CACxDC,EAAE,CAAE,CACFC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE,GACd,CAAC,CACDC,EAAE,CAAE,CACFH,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE,GACd,CAAC,CACDE,EAAE,CAAE,CACFJ,QAAQ,CAAE,SAAS,CACnBC,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE,GACd,CAAC,CACDG,EAAE,CAAE,CACFL,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE,GACd,CAAC,CACDI,EAAE,CAAE,CACFN,QAAQ,CAAE,SAAS,CACnBC,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE,GACd,CAAC,CACDK,EAAE,CAAE,CACFP,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE,GACd,CAAC,CACDM,KAAK,CAAE,CACLR,QAAQ,CAAE,MAAM,CAChBE,UAAU,CAAE,GACd,CAAC,CACDO,KAAK,CAAE,CACLT,QAAQ,CAAE,UAAU,CACpBE,UAAU,CAAE,IACd,CACF,CAAC,CACDQ,KAAK,CAAE,CACLC,YAAY,CAAE,CAChB,CAAC,CACDC,OAAO,CAAE,CAAC,CACVC,UAAU,CAAE,CACVC,SAAS,CAAE,CACTC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJC,aAAa,CAAE,MAAM,CACrBN,YAAY,CAAE,CAAC,CACfO,OAAO,CAAE,UACX,CAAC,CACDC,SAAS,CAAE,CACTC,SAAS,CAAE,2BAA2B,CACtC,SAAS,CAAE,CACTA,SAAS,CAAE,4BACb,CACF,CACF,CACF,CAAC,CACDC,OAAO,CAAE,CACPN,cAAc,CAAE,CACdC,IAAI,CAAE,CACJI,SAAS,CAAE,2BAA2B,CACtCT,YAAY,CAAE,EAChB,CACF,CACF,CAAC,CACDW,QAAQ,CAAE,CACRP,cAAc,CAAE,CACdC,IAAI,CAAE,CACJI,SAAS,CAAE,2BACb,CACF,CACF,CAAC,CACDG,SAAS,CAAE,CACTR,cAAc,CAAE,CACdxB,KAAK,CAAE,CACLiC,eAAe,CAAE,SAAS,CAC1BC,WAAW,CAAE,mBACf,CACF,CACF,CAAC,CACDC,SAAS,CAAE,CACTX,cAAc,CAAE,CACdC,IAAI,CAAE,CACJQ,eAAe,CAAE,SAAS,CAC1BG,KAAK,CAAE,SAAS,CAChBP,SAAS,CAAE,2BACb,CACF,CACF,CAAC,CACDQ,iBAAiB,CAAE,CACjBb,cAAc,CAAE,CACdC,IAAI,CAAE,CACJL,YAAY,CAAE,CAAC,CACfkB,MAAM,CAAE,SAAS,CACjB,gBAAgB,CAAE,CAChBL,eAAe,CAAE,SAAS,CAC1B,SAAS,CAAE,CACTA,eAAe,CAAE,SACnB,CACF,CACF,CACF,CACF,CACF,CACF,CAAC,CAAC,CAEF,cAAe,CAAA3C,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}