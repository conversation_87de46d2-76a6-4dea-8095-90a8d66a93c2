{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{Container}from'react-bootstrap';import{AuthProvider}from'./contexts/AuthContext';import Navbar from'./components/layout/Navbar';import Home from'./pages/Home';import Login from'./pages/auth/Login';import Register from'./pages/auth/Register';import ForgotPassword from'./pages/auth/ForgotPassword';import ResetPassword from'./pages/auth/ResetPassword';import Profile from'./pages/user/Profile';import EditProfile from'./pages/user/EditProfile';import PageView from'./pages/cms/PageView';import ProtectedRoute from'./components/auth/ProtectedRoute';import EmailVerificationNotice from'./components/auth/EmailVerificationNotice';import'bootstrap/dist/css/bootstrap.min.css';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[/*#__PURE__*/_jsx(Navbar,{}),/*#__PURE__*/_jsx(Container,{className:\"mt-4\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:/*#__PURE__*/_jsx(Register,{})}),/*#__PURE__*/_jsx(Route,{path:\"/forgot-password\",element:/*#__PURE__*/_jsx(ForgotPassword,{})}),/*#__PURE__*/_jsx(Route,{path:\"/reset-password\",element:/*#__PURE__*/_jsx(ResetPassword,{})}),/*#__PURE__*/_jsx(Route,{path:\"/pages/:slug\",element:/*#__PURE__*/_jsx(PageView,{})}),/*#__PURE__*/_jsx(Route,{path:\"/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Profile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/profile/edit\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(EditProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/email-verification\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(EmailVerificationNotice,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})]})})]})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Container", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Home", "<PERSON><PERSON>", "Register", "ForgotPassword", "ResetPassword", "Profile", "EditProfile", "<PERSON><PERSON><PERSON><PERSON>", "ProtectedRoute", "EmailVerificationNotice", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "path", "element", "to", "replace"], "sources": ["C:/laragon/www/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Container } from 'react-bootstrap';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Navbar from './components/layout/Navbar';\nimport Home from './pages/Home';\nimport Login from './pages/auth/Login';\nimport Register from './pages/auth/Register';\nimport ForgotPassword from './pages/auth/ForgotPassword';\nimport ResetPassword from './pages/auth/ResetPassword';\nimport Profile from './pages/user/Profile';\nimport EditProfile from './pages/user/EditProfile';\nimport PageView from './pages/cms/PageView';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\nimport EmailVerificationNotice from './components/auth/EmailVerificationNotice';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './App.css';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <Navbar />\n          <Container className=\"mt-4\">\n            <Routes>\n              {/* Public routes */}\n              <Route path=\"/\" element={<Home />} />\n              <Route path=\"/login\" element={<Login />} />\n              <Route path=\"/register\" element={<Register />} />\n              <Route path=\"/forgot-password\" element={<ForgotPassword />} />\n              <Route path=\"/reset-password\" element={<ResetPassword />} />\n              <Route path=\"/pages/:slug\" element={<PageView />} />\n\n              {/* Protected routes */}\n              <Route\n                path=\"/profile\"\n                element={\n                  <ProtectedRoute>\n                    <Profile />\n                  </ProtectedRoute>\n                }\n              />\n              <Route\n                path=\"/profile/edit\"\n                element={\n                  <ProtectedRoute>\n                    <EditProfile />\n                  </ProtectedRoute>\n                }\n              />\n\n              {/* Email verification notice */}\n              <Route\n                path=\"/email-verification\"\n                element={\n                  <ProtectedRoute>\n                    <EmailVerificationNotice />\n                  </ProtectedRoute>\n                }\n              />\n\n              {/* Catch all route */}\n              <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n            </Routes>\n          </Container>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OAASC,SAAS,KAAQ,iBAAiB,CAC3C,OAASC,YAAY,KAAQ,wBAAwB,CACrD,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,KAAK,KAAM,oBAAoB,CACtC,MAAO,CAAAC,QAAQ,KAAM,uBAAuB,CAC5C,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,OAAO,KAAM,sBAAsB,CAC1C,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,QAAQ,KAAM,sBAAsB,CAC3C,MAAO,CAAAC,cAAc,KAAM,kCAAkC,CAC7D,MAAO,CAAAC,uBAAuB,KAAM,2CAA2C,CAC/E,MAAO,sCAAsC,CAC7C,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACb,YAAY,EAAAiB,QAAA,cACXJ,IAAA,CAAClB,MAAM,EAAAsB,QAAA,cACLF,KAAA,QAAKG,SAAS,CAAC,KAAK,CAAAD,QAAA,eAClBJ,IAAA,CAACZ,MAAM,GAAE,CAAC,cACVY,IAAA,CAACd,SAAS,EAACmB,SAAS,CAAC,MAAM,CAAAD,QAAA,cACzBF,KAAA,CAACnB,MAAM,EAAAqB,QAAA,eAELJ,IAAA,CAAChB,KAAK,EAACsB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACX,IAAI,GAAE,CAAE,CAAE,CAAC,cACrCW,IAAA,CAAChB,KAAK,EAACsB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEP,IAAA,CAACV,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3CU,IAAA,CAAChB,KAAK,EAACsB,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEP,IAAA,CAACT,QAAQ,GAAE,CAAE,CAAE,CAAC,cACjDS,IAAA,CAAChB,KAAK,EAACsB,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEP,IAAA,CAACR,cAAc,GAAE,CAAE,CAAE,CAAC,cAC9DQ,IAAA,CAAChB,KAAK,EAACsB,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEP,IAAA,CAACP,aAAa,GAAE,CAAE,CAAE,CAAC,cAC5DO,IAAA,CAAChB,KAAK,EAACsB,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEP,IAAA,CAACJ,QAAQ,GAAE,CAAE,CAAE,CAAC,cAGpDI,IAAA,CAAChB,KAAK,EACJsB,IAAI,CAAC,UAAU,CACfC,OAAO,cACLP,IAAA,CAACH,cAAc,EAAAO,QAAA,cACbJ,IAAA,CAACN,OAAO,GAAE,CAAC,CACG,CACjB,CACF,CAAC,cACFM,IAAA,CAAChB,KAAK,EACJsB,IAAI,CAAC,eAAe,CACpBC,OAAO,cACLP,IAAA,CAACH,cAAc,EAAAO,QAAA,cACbJ,IAAA,CAACL,WAAW,GAAE,CAAC,CACD,CACjB,CACF,CAAC,cAGFK,IAAA,CAAChB,KAAK,EACJsB,IAAI,CAAC,qBAAqB,CAC1BC,OAAO,cACLP,IAAA,CAACH,cAAc,EAAAO,QAAA,cACbJ,IAAA,CAACF,uBAAuB,GAAE,CAAC,CACb,CACjB,CACF,CAAC,cAGFE,IAAA,CAAChB,KAAK,EAACsB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACf,QAAQ,EAACuB,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EAClD,CAAC,CACA,CAAC,EACT,CAAC,CACA,CAAC,CACG,CAAC,CAEnB,CAEA,cAAe,CAAAN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}