{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    bg = 'primary',\n    pill = false,\n    text,\n    className,\n    as: Component = 'span',\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "Badge", "forwardRef", "_ref", "ref", "bsPrefix", "bg", "pill", "text", "className", "as", "Component", "props", "prefix", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/Badge.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  bg = 'primary',\n  pill = false,\n  text,\n  className,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,KAAK,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAAC,IAAA,EAQzCC,GAAG,KAAK;EAAA,IARkC;IAC3CC,QAAQ;IACRC,EAAE,GAAG,SAAS;IACdC,IAAI,GAAG,KAAK;IACZC,IAAI;IACJC,SAAS;IACTC,EAAE,EAAEC,SAAS,GAAG,MAAM;IACtB,GAAGC;EACL,CAAC,GAAAT,IAAA;EACC,MAAMU,MAAM,GAAGf,kBAAkB,CAACO,QAAQ,EAAE,OAAO,CAAC;EACpD,OAAO,aAAaL,IAAI,CAACW,SAAS,EAAE;IAClCP,GAAG,EAAEA,GAAG;IACR,GAAGQ,KAAK;IACRH,SAAS,EAAEb,UAAU,CAACa,SAAS,EAAEI,MAAM,EAAEN,IAAI,IAAI,cAAc,EAAEC,IAAI,IAAI,QAAQA,IAAI,EAAE,EAAEF,EAAE,IAAI,MAAMA,EAAE,EAAE;EAC3G,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,KAAK,CAACa,WAAW,GAAG,OAAO;AAC3B,eAAeb,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}