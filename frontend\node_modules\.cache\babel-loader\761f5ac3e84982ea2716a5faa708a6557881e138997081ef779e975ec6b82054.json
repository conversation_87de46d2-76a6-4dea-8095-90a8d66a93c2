{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport divWithClassName from './divWithClassName';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nconst ModalTitle = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    className,\n    bsPrefix,\n    as: Component = DivStyledAsH4,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nModalTitle.displayName = 'ModalTitle';\nexport default ModalTitle;", "map": {"version": 3, "names": ["React", "classNames", "divWithClassName", "useBootstrapPrefix", "jsx", "_jsx", "DivStyledAsH4", "ModalTitle", "forwardRef", "_ref", "ref", "className", "bsPrefix", "as", "Component", "props", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/ModalTitle.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport divWithClassName from './divWithClassName';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nconst ModalTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nModalTitle.displayName = 'ModalTitle';\nexport default ModalTitle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAGJ,gBAAgB,CAAC,IAAI,CAAC;AAC5C,MAAMK,UAAU,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAAC,IAAA,EAK9CC,GAAG,KAAK;EAAA,IALuC;IAChDC,SAAS;IACTC,QAAQ;IACRC,EAAE,EAAEC,SAAS,GAAGR,aAAa;IAC7B,GAAGS;EACL,CAAC,GAAAN,IAAA;EACCG,QAAQ,GAAGT,kBAAkB,CAACS,QAAQ,EAAE,aAAa,CAAC;EACtD,OAAO,aAAaP,IAAI,CAACS,SAAS,EAAE;IAClCJ,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,UAAU,CAACS,WAAW,GAAG,YAAY;AACrC,eAAeT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}