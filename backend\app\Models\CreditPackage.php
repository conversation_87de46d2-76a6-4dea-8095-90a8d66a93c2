<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CreditPackage extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'credit_amount',
        'is_active',
        'sort_order',
        'features',
    ];

    protected function casts(): array
    {
        return [
            'price' => 'decimal:2',
            'is_active' => 'boolean',
            'features' => 'array',
        ];
    }

    /**
     * Scope for active packages
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordering packages
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Get wallet transactions for this package
     */
    public function walletTransactions()
    {
        return $this->hasMany(WalletTransaction::class, 'package_id');
    }

    /**
     * Get credit transactions for this package (legacy - redirects to wallet transactions)
     * @deprecated Use walletTransactions() instead
     */
    public function creditTransactions()
    {
        return $this->walletTransactions();
    }

    /**
     * Get the price per credit ratio
     */
    public function getPricePerCreditAttribute()
    {
        return $this->credit_amount > 0 ? $this->price / $this->credit_amount : 0;
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute()
    {
        return 'RM ' . number_format($this->price, 2);
    }
}
