{"ast": null, "code": "import createPalette from \"./createPalette.js\";\nimport createThemeWithVars from \"./createThemeWithVars.js\";\nimport createThemeNoVars from \"./createThemeNoVars.js\";\n// eslint-disable-next-line consistent-return\nfunction attachColorScheme(theme, scheme, colorScheme) {\n  if (!theme.colorSchemes) {\n    return undefined;\n  }\n  if (colorScheme) {\n    theme.colorSchemes[scheme] = {\n      ...(colorScheme !== true && colorScheme),\n      palette: createPalette({\n        ...(colorScheme === true ? {} : colorScheme.palette),\n        mode: scheme\n      }) // cast type to skip module augmentation test\n    };\n  }\n}\n\n/**\n * Generate a theme base on the options received.\n * @param options Takes an incomplete theme object and adds the missing parts.\n * @param args Deep merge the arguments with the about to be returned theme.\n * @returns A complete, ready-to-use theme object.\n */\nexport default function createTheme() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    palette,\n    cssVariables = false,\n    colorSchemes: initialColorSchemes = !palette ? {\n      light: true\n    } : undefined,\n    defaultColorScheme: initialDefaultColorScheme = palette?.mode,\n    ...rest\n  } = options;\n  const defaultColorSchemeInput = initialDefaultColorScheme || 'light';\n  const defaultScheme = initialColorSchemes?.[defaultColorSchemeInput];\n  const colorSchemesInput = {\n    ...initialColorSchemes,\n    ...(palette ? {\n      [defaultColorSchemeInput]: {\n        ...(typeof defaultScheme !== 'boolean' && defaultScheme),\n        palette\n      }\n    } : undefined)\n  };\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  if (cssVariables === false) {\n    if (!('colorSchemes' in options)) {\n      // Behaves exactly as v5\n      return createThemeNoVars(options, ...args);\n    }\n    let paletteOptions = palette;\n    if (!('palette' in options)) {\n      if (colorSchemesInput[defaultColorSchemeInput]) {\n        if (colorSchemesInput[defaultColorSchemeInput] !== true) {\n          paletteOptions = colorSchemesInput[defaultColorSchemeInput].palette;\n        } else if (defaultColorSchemeInput === 'dark') {\n          // @ts-ignore to prevent the module augmentation test from failing\n          paletteOptions = {\n            mode: 'dark'\n          };\n        }\n      }\n    }\n    const theme = createThemeNoVars({\n      ...options,\n      palette: paletteOptions\n    }, ...args);\n    theme.defaultColorScheme = defaultColorSchemeInput;\n    theme.colorSchemes = colorSchemesInput;\n    if (theme.palette.mode === 'light') {\n      theme.colorSchemes.light = {\n        ...(colorSchemesInput.light !== true && colorSchemesInput.light),\n        palette: theme.palette\n      };\n      attachColorScheme(theme, 'dark', colorSchemesInput.dark);\n    }\n    if (theme.palette.mode === 'dark') {\n      theme.colorSchemes.dark = {\n        ...(colorSchemesInput.dark !== true && colorSchemesInput.dark),\n        palette: theme.palette\n      };\n      attachColorScheme(theme, 'light', colorSchemesInput.light);\n    }\n    return theme;\n  }\n  if (!palette && !('light' in colorSchemesInput) && defaultColorSchemeInput === 'light') {\n    colorSchemesInput.light = true;\n  }\n  return createThemeWithVars({\n    ...rest,\n    colorSchemes: colorSchemesInput,\n    defaultColorScheme: defaultColorSchemeInput,\n    ...(typeof cssVariables !== 'boolean' && cssVariables)\n  }, ...args);\n}", "map": {"version": 3, "names": ["createPalette", "createThemeWithVars", "createThemeNoVars", "attachColorScheme", "theme", "scheme", "colorScheme", "colorSchemes", "undefined", "palette", "mode", "createTheme", "options", "arguments", "length", "cssVariables", "initialColorSchemes", "light", "defaultColorScheme", "initialDefaultColorScheme", "rest", "defaultColorSchemeInput", "defaultScheme", "colorSchemesInput", "_len", "args", "Array", "_key", "paletteOptions", "dark"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/styles/createTheme.js"], "sourcesContent": ["import createPalette from \"./createPalette.js\";\nimport createThemeWithVars from \"./createThemeWithVars.js\";\nimport createThemeNoVars from \"./createThemeNoVars.js\";\n// eslint-disable-next-line consistent-return\nfunction attachColorScheme(theme, scheme, colorScheme) {\n  if (!theme.colorSchemes) {\n    return undefined;\n  }\n  if (colorScheme) {\n    theme.colorSchemes[scheme] = {\n      ...(colorScheme !== true && colorScheme),\n      palette: createPalette({\n        ...(colorScheme === true ? {} : colorScheme.palette),\n        mode: scheme\n      }) // cast type to skip module augmentation test\n    };\n  }\n}\n\n/**\n * Generate a theme base on the options received.\n * @param options Takes an incomplete theme object and adds the missing parts.\n * @param args Deep merge the arguments with the about to be returned theme.\n * @returns A complete, ready-to-use theme object.\n */\nexport default function createTheme(options = {},\n// cast type to skip module augmentation test\n...args) {\n  const {\n    palette,\n    cssVariables = false,\n    colorSchemes: initialColorSchemes = !palette ? {\n      light: true\n    } : undefined,\n    defaultColorScheme: initialDefaultColorScheme = palette?.mode,\n    ...rest\n  } = options;\n  const defaultColorSchemeInput = initialDefaultColorScheme || 'light';\n  const defaultScheme = initialColorSchemes?.[defaultColorSchemeInput];\n  const colorSchemesInput = {\n    ...initialColorSchemes,\n    ...(palette ? {\n      [defaultColorSchemeInput]: {\n        ...(typeof defaultScheme !== 'boolean' && defaultScheme),\n        palette\n      }\n    } : undefined)\n  };\n  if (cssVariables === false) {\n    if (!('colorSchemes' in options)) {\n      // Behaves exactly as v5\n      return createThemeNoVars(options, ...args);\n    }\n    let paletteOptions = palette;\n    if (!('palette' in options)) {\n      if (colorSchemesInput[defaultColorSchemeInput]) {\n        if (colorSchemesInput[defaultColorSchemeInput] !== true) {\n          paletteOptions = colorSchemesInput[defaultColorSchemeInput].palette;\n        } else if (defaultColorSchemeInput === 'dark') {\n          // @ts-ignore to prevent the module augmentation test from failing\n          paletteOptions = {\n            mode: 'dark'\n          };\n        }\n      }\n    }\n    const theme = createThemeNoVars({\n      ...options,\n      palette: paletteOptions\n    }, ...args);\n    theme.defaultColorScheme = defaultColorSchemeInput;\n    theme.colorSchemes = colorSchemesInput;\n    if (theme.palette.mode === 'light') {\n      theme.colorSchemes.light = {\n        ...(colorSchemesInput.light !== true && colorSchemesInput.light),\n        palette: theme.palette\n      };\n      attachColorScheme(theme, 'dark', colorSchemesInput.dark);\n    }\n    if (theme.palette.mode === 'dark') {\n      theme.colorSchemes.dark = {\n        ...(colorSchemesInput.dark !== true && colorSchemesInput.dark),\n        palette: theme.palette\n      };\n      attachColorScheme(theme, 'light', colorSchemesInput.light);\n    }\n    return theme;\n  }\n  if (!palette && !('light' in colorSchemesInput) && defaultColorSchemeInput === 'light') {\n    colorSchemesInput.light = true;\n  }\n  return createThemeWithVars({\n    ...rest,\n    colorSchemes: colorSchemesInput,\n    defaultColorScheme: defaultColorSchemeInput,\n    ...(typeof cssVariables !== 'boolean' && cssVariables)\n  }, ...args);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,mBAAmB,MAAM,0BAA0B;AAC1D,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAE;EACrD,IAAI,CAACF,KAAK,CAACG,YAAY,EAAE;IACvB,OAAOC,SAAS;EAClB;EACA,IAAIF,WAAW,EAAE;IACfF,KAAK,CAACG,YAAY,CAACF,MAAM,CAAC,GAAG;MAC3B,IAAIC,WAAW,KAAK,IAAI,IAAIA,WAAW,CAAC;MACxCG,OAAO,EAAET,aAAa,CAAC;QACrB,IAAIM,WAAW,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA,WAAW,CAACG,OAAO,CAAC;QACpDC,IAAI,EAAEL;MACR,CAAC,CAAC,CAAC;IACL,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASM,WAAWA,CAAA,EAE1B;EAAA,IAF2BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAL,SAAA,GAAAK,SAAA,MAAG,CAAC,CAAC;EAG9C,MAAM;IACJJ,OAAO;IACPM,YAAY,GAAG,KAAK;IACpBR,YAAY,EAAES,mBAAmB,GAAG,CAACP,OAAO,GAAG;MAC7CQ,KAAK,EAAE;IACT,CAAC,GAAGT,SAAS;IACbU,kBAAkB,EAAEC,yBAAyB,GAAGV,OAAO,EAAEC,IAAI;IAC7D,GAAGU;EACL,CAAC,GAAGR,OAAO;EACX,MAAMS,uBAAuB,GAAGF,yBAAyB,IAAI,OAAO;EACpE,MAAMG,aAAa,GAAGN,mBAAmB,GAAGK,uBAAuB,CAAC;EACpE,MAAME,iBAAiB,GAAG;IACxB,GAAGP,mBAAmB;IACtB,IAAIP,OAAO,GAAG;MACZ,CAACY,uBAAuB,GAAG;QACzB,IAAI,OAAOC,aAAa,KAAK,SAAS,IAAIA,aAAa,CAAC;QACxDb;MACF;IACF,CAAC,GAAGD,SAAS;EACf,CAAC;EAAC,SAAAgB,IAAA,GAAAX,SAAA,CAAAC,MAAA,EApBDW,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAd,SAAA,CAAAc,IAAA;EAAA;EAqBL,IAAIZ,YAAY,KAAK,KAAK,EAAE;IAC1B,IAAI,EAAE,cAAc,IAAIH,OAAO,CAAC,EAAE;MAChC;MACA,OAAOV,iBAAiB,CAACU,OAAO,EAAE,GAAGa,IAAI,CAAC;IAC5C;IACA,IAAIG,cAAc,GAAGnB,OAAO;IAC5B,IAAI,EAAE,SAAS,IAAIG,OAAO,CAAC,EAAE;MAC3B,IAAIW,iBAAiB,CAACF,uBAAuB,CAAC,EAAE;QAC9C,IAAIE,iBAAiB,CAACF,uBAAuB,CAAC,KAAK,IAAI,EAAE;UACvDO,cAAc,GAAGL,iBAAiB,CAACF,uBAAuB,CAAC,CAACZ,OAAO;QACrE,CAAC,MAAM,IAAIY,uBAAuB,KAAK,MAAM,EAAE;UAC7C;UACAO,cAAc,GAAG;YACflB,IAAI,EAAE;UACR,CAAC;QACH;MACF;IACF;IACA,MAAMN,KAAK,GAAGF,iBAAiB,CAAC;MAC9B,GAAGU,OAAO;MACVH,OAAO,EAAEmB;IACX,CAAC,EAAE,GAAGH,IAAI,CAAC;IACXrB,KAAK,CAACc,kBAAkB,GAAGG,uBAAuB;IAClDjB,KAAK,CAACG,YAAY,GAAGgB,iBAAiB;IACtC,IAAInB,KAAK,CAACK,OAAO,CAACC,IAAI,KAAK,OAAO,EAAE;MAClCN,KAAK,CAACG,YAAY,CAACU,KAAK,GAAG;QACzB,IAAIM,iBAAiB,CAACN,KAAK,KAAK,IAAI,IAAIM,iBAAiB,CAACN,KAAK,CAAC;QAChER,OAAO,EAAEL,KAAK,CAACK;MACjB,CAAC;MACDN,iBAAiB,CAACC,KAAK,EAAE,MAAM,EAAEmB,iBAAiB,CAACM,IAAI,CAAC;IAC1D;IACA,IAAIzB,KAAK,CAACK,OAAO,CAACC,IAAI,KAAK,MAAM,EAAE;MACjCN,KAAK,CAACG,YAAY,CAACsB,IAAI,GAAG;QACxB,IAAIN,iBAAiB,CAACM,IAAI,KAAK,IAAI,IAAIN,iBAAiB,CAACM,IAAI,CAAC;QAC9DpB,OAAO,EAAEL,KAAK,CAACK;MACjB,CAAC;MACDN,iBAAiB,CAACC,KAAK,EAAE,OAAO,EAAEmB,iBAAiB,CAACN,KAAK,CAAC;IAC5D;IACA,OAAOb,KAAK;EACd;EACA,IAAI,CAACK,OAAO,IAAI,EAAE,OAAO,IAAIc,iBAAiB,CAAC,IAAIF,uBAAuB,KAAK,OAAO,EAAE;IACtFE,iBAAiB,CAACN,KAAK,GAAG,IAAI;EAChC;EACA,OAAOhB,mBAAmB,CAAC;IACzB,GAAGmB,IAAI;IACPb,YAAY,EAAEgB,iBAAiB;IAC/BL,kBAAkB,EAAEG,uBAAuB;IAC3C,IAAI,OAAON,YAAY,KAAK,SAAS,IAAIA,YAAY;EACvD,CAAC,EAAE,GAAGU,IAAI,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}