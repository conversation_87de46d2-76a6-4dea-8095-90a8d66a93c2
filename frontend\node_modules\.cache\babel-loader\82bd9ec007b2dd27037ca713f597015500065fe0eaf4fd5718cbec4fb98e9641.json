{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport useBreakpoint from '@restart/hooks/useBreakpoint';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport * as React from 'react';\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport BaseModal from '@restart/ui/Modal';\nimport Fade from './Fade';\nimport OffcanvasBody from './OffcanvasBody';\nimport OffcanvasToggling from './OffcanvasToggling';\nimport ModalContext from './ModalContext';\nimport OffcanvasHeader from './OffcanvasHeader';\nimport OffcanvasTitle from './OffcanvasTitle';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport BootstrapModalManager, { getSharedManager } from './BootstrapModalManager';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction DialogTransition(props) {\n  return /*#__PURE__*/_jsx(OffcanvasToggling, {\n    ...props\n  });\n}\nfunction BackdropTransition(props) {\n  return /*#__PURE__*/_jsx(Fade, {\n    ...props\n  });\n}\nconst Offcanvas = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    children,\n    'aria-labelledby': ariaLabelledby,\n    placement = 'start',\n    responsive,\n    /* BaseModal props */\n\n    show = false,\n    backdrop = true,\n    keyboard = true,\n    scroll = false,\n    onEscapeKeyDown,\n    onShow,\n    onHide,\n    container,\n    autoFocus = true,\n    enforceFocus = true,\n    restoreFocus = true,\n    restoreFocusOptions,\n    onEntered,\n    onExit,\n    onExiting,\n    onEnter,\n    onEntering,\n    onExited,\n    backdropClassName,\n    manager: propsManager,\n    renderStaticNode = false,\n    ...props\n  } = _ref;\n  const modalManager = useRef();\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas');\n  const [showOffcanvas, setShowOffcanvas] = useState(false);\n  const handleHide = useEventCallback(onHide);\n  const hideResponsiveOffcanvas = useBreakpoint(responsive || 'xs', 'up');\n  useEffect(() => {\n    // Handles the case where screen is resized while the responsive\n    // offcanvas is shown. If `responsive` not provided, just use `show`.\n    setShowOffcanvas(responsive ? show && !hideResponsiveOffcanvas : show);\n  }, [show, responsive, hideResponsiveOffcanvas]);\n  const modalContext = useMemo(() => ({\n    onHide: handleHide\n  }), [handleHide]);\n  function getModalManager() {\n    if (propsManager) return propsManager;\n    if (scroll) {\n      // Have to use a different modal manager since the shared\n      // one handles overflow.\n      if (!modalManager.current) modalManager.current = new BootstrapModalManager({\n        handleContainerOverflow: false\n      });\n      return modalManager.current;\n    }\n    return getSharedManager();\n  }\n  const handleEnter = function (node) {\n    if (node) node.style.visibility = 'visible';\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    onEnter == null || onEnter(node, ...args);\n  };\n  const handleExited = function (node) {\n    if (node) node.style.visibility = '';\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    onExited == null || onExited(...args);\n  };\n  const renderBackdrop = useCallback(backdropProps => /*#__PURE__*/_jsx(\"div\", {\n    ...backdropProps,\n    className: classNames(`${bsPrefix}-backdrop`, backdropClassName)\n  }), [backdropClassName, bsPrefix]);\n  const renderDialog = dialogProps => /*#__PURE__*/_jsx(\"div\", {\n    ...dialogProps,\n    ...props,\n    className: classNames(className, responsive ? `${bsPrefix}-${responsive}` : bsPrefix, `${bsPrefix}-${placement}`),\n    \"aria-labelledby\": ariaLabelledby,\n    children: children\n  });\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [!showOffcanvas && (responsive || renderStaticNode) && renderDialog({}), /*#__PURE__*/_jsx(ModalContext.Provider, {\n      value: modalContext,\n      children: /*#__PURE__*/_jsx(BaseModal, {\n        show: showOffcanvas,\n        ref: ref,\n        backdrop: backdrop,\n        container: container,\n        keyboard: keyboard,\n        autoFocus: autoFocus,\n        enforceFocus: enforceFocus && !scroll,\n        restoreFocus: restoreFocus,\n        restoreFocusOptions: restoreFocusOptions,\n        onEscapeKeyDown: onEscapeKeyDown,\n        onShow: onShow,\n        onHide: handleHide,\n        onEnter: handleEnter,\n        onEntering: onEntering,\n        onEntered: onEntered,\n        onExit: onExit,\n        onExiting: onExiting,\n        onExited: handleExited,\n        manager: getModalManager(),\n        transition: DialogTransition,\n        backdropTransition: BackdropTransition,\n        renderBackdrop: renderBackdrop,\n        renderDialog: renderDialog\n      })\n    })]\n  });\n});\nOffcanvas.displayName = 'Offcanvas';\nexport default Object.assign(Offcanvas, {\n  Body: OffcanvasBody,\n  Header: OffcanvasHeader,\n  Title: OffcanvasTitle\n});", "map": {"version": 3, "names": ["classNames", "useBreakpoint", "useEventCallback", "React", "useCallback", "useEffect", "useMemo", "useRef", "useState", "BaseModal", "Fade", "OffcanvasBody", "OffcanvasToggling", "ModalContext", "OffcanvasHeader", "OffcanvasTitle", "useBootstrapPrefix", "BootstrapModalManager", "getSharedManager", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "DialogTransition", "props", "BackdropTransition", "<PERSON><PERSON><PERSON>", "forwardRef", "_ref", "ref", "bsPrefix", "className", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placement", "responsive", "show", "backdrop", "keyboard", "scroll", "onEscapeKeyDown", "onShow", "onHide", "container", "autoFocus", "enforceFocus", "restoreFocus", "restoreFocusOptions", "onEntered", "onExit", "onExiting", "onEnter", "onEntering", "onExited", "backdropClassName", "manager", "props<PERSON>anager", "renderStaticNode", "modalManager", "showOffcanvas", "setShowOffcanvas", "handleHide", "hideResponsiveOffcanvas", "modalContext", "getModalManager", "current", "handleContainerOverflow", "handleEnter", "node", "style", "visibility", "_len", "arguments", "length", "args", "Array", "_key", "handleExited", "_len2", "_key2", "renderBackdrop", "backdropProps", "renderDialog", "dialogProps", "Provider", "value", "transition", "backdropTransition", "displayName", "Object", "assign", "Body", "Header", "Title"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/Offcanvas.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport useBreakpoint from '@restart/hooks/useBreakpoint';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport * as React from 'react';\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport BaseModal from '@restart/ui/Modal';\nimport Fade from './Fade';\nimport OffcanvasBody from './OffcanvasBody';\nimport OffcanvasToggling from './OffcanvasToggling';\nimport ModalContext from './ModalContext';\nimport OffcanvasHeader from './OffcanvasHeader';\nimport OffcanvasTitle from './OffcanvasTitle';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport BootstrapModalManager, { getSharedManager } from './BootstrapModalManager';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction DialogTransition(props) {\n  return /*#__PURE__*/_jsx(OffcanvasToggling, {\n    ...props\n  });\n}\nfunction BackdropTransition(props) {\n  return /*#__PURE__*/_jsx(Fade, {\n    ...props\n  });\n}\nconst Offcanvas = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  'aria-labelledby': ariaLabelledby,\n  placement = 'start',\n  responsive,\n  /* BaseModal props */\n\n  show = false,\n  backdrop = true,\n  keyboard = true,\n  scroll = false,\n  onEscapeKeyDown,\n  onShow,\n  onHide,\n  container,\n  autoFocus = true,\n  enforceFocus = true,\n  restoreFocus = true,\n  restoreFocusOptions,\n  onEntered,\n  onExit,\n  onExiting,\n  onEnter,\n  onEntering,\n  onExited,\n  backdropClassName,\n  manager: propsManager,\n  renderStaticNode = false,\n  ...props\n}, ref) => {\n  const modalManager = useRef();\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas');\n  const [showOffcanvas, setShowOffcanvas] = useState(false);\n  const handleHide = useEventCallback(onHide);\n  const hideResponsiveOffcanvas = useBreakpoint(responsive || 'xs', 'up');\n  useEffect(() => {\n    // Handles the case where screen is resized while the responsive\n    // offcanvas is shown. If `responsive` not provided, just use `show`.\n    setShowOffcanvas(responsive ? show && !hideResponsiveOffcanvas : show);\n  }, [show, responsive, hideResponsiveOffcanvas]);\n  const modalContext = useMemo(() => ({\n    onHide: handleHide\n  }), [handleHide]);\n  function getModalManager() {\n    if (propsManager) return propsManager;\n    if (scroll) {\n      // Have to use a different modal manager since the shared\n      // one handles overflow.\n      if (!modalManager.current) modalManager.current = new BootstrapModalManager({\n        handleContainerOverflow: false\n      });\n      return modalManager.current;\n    }\n    return getSharedManager();\n  }\n  const handleEnter = (node, ...args) => {\n    if (node) node.style.visibility = 'visible';\n    onEnter == null || onEnter(node, ...args);\n  };\n  const handleExited = (node, ...args) => {\n    if (node) node.style.visibility = '';\n    onExited == null || onExited(...args);\n  };\n  const renderBackdrop = useCallback(backdropProps => /*#__PURE__*/_jsx(\"div\", {\n    ...backdropProps,\n    className: classNames(`${bsPrefix}-backdrop`, backdropClassName)\n  }), [backdropClassName, bsPrefix]);\n  const renderDialog = dialogProps => /*#__PURE__*/_jsx(\"div\", {\n    ...dialogProps,\n    ...props,\n    className: classNames(className, responsive ? `${bsPrefix}-${responsive}` : bsPrefix, `${bsPrefix}-${placement}`),\n    \"aria-labelledby\": ariaLabelledby,\n    children: children\n  });\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [!showOffcanvas && (responsive || renderStaticNode) && renderDialog({}), /*#__PURE__*/_jsx(ModalContext.Provider, {\n      value: modalContext,\n      children: /*#__PURE__*/_jsx(BaseModal, {\n        show: showOffcanvas,\n        ref: ref,\n        backdrop: backdrop,\n        container: container,\n        keyboard: keyboard,\n        autoFocus: autoFocus,\n        enforceFocus: enforceFocus && !scroll,\n        restoreFocus: restoreFocus,\n        restoreFocusOptions: restoreFocusOptions,\n        onEscapeKeyDown: onEscapeKeyDown,\n        onShow: onShow,\n        onHide: handleHide,\n        onEnter: handleEnter,\n        onEntering: onEntering,\n        onEntered: onEntered,\n        onExit: onExit,\n        onExiting: onExiting,\n        onExited: handleExited,\n        manager: getModalManager(),\n        transition: DialogTransition,\n        backdropTransition: BackdropTransition,\n        renderBackdrop: renderBackdrop,\n        renderDialog: renderDialog\n      })\n    })]\n  });\n});\nOffcanvas.displayName = 'Offcanvas';\nexport default Object.assign(Offcanvas, {\n  Body: OffcanvasBody,\n  Header: OffcanvasHeader,\n  Title: OffcanvasTitle\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACzE,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,qBAAqB,IAAIC,gBAAgB,QAAQ,yBAAyB;AACjF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACzD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,OAAO,aAAaN,IAAI,CAACR,iBAAiB,EAAE;IAC1C,GAAGc;EACL,CAAC,CAAC;AACJ;AACA,SAASC,kBAAkBA,CAACD,KAAK,EAAE;EACjC,OAAO,aAAaN,IAAI,CAACV,IAAI,EAAE;IAC7B,GAAGgB;EACL,CAAC,CAAC;AACJ;AACA,MAAME,SAAS,GAAG,aAAazB,KAAK,CAAC0B,UAAU,CAAC,CAAAC,IAAA,EA+B7CC,GAAG,KAAK;EAAA,IA/BsC;IAC/CC,QAAQ;IACRC,SAAS;IACTC,QAAQ;IACR,iBAAiB,EAAEC,cAAc;IACjCC,SAAS,GAAG,OAAO;IACnBC,UAAU;IACV;;IAEAC,IAAI,GAAG,KAAK;IACZC,QAAQ,GAAG,IAAI;IACfC,QAAQ,GAAG,IAAI;IACfC,MAAM,GAAG,KAAK;IACdC,eAAe;IACfC,MAAM;IACNC,MAAM;IACNC,SAAS;IACTC,SAAS,GAAG,IAAI;IAChBC,YAAY,GAAG,IAAI;IACnBC,YAAY,GAAG,IAAI;IACnBC,mBAAmB;IACnBC,SAAS;IACTC,MAAM;IACNC,SAAS;IACTC,OAAO;IACPC,UAAU;IACVC,QAAQ;IACRC,iBAAiB;IACjBC,OAAO,EAAEC,YAAY;IACrBC,gBAAgB,GAAG,KAAK;IACxB,GAAGjC;EACL,CAAC,GAAAI,IAAA;EACC,MAAM8B,YAAY,GAAGrD,MAAM,CAAC,CAAC;EAC7ByB,QAAQ,GAAGhB,kBAAkB,CAACgB,QAAQ,EAAE,WAAW,CAAC;EACpD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMuD,UAAU,GAAG7D,gBAAgB,CAAC0C,MAAM,CAAC;EAC3C,MAAMoB,uBAAuB,GAAG/D,aAAa,CAACoC,UAAU,IAAI,IAAI,EAAE,IAAI,CAAC;EACvEhC,SAAS,CAAC,MAAM;IACd;IACA;IACAyD,gBAAgB,CAACzB,UAAU,GAAGC,IAAI,IAAI,CAAC0B,uBAAuB,GAAG1B,IAAI,CAAC;EACxE,CAAC,EAAE,CAACA,IAAI,EAAED,UAAU,EAAE2B,uBAAuB,CAAC,CAAC;EAC/C,MAAMC,YAAY,GAAG3D,OAAO,CAAC,OAAO;IAClCsC,MAAM,EAAEmB;EACV,CAAC,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EACjB,SAASG,eAAeA,CAAA,EAAG;IACzB,IAAIR,YAAY,EAAE,OAAOA,YAAY;IACrC,IAAIjB,MAAM,EAAE;MACV;MACA;MACA,IAAI,CAACmB,YAAY,CAACO,OAAO,EAAEP,YAAY,CAACO,OAAO,GAAG,IAAIlD,qBAAqB,CAAC;QAC1EmD,uBAAuB,EAAE;MAC3B,CAAC,CAAC;MACF,OAAOR,YAAY,CAACO,OAAO;IAC7B;IACA,OAAOjD,gBAAgB,CAAC,CAAC;EAC3B;EACA,MAAMmD,WAAW,GAAG,SAAAA,CAACC,IAAI,EAAc;IACrC,IAAIA,IAAI,EAAEA,IAAI,CAACC,KAAK,CAACC,UAAU,GAAG,SAAS;IAAC,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADhBC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;IAAA;IAEhCzB,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACiB,IAAI,EAAE,GAAGM,IAAI,CAAC;EAC3C,CAAC;EACD,MAAMG,YAAY,GAAG,SAAAA,CAACT,IAAI,EAAc;IACtC,IAAIA,IAAI,EAAEA,IAAI,CAACC,KAAK,CAACC,UAAU,GAAG,EAAE;IAAC,SAAAQ,KAAA,GAAAN,SAAA,CAAAC,MAAA,EADRC,IAAI,OAAAC,KAAA,CAAAG,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJL,IAAI,CAAAK,KAAA,QAAAP,SAAA,CAAAO,KAAA;IAAA;IAEjC1B,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAAC,GAAGqB,IAAI,CAAC;EACvC,CAAC;EACD,MAAMM,cAAc,GAAG9E,WAAW,CAAC+E,aAAa,IAAI,aAAa/D,IAAI,CAAC,KAAK,EAAE;IAC3E,GAAG+D,aAAa;IAChBlD,SAAS,EAAEjC,UAAU,CAAC,GAAGgC,QAAQ,WAAW,EAAEwB,iBAAiB;EACjE,CAAC,CAAC,EAAE,CAACA,iBAAiB,EAAExB,QAAQ,CAAC,CAAC;EAClC,MAAMoD,YAAY,GAAGC,WAAW,IAAI,aAAajE,IAAI,CAAC,KAAK,EAAE;IAC3D,GAAGiE,WAAW;IACd,GAAG3D,KAAK;IACRO,SAAS,EAAEjC,UAAU,CAACiC,SAAS,EAAEI,UAAU,GAAG,GAAGL,QAAQ,IAAIK,UAAU,EAAE,GAAGL,QAAQ,EAAE,GAAGA,QAAQ,IAAII,SAAS,EAAE,CAAC;IACjH,iBAAiB,EAAED,cAAc;IACjCD,QAAQ,EAAEA;EACZ,CAAC,CAAC;EACF,OAAO,aAAaV,KAAK,CAACF,SAAS,EAAE;IACnCY,QAAQ,EAAE,CAAC,CAAC2B,aAAa,KAAKxB,UAAU,IAAIsB,gBAAgB,CAAC,IAAIyB,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,aAAahE,IAAI,CAACP,YAAY,CAACyE,QAAQ,EAAE;MAC1HC,KAAK,EAAEtB,YAAY;MACnB/B,QAAQ,EAAE,aAAad,IAAI,CAACX,SAAS,EAAE;QACrC6B,IAAI,EAAEuB,aAAa;QACnB9B,GAAG,EAAEA,GAAG;QACRQ,QAAQ,EAAEA,QAAQ;QAClBM,SAAS,EAAEA,SAAS;QACpBL,QAAQ,EAAEA,QAAQ;QAClBM,SAAS,EAAEA,SAAS;QACpBC,YAAY,EAAEA,YAAY,IAAI,CAACN,MAAM;QACrCO,YAAY,EAAEA,YAAY;QAC1BC,mBAAmB,EAAEA,mBAAmB;QACxCP,eAAe,EAAEA,eAAe;QAChCC,MAAM,EAAEA,MAAM;QACdC,MAAM,EAAEmB,UAAU;QAClBV,OAAO,EAAEgB,WAAW;QACpBf,UAAU,EAAEA,UAAU;QACtBJ,SAAS,EAAEA,SAAS;QACpBC,MAAM,EAAEA,MAAM;QACdC,SAAS,EAAEA,SAAS;QACpBG,QAAQ,EAAEwB,YAAY;QACtBtB,OAAO,EAAES,eAAe,CAAC,CAAC;QAC1BsB,UAAU,EAAE/D,gBAAgB;QAC5BgE,kBAAkB,EAAE9D,kBAAkB;QACtCuD,cAAc,EAAEA,cAAc;QAC9BE,YAAY,EAAEA;MAChB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFxD,SAAS,CAAC8D,WAAW,GAAG,WAAW;AACnC,eAAeC,MAAM,CAACC,MAAM,CAAChE,SAAS,EAAE;EACtCiE,IAAI,EAAElF,aAAa;EACnBmF,MAAM,EAAEhF,eAAe;EACvBiF,KAAK,EAAEhF;AACT,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}