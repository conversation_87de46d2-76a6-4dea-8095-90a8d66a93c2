{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport styleFunctionSx from '@mui/system/styleFunctionSx';\nimport { unstable_createCssVarsProvider as createCssVarsProvider } from '@mui/system';\nimport createTheme from \"./createTheme.js\";\nimport createTypography from \"./createTypography.js\";\nimport THEME_ID from \"./identifier.js\";\nimport { defaultConfig } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst {\n  CssVarsProvider: InternalCssVarsProvider,\n  useColorScheme,\n  getInitColorSchemeScript: deprecatedGetInitColorSchemeScript\n} = createCssVarsProvider({\n  themeId: THEME_ID,\n  // @ts-ignore ignore module augmentation tests\n  theme: () => createTheme({\n    cssVariables: true\n  }),\n  colorSchemeStorageKey: defaultConfig.colorSchemeStorageKey,\n  modeStorageKey: defaultConfig.modeStorageKey,\n  defaultColorScheme: {\n    light: defaultConfig.defaultLightColorScheme,\n    dark: defaultConfig.defaultDarkColorScheme\n  },\n  resolveTheme: theme => {\n    const newTheme = {\n      ...theme,\n      typography: createTypography(theme.palette, theme.typography)\n    };\n    newTheme.unstable_sx = function sx(props) {\n      return styleFunctionSx({\n        sx: props,\n        theme: this\n      });\n    };\n    return newTheme;\n  }\n});\nlet warnedOnce = false;\n\n// TODO: remove in v7\n// eslint-disable-next-line @typescript-eslint/naming-convention\nfunction Experimental_CssVarsProvider(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnce) {\n      console.warn(['MUI: The Experimental_CssVarsProvider component has been ported into ThemeProvider.', '', \"You should use `import { ThemeProvider } from '@mui/material/styles'` instead.\", 'For more details, check out https://mui.com/material-ui/customization/css-theme-variables/usage/'].join('\\n'));\n      warnedOnce = true;\n    }\n  }\n  return /*#__PURE__*/_jsx(InternalCssVarsProvider, {\n    ...props\n  });\n}\nlet warnedInitScriptOnce = false;\n\n// TODO: remove in v7\nconst getInitColorSchemeScript = params => {\n  if (!warnedInitScriptOnce) {\n    console.warn(['MUI: The getInitColorSchemeScript function has been deprecated.', '', \"You should use `import InitColorSchemeScript from '@mui/material/InitColorSchemeScript'`\", 'and replace the function call with `<InitColorSchemeScript />` instead.'].join('\\n'));\n    warnedInitScriptOnce = true;\n  }\n  return deprecatedGetInitColorSchemeScript(params);\n};\n\n/**\n * TODO: remove this export in v7\n * @deprecated\n * The `CssVarsProvider` component has been deprecated and ported into `ThemeProvider`.\n *\n * You should use `ThemeProvider` and `createTheme()` instead:\n *\n * ```diff\n * - import { CssVarsProvider, extendTheme } from '@mui/material/styles';\n * + import { ThemeProvider, createTheme } from '@mui/material/styles';\n *\n * - const theme = extendTheme();\n * + const theme = createTheme({\n * +   cssVariables: true,\n * +   colorSchemes: { light: true, dark: true },\n * + });\n *\n * - <CssVarsProvider theme={theme}>\n * + <ThemeProvider theme={theme}>\n * ```\n *\n * To see the full documentation, check out https://mui.com/material-ui/customization/css-theme-variables/usage/.\n */\nexport const CssVarsProvider = InternalCssVarsProvider;\nexport { useColorScheme, getInitColorSchemeScript, Experimental_CssVarsProvider };", "map": {"version": 3, "names": ["React", "styleFunctionSx", "unstable_createCssVarsProvider", "createCssVarsProvider", "createTheme", "createTypography", "THEME_ID", "defaultConfig", "jsx", "_jsx", "CssVarsProvider", "InternalCssVarsProvider", "useColorScheme", "getInitColorSchemeScript", "deprecatedGetInitColorSchemeScript", "themeId", "theme", "cssVariables", "colorSchemeStorageKey", "modeStorageKey", "defaultColorScheme", "light", "defaultLightColorScheme", "dark", "defaultDarkColorScheme", "resolveTheme", "newTheme", "typography", "palette", "unstable_sx", "sx", "props", "warnedOnce", "Experimental_CssVarsProvider", "process", "env", "NODE_ENV", "console", "warn", "join", "warnedInitScriptOnce", "params"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/styles/ThemeProviderWithVars.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport styleFunctionSx from '@mui/system/styleFunctionSx';\nimport { unstable_createCssVarsProvider as createCssVarsProvider } from '@mui/system';\nimport createTheme from \"./createTheme.js\";\nimport createTypography from \"./createTypography.js\";\nimport THEME_ID from \"./identifier.js\";\nimport { defaultConfig } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst {\n  CssVarsProvider: InternalCssVarsProvider,\n  useColorScheme,\n  getInitColorSchemeScript: deprecatedGetInitColorSchemeScript\n} = createCssVarsProvider({\n  themeId: THEME_ID,\n  // @ts-ignore ignore module augmentation tests\n  theme: () => createTheme({\n    cssVariables: true\n  }),\n  colorSchemeStorageKey: defaultConfig.colorSchemeStorageKey,\n  modeStorageKey: defaultConfig.modeStorageKey,\n  defaultColorScheme: {\n    light: defaultConfig.defaultLightColorScheme,\n    dark: defaultConfig.defaultDarkColorScheme\n  },\n  resolveTheme: theme => {\n    const newTheme = {\n      ...theme,\n      typography: createTypography(theme.palette, theme.typography)\n    };\n    newTheme.unstable_sx = function sx(props) {\n      return styleFunctionSx({\n        sx: props,\n        theme: this\n      });\n    };\n    return newTheme;\n  }\n});\nlet warnedOnce = false;\n\n// TODO: remove in v7\n// eslint-disable-next-line @typescript-eslint/naming-convention\nfunction Experimental_CssVarsProvider(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnce) {\n      console.warn(['MUI: The Experimental_CssVarsProvider component has been ported into ThemeProvider.', '', \"You should use `import { ThemeProvider } from '@mui/material/styles'` instead.\", 'For more details, check out https://mui.com/material-ui/customization/css-theme-variables/usage/'].join('\\n'));\n      warnedOnce = true;\n    }\n  }\n  return /*#__PURE__*/_jsx(InternalCssVarsProvider, {\n    ...props\n  });\n}\nlet warnedInitScriptOnce = false;\n\n// TODO: remove in v7\nconst getInitColorSchemeScript = params => {\n  if (!warnedInitScriptOnce) {\n    console.warn(['MUI: The getInitColorSchemeScript function has been deprecated.', '', \"You should use `import InitColorSchemeScript from '@mui/material/InitColorSchemeScript'`\", 'and replace the function call with `<InitColorSchemeScript />` instead.'].join('\\n'));\n    warnedInitScriptOnce = true;\n  }\n  return deprecatedGetInitColorSchemeScript(params);\n};\n\n/**\n * TODO: remove this export in v7\n * @deprecated\n * The `CssVarsProvider` component has been deprecated and ported into `ThemeProvider`.\n *\n * You should use `ThemeProvider` and `createTheme()` instead:\n *\n * ```diff\n * - import { CssVarsProvider, extendTheme } from '@mui/material/styles';\n * + import { ThemeProvider, createTheme } from '@mui/material/styles';\n *\n * - const theme = extendTheme();\n * + const theme = createTheme({\n * +   cssVariables: true,\n * +   colorSchemes: { light: true, dark: true },\n * + });\n *\n * - <CssVarsProvider theme={theme}>\n * + <ThemeProvider theme={theme}>\n * ```\n *\n * To see the full documentation, check out https://mui.com/material-ui/customization/css-theme-variables/usage/.\n */\nexport const CssVarsProvider = InternalCssVarsProvider;\nexport { useColorScheme, getInitColorSchemeScript, Experimental_CssVarsProvider };"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,8BAA8B,IAAIC,qBAAqB,QAAQ,aAAa;AACrF,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,aAAa,QAAQ,mDAAmD;AACjF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAM;EACJC,eAAe,EAAEC,uBAAuB;EACxCC,cAAc;EACdC,wBAAwB,EAAEC;AAC5B,CAAC,GAAGX,qBAAqB,CAAC;EACxBY,OAAO,EAAET,QAAQ;EACjB;EACAU,KAAK,EAAEA,CAAA,KAAMZ,WAAW,CAAC;IACvBa,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,qBAAqB,EAAEX,aAAa,CAACW,qBAAqB;EAC1DC,cAAc,EAAEZ,aAAa,CAACY,cAAc;EAC5CC,kBAAkB,EAAE;IAClBC,KAAK,EAAEd,aAAa,CAACe,uBAAuB;IAC5CC,IAAI,EAAEhB,aAAa,CAACiB;EACtB,CAAC;EACDC,YAAY,EAAET,KAAK,IAAI;IACrB,MAAMU,QAAQ,GAAG;MACf,GAAGV,KAAK;MACRW,UAAU,EAAEtB,gBAAgB,CAACW,KAAK,CAACY,OAAO,EAAEZ,KAAK,CAACW,UAAU;IAC9D,CAAC;IACDD,QAAQ,CAACG,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;MACxC,OAAO9B,eAAe,CAAC;QACrB6B,EAAE,EAAEC,KAAK;QACTf,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC;IACD,OAAOU,QAAQ;EACjB;AACF,CAAC,CAAC;AACF,IAAIM,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA,SAASC,4BAA4BA,CAACF,KAAK,EAAE;EAC3C,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACJ,UAAU,EAAE;MACfK,OAAO,CAACC,IAAI,CAAC,CAAC,qFAAqF,EAAE,EAAE,EAAE,gFAAgF,EAAE,kGAAkG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1SP,UAAU,GAAG,IAAI;IACnB;EACF;EACA,OAAO,aAAavB,IAAI,CAACE,uBAAuB,EAAE;IAChD,GAAGoB;EACL,CAAC,CAAC;AACJ;AACA,IAAIS,oBAAoB,GAAG,KAAK;;AAEhC;AACA,MAAM3B,wBAAwB,GAAG4B,MAAM,IAAI;EACzC,IAAI,CAACD,oBAAoB,EAAE;IACzBH,OAAO,CAACC,IAAI,CAAC,CAAC,iEAAiE,EAAE,EAAE,EAAE,0FAA0F,EAAE,yEAAyE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvQC,oBAAoB,GAAG,IAAI;EAC7B;EACA,OAAO1B,kCAAkC,CAAC2B,MAAM,CAAC;AACnD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM/B,eAAe,GAAGC,uBAAuB;AACtD,SAASC,cAAc,EAAEC,wBAAwB,EAAEoB,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}