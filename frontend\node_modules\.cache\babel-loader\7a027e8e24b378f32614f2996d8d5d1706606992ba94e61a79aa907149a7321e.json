{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownDivider = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    className,\n    bsPrefix,\n    as: Component = 'hr',\n    role = 'separator',\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'dropdown-divider');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    role: role,\n    ...props\n  });\n});\nDropdownDivider.displayName = 'DropdownDivider';\nexport default DropdownDivider;", "map": {"version": 3, "names": ["React", "classNames", "useBootstrapPrefix", "jsx", "_jsx", "DropdownDivider", "forwardRef", "_ref", "ref", "className", "bsPrefix", "as", "Component", "role", "props", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/DropdownDivider.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownDivider = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'hr',\n  role = 'separator',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'dropdown-divider');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    role: role,\n    ...props\n  });\n});\nDropdownDivider.displayName = 'DropdownDivider';\nexport default DropdownDivider;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,eAAe,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAAC,IAAA,EAMnDC,GAAG,KAAK;EAAA,IAN4C;IACrDC,SAAS;IACTC,QAAQ;IACRC,EAAE,EAAEC,SAAS,GAAG,IAAI;IACpBC,IAAI,GAAG,WAAW;IAClB,GAAGC;EACL,CAAC,GAAAP,IAAA;EACCG,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,OAAO,aAAaN,IAAI,CAACQ,SAAS,EAAE;IAClCJ,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAEC,QAAQ,CAAC;IAC1CG,IAAI,EAAEA,IAAI;IACV,GAAGC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,eAAe,CAACU,WAAW,GAAG,iBAAiB;AAC/C,eAAeV,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}