{"ast": null, "code": "export { default } from \"./Paper.js\";\nexport { default as paperClasses } from \"./paperClasses.js\";\nexport * from \"./paperClasses.js\";", "map": {"version": 3, "names": ["default", "paperClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Paper/index.js"], "sourcesContent": ["export { default } from \"./Paper.js\";\nexport { default as paperClasses } from \"./paperClasses.js\";\nexport * from \"./paperClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASA,OAAO,IAAIC,YAAY,QAAQ,mBAAmB;AAC3D,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}