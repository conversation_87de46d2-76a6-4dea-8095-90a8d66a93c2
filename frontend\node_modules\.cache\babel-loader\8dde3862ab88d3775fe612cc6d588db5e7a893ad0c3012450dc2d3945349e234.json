{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport usePreviousProps from '@mui/utils/usePreviousProps';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useBadge from \"./useBadge.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport badgeClasses, { getBadgeUtilityClass } from \"./badgeClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst RADIUS_STANDARD = 10;\nconst RADIUS_DOT = 4;\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    anchorOrigin,\n    invisible,\n    overlap,\n    variant,\n    classes = {}\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    badge: ['badge', variant, invisible && 'invisible', `anchorOrigin${capitalize(anchorOrigin.vertical)}${capitalize(anchorOrigin.horizontal)}`, `anchorOrigin${capitalize(anchorOrigin.vertical)}${capitalize(anchorOrigin.horizontal)}${capitalize(overlap)}`, `overlap${capitalize(overlap)}`, color !== 'default' && `color${capitalize(color)}`]\n  };\n  return composeClasses(slots, getBadgeUtilityClass, classes);\n};\nconst BadgeRoot = styled('span', {\n  name: 'MuiBadge',\n  slot: 'Root'\n})({\n  position: 'relative',\n  display: 'inline-flex',\n  // For correct alignment with the text.\n  verticalAlign: 'middle',\n  flexShrink: 0\n});\nconst BadgeBadge = styled('span', {\n  name: 'MuiBadge',\n  slot: 'Badge',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.badge, styles[ownerState.variant], styles[`anchorOrigin${capitalize(ownerState.anchorOrigin.vertical)}${capitalize(ownerState.anchorOrigin.horizontal)}${capitalize(ownerState.overlap)}`], ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`], ownerState.invisible && styles.invisible];\n  }\n})(memoTheme(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    display: 'flex',\n    flexDirection: 'row',\n    flexWrap: 'wrap',\n    justifyContent: 'center',\n    alignContent: 'center',\n    alignItems: 'center',\n    position: 'absolute',\n    boxSizing: 'border-box',\n    fontFamily: theme.typography.fontFamily,\n    fontWeight: theme.typography.fontWeightMedium,\n    fontSize: theme.typography.pxToRem(12),\n    minWidth: RADIUS_STANDARD * 2,\n    lineHeight: 1,\n    padding: '0 6px',\n    height: RADIUS_STANDARD * 2,\n    borderRadius: RADIUS_STANDARD,\n    zIndex: 1,\n    // Render the badge on top of potential ripples.\n    transition: theme.transitions.create('transform', {\n      easing: theme.transitions.easing.easeInOut,\n      duration: theme.transitions.duration.enteringScreen\n    }),\n    variants: [...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['contrastText'])).map(_ref2 => {\n      let [color] = _ref2;\n      return {\n        props: {\n          color\n        },\n        style: {\n          backgroundColor: (theme.vars || theme).palette[color].main,\n          color: (theme.vars || theme).palette[color].contrastText\n        }\n      };\n    }), {\n      props: {\n        variant: 'dot'\n      },\n      style: {\n        borderRadius: RADIUS_DOT,\n        height: RADIUS_DOT * 2,\n        minWidth: RADIUS_DOT * 2,\n        padding: 0\n      }\n    }, {\n      props: _ref3 => {\n        let {\n          ownerState\n        } = _ref3;\n        return ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'rectangular';\n      },\n      style: {\n        top: 0,\n        right: 0,\n        transform: 'scale(1) translate(50%, -50%)',\n        transformOrigin: '100% 0%',\n        [`&.${badgeClasses.invisible}`]: {\n          transform: 'scale(0) translate(50%, -50%)'\n        }\n      }\n    }, {\n      props: _ref4 => {\n        let {\n          ownerState\n        } = _ref4;\n        return ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'rectangular';\n      },\n      style: {\n        bottom: 0,\n        right: 0,\n        transform: 'scale(1) translate(50%, 50%)',\n        transformOrigin: '100% 100%',\n        [`&.${badgeClasses.invisible}`]: {\n          transform: 'scale(0) translate(50%, 50%)'\n        }\n      }\n    }, {\n      props: _ref5 => {\n        let {\n          ownerState\n        } = _ref5;\n        return ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'rectangular';\n      },\n      style: {\n        top: 0,\n        left: 0,\n        transform: 'scale(1) translate(-50%, -50%)',\n        transformOrigin: '0% 0%',\n        [`&.${badgeClasses.invisible}`]: {\n          transform: 'scale(0) translate(-50%, -50%)'\n        }\n      }\n    }, {\n      props: _ref6 => {\n        let {\n          ownerState\n        } = _ref6;\n        return ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'rectangular';\n      },\n      style: {\n        bottom: 0,\n        left: 0,\n        transform: 'scale(1) translate(-50%, 50%)',\n        transformOrigin: '0% 100%',\n        [`&.${badgeClasses.invisible}`]: {\n          transform: 'scale(0) translate(-50%, 50%)'\n        }\n      }\n    }, {\n      props: _ref7 => {\n        let {\n          ownerState\n        } = _ref7;\n        return ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'circular';\n      },\n      style: {\n        top: '14%',\n        right: '14%',\n        transform: 'scale(1) translate(50%, -50%)',\n        transformOrigin: '100% 0%',\n        [`&.${badgeClasses.invisible}`]: {\n          transform: 'scale(0) translate(50%, -50%)'\n        }\n      }\n    }, {\n      props: _ref8 => {\n        let {\n          ownerState\n        } = _ref8;\n        return ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'circular';\n      },\n      style: {\n        bottom: '14%',\n        right: '14%',\n        transform: 'scale(1) translate(50%, 50%)',\n        transformOrigin: '100% 100%',\n        [`&.${badgeClasses.invisible}`]: {\n          transform: 'scale(0) translate(50%, 50%)'\n        }\n      }\n    }, {\n      props: _ref9 => {\n        let {\n          ownerState\n        } = _ref9;\n        return ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'circular';\n      },\n      style: {\n        top: '14%',\n        left: '14%',\n        transform: 'scale(1) translate(-50%, -50%)',\n        transformOrigin: '0% 0%',\n        [`&.${badgeClasses.invisible}`]: {\n          transform: 'scale(0) translate(-50%, -50%)'\n        }\n      }\n    }, {\n      props: _ref0 => {\n        let {\n          ownerState\n        } = _ref0;\n        return ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'circular';\n      },\n      style: {\n        bottom: '14%',\n        left: '14%',\n        transform: 'scale(1) translate(-50%, 50%)',\n        transformOrigin: '0% 100%',\n        [`&.${badgeClasses.invisible}`]: {\n          transform: 'scale(0) translate(-50%, 50%)'\n        }\n      }\n    }, {\n      props: {\n        invisible: true\n      },\n      style: {\n        transition: theme.transitions.create('transform', {\n          easing: theme.transitions.easing.easeInOut,\n          duration: theme.transitions.duration.leavingScreen\n        })\n      }\n    }]\n  };\n}));\nfunction getAnchorOrigin(anchorOrigin) {\n  return {\n    vertical: anchorOrigin?.vertical ?? 'top',\n    horizontal: anchorOrigin?.horizontal ?? 'right'\n  };\n}\nconst Badge = /*#__PURE__*/React.forwardRef(function Badge(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBadge'\n  });\n  const {\n    anchorOrigin: anchorOriginProp,\n    className,\n    classes: classesProp,\n    component,\n    components = {},\n    componentsProps = {},\n    children,\n    overlap: overlapProp = 'rectangular',\n    color: colorProp = 'default',\n    invisible: invisibleProp = false,\n    max: maxProp = 99,\n    badgeContent: badgeContentProp,\n    slots,\n    slotProps,\n    showZero = false,\n    variant: variantProp = 'standard',\n    ...other\n  } = props;\n  const {\n    badgeContent,\n    invisible: invisibleFromHook,\n    max,\n    displayValue: displayValueFromHook\n  } = useBadge({\n    max: maxProp,\n    invisible: invisibleProp,\n    badgeContent: badgeContentProp,\n    showZero\n  });\n  const prevProps = usePreviousProps({\n    anchorOrigin: getAnchorOrigin(anchorOriginProp),\n    color: colorProp,\n    overlap: overlapProp,\n    variant: variantProp,\n    badgeContent: badgeContentProp\n  });\n  const invisible = invisibleFromHook || badgeContent == null && variantProp !== 'dot';\n  const {\n    color = colorProp,\n    overlap = overlapProp,\n    anchorOrigin: anchorOriginPropProp,\n    variant = variantProp\n  } = invisible ? prevProps : props;\n  const anchorOrigin = getAnchorOrigin(anchorOriginPropProp);\n  const displayValue = variant !== 'dot' ? displayValueFromHook : undefined;\n  const ownerState = {\n    ...props,\n    badgeContent,\n    invisible,\n    max,\n    displayValue,\n    showZero,\n    anchorOrigin,\n    color,\n    overlap,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n\n  // support both `slots` and `components` for backward compatibility\n  const externalForwardedProps = {\n    slots: {\n      root: slots?.root ?? components.Root,\n      badge: slots?.badge ?? components.Badge\n    },\n    slotProps: {\n      root: slotProps?.root ?? componentsProps.root,\n      badge: slotProps?.badge ?? componentsProps.badge\n    }\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: BadgeRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    className: clsx(classes.root, className),\n    ref,\n    additionalProps: {\n      as: component\n    }\n  });\n  const [BadgeSlot, badgeProps] = useSlot('badge', {\n    elementType: BadgeBadge,\n    externalForwardedProps,\n    ownerState,\n    className: classes.badge\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootProps,\n    children: [children, /*#__PURE__*/_jsx(BadgeSlot, {\n      ...badgeProps,\n      children: displayValue\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Badge.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The anchor of the badge.\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'right',\n   * }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOf(['left', 'right']),\n    vertical: PropTypes.oneOf(['bottom', 'top'])\n  }),\n  /**\n   * The content rendered within the badge.\n   */\n  badgeContent: PropTypes.node,\n  /**\n   * The badge will be added relative to this node.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Badge: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the badge is invisible.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * Max count to show.\n   * @default 99\n   */\n  max: PropTypes.number,\n  /**\n   * Wrapped shape the badge should overlap.\n   * @default 'rectangular'\n   */\n  overlap: PropTypes.oneOf(['circular', 'rectangular']),\n  /**\n   * Controls whether the badge is hidden when `badgeContent` is zero.\n   * @default false\n   */\n  showZero: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    badge: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'standard'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['dot', 'standard']), PropTypes.string])\n} : void 0;\nexport default Badge;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "usePreviousProps", "composeClasses", "useBadge", "styled", "memoTheme", "createSimplePaletteValueFilter", "useDefaultProps", "capitalize", "badgeClasses", "getBadgeUtilityClass", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "RADIUS_STANDARD", "RADIUS_DOT", "useUtilityClasses", "ownerState", "color", "anchor<PERSON><PERSON><PERSON>", "invisible", "overlap", "variant", "classes", "slots", "root", "badge", "vertical", "horizontal", "BadgeRoot", "name", "slot", "position", "display", "verticalAlign", "flexShrink", "BadgeBadge", "overridesResolver", "props", "styles", "_ref", "theme", "flexDirection", "flexWrap", "justifyContent", "align<PERSON><PERSON><PERSON>", "alignItems", "boxSizing", "fontFamily", "typography", "fontWeight", "fontWeightMedium", "fontSize", "pxToRem", "min<PERSON><PERSON><PERSON>", "lineHeight", "padding", "height", "borderRadius", "zIndex", "transition", "transitions", "create", "easing", "easeInOut", "duration", "enteringScreen", "variants", "Object", "entries", "palette", "filter", "map", "_ref2", "style", "backgroundColor", "vars", "main", "contrastText", "_ref3", "top", "right", "transform", "transform<PERSON><PERSON>in", "_ref4", "bottom", "_ref5", "left", "_ref6", "_ref7", "_ref8", "_ref9", "_ref0", "leavingScreen", "getAnchor<PERSON><PERSON>in", "Badge", "forwardRef", "inProps", "ref", "anchorOriginProp", "className", "classesProp", "component", "components", "componentsProps", "children", "overlapProp", "colorProp", "invisibleProp", "max", "maxProp", "badgeContent", "badgeContentProp", "slotProps", "showZero", "variantProp", "other", "invisibleFromHook", "displayValue", "displayValueFromHook", "prevProps", "anchorOriginPropProp", "undefined", "externalForwardedProps", "Root", "RootSlot", "rootProps", "elementType", "additionalProps", "as", "BadgeSlot", "badgeProps", "process", "env", "NODE_ENV", "propTypes", "shape", "oneOf", "node", "object", "string", "oneOfType", "func", "bool", "number", "sx", "arrayOf"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Badge/Badge.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport usePreviousProps from '@mui/utils/usePreviousProps';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useBadge from \"./useBadge.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport badgeClasses, { getBadgeUtilityClass } from \"./badgeClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst RADIUS_STANDARD = 10;\nconst RADIUS_DOT = 4;\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    anchorOrigin,\n    invisible,\n    overlap,\n    variant,\n    classes = {}\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    badge: ['badge', variant, invisible && 'invisible', `anchorOrigin${capitalize(anchorOrigin.vertical)}${capitalize(anchorOrigin.horizontal)}`, `anchorOrigin${capitalize(anchorOrigin.vertical)}${capitalize(anchorOrigin.horizontal)}${capitalize(overlap)}`, `overlap${capitalize(overlap)}`, color !== 'default' && `color${capitalize(color)}`]\n  };\n  return composeClasses(slots, getBadgeUtilityClass, classes);\n};\nconst BadgeRoot = styled('span', {\n  name: 'MuiBadge',\n  slot: 'Root'\n})({\n  position: 'relative',\n  display: 'inline-flex',\n  // For correct alignment with the text.\n  verticalAlign: 'middle',\n  flexShrink: 0\n});\nconst BadgeBadge = styled('span', {\n  name: 'MuiBadge',\n  slot: 'Badge',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.badge, styles[ownerState.variant], styles[`anchorOrigin${capitalize(ownerState.anchorOrigin.vertical)}${capitalize(ownerState.anchorOrigin.horizontal)}${capitalize(ownerState.overlap)}`], ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`], ownerState.invisible && styles.invisible];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  flexDirection: 'row',\n  flexWrap: 'wrap',\n  justifyContent: 'center',\n  alignContent: 'center',\n  alignItems: 'center',\n  position: 'absolute',\n  boxSizing: 'border-box',\n  fontFamily: theme.typography.fontFamily,\n  fontWeight: theme.typography.fontWeightMedium,\n  fontSize: theme.typography.pxToRem(12),\n  minWidth: RADIUS_STANDARD * 2,\n  lineHeight: 1,\n  padding: '0 6px',\n  height: RADIUS_STANDARD * 2,\n  borderRadius: RADIUS_STANDARD,\n  zIndex: 1,\n  // Render the badge on top of potential ripples.\n  transition: theme.transitions.create('transform', {\n    easing: theme.transitions.easing.easeInOut,\n    duration: theme.transitions.duration.enteringScreen\n  }),\n  variants: [...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['contrastText'])).map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette[color].main,\n      color: (theme.vars || theme).palette[color].contrastText\n    }\n  })), {\n    props: {\n      variant: 'dot'\n    },\n    style: {\n      borderRadius: RADIUS_DOT,\n      height: RADIUS_DOT * 2,\n      minWidth: RADIUS_DOT * 2,\n      padding: 0\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'rectangular',\n    style: {\n      top: 0,\n      right: 0,\n      transform: 'scale(1) translate(50%, -50%)',\n      transformOrigin: '100% 0%',\n      [`&.${badgeClasses.invisible}`]: {\n        transform: 'scale(0) translate(50%, -50%)'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'rectangular',\n    style: {\n      bottom: 0,\n      right: 0,\n      transform: 'scale(1) translate(50%, 50%)',\n      transformOrigin: '100% 100%',\n      [`&.${badgeClasses.invisible}`]: {\n        transform: 'scale(0) translate(50%, 50%)'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'rectangular',\n    style: {\n      top: 0,\n      left: 0,\n      transform: 'scale(1) translate(-50%, -50%)',\n      transformOrigin: '0% 0%',\n      [`&.${badgeClasses.invisible}`]: {\n        transform: 'scale(0) translate(-50%, -50%)'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'rectangular',\n    style: {\n      bottom: 0,\n      left: 0,\n      transform: 'scale(1) translate(-50%, 50%)',\n      transformOrigin: '0% 100%',\n      [`&.${badgeClasses.invisible}`]: {\n        transform: 'scale(0) translate(-50%, 50%)'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'circular',\n    style: {\n      top: '14%',\n      right: '14%',\n      transform: 'scale(1) translate(50%, -50%)',\n      transformOrigin: '100% 0%',\n      [`&.${badgeClasses.invisible}`]: {\n        transform: 'scale(0) translate(50%, -50%)'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'right' && ownerState.overlap === 'circular',\n    style: {\n      bottom: '14%',\n      right: '14%',\n      transform: 'scale(1) translate(50%, 50%)',\n      transformOrigin: '100% 100%',\n      [`&.${badgeClasses.invisible}`]: {\n        transform: 'scale(0) translate(50%, 50%)'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.anchorOrigin.vertical === 'top' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'circular',\n    style: {\n      top: '14%',\n      left: '14%',\n      transform: 'scale(1) translate(-50%, -50%)',\n      transformOrigin: '0% 0%',\n      [`&.${badgeClasses.invisible}`]: {\n        transform: 'scale(0) translate(-50%, -50%)'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.anchorOrigin.vertical === 'bottom' && ownerState.anchorOrigin.horizontal === 'left' && ownerState.overlap === 'circular',\n    style: {\n      bottom: '14%',\n      left: '14%',\n      transform: 'scale(1) translate(-50%, 50%)',\n      transformOrigin: '0% 100%',\n      [`&.${badgeClasses.invisible}`]: {\n        transform: 'scale(0) translate(-50%, 50%)'\n      }\n    }\n  }, {\n    props: {\n      invisible: true\n    },\n    style: {\n      transition: theme.transitions.create('transform', {\n        easing: theme.transitions.easing.easeInOut,\n        duration: theme.transitions.duration.leavingScreen\n      })\n    }\n  }]\n})));\nfunction getAnchorOrigin(anchorOrigin) {\n  return {\n    vertical: anchorOrigin?.vertical ?? 'top',\n    horizontal: anchorOrigin?.horizontal ?? 'right'\n  };\n}\nconst Badge = /*#__PURE__*/React.forwardRef(function Badge(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBadge'\n  });\n  const {\n    anchorOrigin: anchorOriginProp,\n    className,\n    classes: classesProp,\n    component,\n    components = {},\n    componentsProps = {},\n    children,\n    overlap: overlapProp = 'rectangular',\n    color: colorProp = 'default',\n    invisible: invisibleProp = false,\n    max: maxProp = 99,\n    badgeContent: badgeContentProp,\n    slots,\n    slotProps,\n    showZero = false,\n    variant: variantProp = 'standard',\n    ...other\n  } = props;\n  const {\n    badgeContent,\n    invisible: invisibleFromHook,\n    max,\n    displayValue: displayValueFromHook\n  } = useBadge({\n    max: maxProp,\n    invisible: invisibleProp,\n    badgeContent: badgeContentProp,\n    showZero\n  });\n  const prevProps = usePreviousProps({\n    anchorOrigin: getAnchorOrigin(anchorOriginProp),\n    color: colorProp,\n    overlap: overlapProp,\n    variant: variantProp,\n    badgeContent: badgeContentProp\n  });\n  const invisible = invisibleFromHook || badgeContent == null && variantProp !== 'dot';\n  const {\n    color = colorProp,\n    overlap = overlapProp,\n    anchorOrigin: anchorOriginPropProp,\n    variant = variantProp\n  } = invisible ? prevProps : props;\n  const anchorOrigin = getAnchorOrigin(anchorOriginPropProp);\n  const displayValue = variant !== 'dot' ? displayValueFromHook : undefined;\n  const ownerState = {\n    ...props,\n    badgeContent,\n    invisible,\n    max,\n    displayValue,\n    showZero,\n    anchorOrigin,\n    color,\n    overlap,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n\n  // support both `slots` and `components` for backward compatibility\n  const externalForwardedProps = {\n    slots: {\n      root: slots?.root ?? components.Root,\n      badge: slots?.badge ?? components.Badge\n    },\n    slotProps: {\n      root: slotProps?.root ?? componentsProps.root,\n      badge: slotProps?.badge ?? componentsProps.badge\n    }\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: BadgeRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    className: clsx(classes.root, className),\n    ref,\n    additionalProps: {\n      as: component\n    }\n  });\n  const [BadgeSlot, badgeProps] = useSlot('badge', {\n    elementType: BadgeBadge,\n    externalForwardedProps,\n    ownerState,\n    className: classes.badge\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootProps,\n    children: [children, /*#__PURE__*/_jsx(BadgeSlot, {\n      ...badgeProps,\n      children: displayValue\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Badge.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The anchor of the badge.\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'right',\n   * }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOf(['left', 'right']),\n    vertical: PropTypes.oneOf(['bottom', 'top'])\n  }),\n  /**\n   * The content rendered within the badge.\n   */\n  badgeContent: PropTypes.node,\n  /**\n   * The badge will be added relative to this node.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Badge: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the badge is invisible.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * Max count to show.\n   * @default 99\n   */\n  max: PropTypes.number,\n  /**\n   * Wrapped shape the badge should overlap.\n   * @default 'rectangular'\n   */\n  overlap: PropTypes.oneOf(['circular', 'rectangular']),\n  /**\n   * Controls whether the badge is hidden when `badgeContent` is zero.\n   * @default false\n   */\n  showZero: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    badge: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'standard'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['dot', 'standard']), PropTypes.string])\n} : void 0;\nexport default Badge;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,QAAQ,MAAM,eAAe;AACpC,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,8BAA8B,MAAM,4CAA4C;AACvF,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,IAAIC,oBAAoB,QAAQ,mBAAmB;AACtE,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,eAAe,GAAG,EAAE;AAC1B,MAAMC,UAAU,GAAG,CAAC;AACpB,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,KAAK;IACLC,YAAY;IACZC,SAAS;IACTC,OAAO;IACPC,OAAO;IACPC,OAAO,GAAG,CAAC;EACb,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,KAAK,EAAE,CAAC,OAAO,EAAEJ,OAAO,EAAEF,SAAS,IAAI,WAAW,EAAE,eAAed,UAAU,CAACa,YAAY,CAACQ,QAAQ,CAAC,GAAGrB,UAAU,CAACa,YAAY,CAACS,UAAU,CAAC,EAAE,EAAE,eAAetB,UAAU,CAACa,YAAY,CAACQ,QAAQ,CAAC,GAAGrB,UAAU,CAACa,YAAY,CAACS,UAAU,CAAC,GAAGtB,UAAU,CAACe,OAAO,CAAC,EAAE,EAAE,UAAUf,UAAU,CAACe,OAAO,CAAC,EAAE,EAAEH,KAAK,KAAK,SAAS,IAAI,QAAQZ,UAAU,CAACY,KAAK,CAAC,EAAE;EACnV,CAAC;EACD,OAAOlB,cAAc,CAACwB,KAAK,EAAEhB,oBAAoB,EAAEe,OAAO,CAAC;AAC7D,CAAC;AACD,MAAMM,SAAS,GAAG3B,MAAM,CAAC,MAAM,EAAE;EAC/B4B,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,aAAa;EACtB;EACAC,aAAa,EAAE,QAAQ;EACvBC,UAAU,EAAE;AACd,CAAC,CAAC;AACF,MAAMC,UAAU,GAAGlC,MAAM,CAAC,MAAM,EAAE;EAChC4B,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,OAAO;EACbM,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJtB;IACF,CAAC,GAAGqB,KAAK;IACT,OAAO,CAACC,MAAM,CAACb,KAAK,EAAEa,MAAM,CAACtB,UAAU,CAACK,OAAO,CAAC,EAAEiB,MAAM,CAAC,eAAejC,UAAU,CAACW,UAAU,CAACE,YAAY,CAACQ,QAAQ,CAAC,GAAGrB,UAAU,CAACW,UAAU,CAACE,YAAY,CAACS,UAAU,CAAC,GAAGtB,UAAU,CAACW,UAAU,CAACI,OAAO,CAAC,EAAE,CAAC,EAAEJ,UAAU,CAACC,KAAK,KAAK,SAAS,IAAIqB,MAAM,CAAC,QAAQjC,UAAU,CAACW,UAAU,CAACC,KAAK,CAAC,EAAE,CAAC,EAAED,UAAU,CAACG,SAAS,IAAImB,MAAM,CAACnB,SAAS,CAAC;EACxU;AACF,CAAC,CAAC,CAACjB,SAAS,CAACqC,IAAA;EAAA,IAAC;IACZC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLP,OAAO,EAAE,MAAM;IACfS,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,MAAM;IAChBC,cAAc,EAAE,QAAQ;IACxBC,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,QAAQ;IACpBd,QAAQ,EAAE,UAAU;IACpBe,SAAS,EAAE,YAAY;IACvBC,UAAU,EAAEP,KAAK,CAACQ,UAAU,CAACD,UAAU;IACvCE,UAAU,EAAET,KAAK,CAACQ,UAAU,CAACE,gBAAgB;IAC7CC,QAAQ,EAAEX,KAAK,CAACQ,UAAU,CAACI,OAAO,CAAC,EAAE,CAAC;IACtCC,QAAQ,EAAExC,eAAe,GAAG,CAAC;IAC7ByC,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,OAAO;IAChBC,MAAM,EAAE3C,eAAe,GAAG,CAAC;IAC3B4C,YAAY,EAAE5C,eAAe;IAC7B6C,MAAM,EAAE,CAAC;IACT;IACAC,UAAU,EAAEnB,KAAK,CAACoB,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;MAChDC,MAAM,EAAEtB,KAAK,CAACoB,WAAW,CAACE,MAAM,CAACC,SAAS;MAC1CC,QAAQ,EAAExB,KAAK,CAACoB,WAAW,CAACI,QAAQ,CAACC;IACvC,CAAC,CAAC;IACFC,QAAQ,EAAE,CAAC,GAAGC,MAAM,CAACC,OAAO,CAAC5B,KAAK,CAAC6B,OAAO,CAAC,CAACC,MAAM,CAACnE,8BAA8B,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAACoE,GAAG,CAACC,KAAA;MAAA,IAAC,CAACvD,KAAK,CAAC,GAAAuD,KAAA;MAAA,OAAM;QACrHnC,KAAK,EAAE;UACLpB;QACF,CAAC;QACDwD,KAAK,EAAE;UACLC,eAAe,EAAE,CAAClC,KAAK,CAACmC,IAAI,IAAInC,KAAK,EAAE6B,OAAO,CAACpD,KAAK,CAAC,CAAC2D,IAAI;UAC1D3D,KAAK,EAAE,CAACuB,KAAK,CAACmC,IAAI,IAAInC,KAAK,EAAE6B,OAAO,CAACpD,KAAK,CAAC,CAAC4D;QAC9C;MACF,CAAC;IAAA,CAAC,CAAC,EAAE;MACHxC,KAAK,EAAE;QACLhB,OAAO,EAAE;MACX,CAAC;MACDoD,KAAK,EAAE;QACLhB,YAAY,EAAE3C,UAAU;QACxB0C,MAAM,EAAE1C,UAAU,GAAG,CAAC;QACtBuC,QAAQ,EAAEvC,UAAU,GAAG,CAAC;QACxByC,OAAO,EAAE;MACX;IACF,CAAC,EAAE;MACDlB,KAAK,EAAEyC,KAAA;QAAA,IAAC;UACN9D;QACF,CAAC,GAAA8D,KAAA;QAAA,OAAK9D,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,KAAK,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,OAAO,IAAIX,UAAU,CAACI,OAAO,KAAK,aAAa;MAAA;MAC1IqD,KAAK,EAAE;QACLM,GAAG,EAAE,CAAC;QACNC,KAAK,EAAE,CAAC;QACRC,SAAS,EAAE,+BAA+B;QAC1CC,eAAe,EAAE,SAAS;QAC1B,CAAC,KAAK5E,YAAY,CAACa,SAAS,EAAE,GAAG;UAC/B8D,SAAS,EAAE;QACb;MACF;IACF,CAAC,EAAE;MACD5C,KAAK,EAAE8C,KAAA;QAAA,IAAC;UACNnE;QACF,CAAC,GAAAmE,KAAA;QAAA,OAAKnE,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,QAAQ,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,OAAO,IAAIX,UAAU,CAACI,OAAO,KAAK,aAAa;MAAA;MAC7IqD,KAAK,EAAE;QACLW,MAAM,EAAE,CAAC;QACTJ,KAAK,EAAE,CAAC;QACRC,SAAS,EAAE,8BAA8B;QACzCC,eAAe,EAAE,WAAW;QAC5B,CAAC,KAAK5E,YAAY,CAACa,SAAS,EAAE,GAAG;UAC/B8D,SAAS,EAAE;QACb;MACF;IACF,CAAC,EAAE;MACD5C,KAAK,EAAEgD,KAAA;QAAA,IAAC;UACNrE;QACF,CAAC,GAAAqE,KAAA;QAAA,OAAKrE,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,KAAK,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,MAAM,IAAIX,UAAU,CAACI,OAAO,KAAK,aAAa;MAAA;MACzIqD,KAAK,EAAE;QACLM,GAAG,EAAE,CAAC;QACNO,IAAI,EAAE,CAAC;QACPL,SAAS,EAAE,gCAAgC;QAC3CC,eAAe,EAAE,OAAO;QACxB,CAAC,KAAK5E,YAAY,CAACa,SAAS,EAAE,GAAG;UAC/B8D,SAAS,EAAE;QACb;MACF;IACF,CAAC,EAAE;MACD5C,KAAK,EAAEkD,KAAA;QAAA,IAAC;UACNvE;QACF,CAAC,GAAAuE,KAAA;QAAA,OAAKvE,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,QAAQ,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,MAAM,IAAIX,UAAU,CAACI,OAAO,KAAK,aAAa;MAAA;MAC5IqD,KAAK,EAAE;QACLW,MAAM,EAAE,CAAC;QACTE,IAAI,EAAE,CAAC;QACPL,SAAS,EAAE,+BAA+B;QAC1CC,eAAe,EAAE,SAAS;QAC1B,CAAC,KAAK5E,YAAY,CAACa,SAAS,EAAE,GAAG;UAC/B8D,SAAS,EAAE;QACb;MACF;IACF,CAAC,EAAE;MACD5C,KAAK,EAAEmD,KAAA;QAAA,IAAC;UACNxE;QACF,CAAC,GAAAwE,KAAA;QAAA,OAAKxE,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,KAAK,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,OAAO,IAAIX,UAAU,CAACI,OAAO,KAAK,UAAU;MAAA;MACvIqD,KAAK,EAAE;QACLM,GAAG,EAAE,KAAK;QACVC,KAAK,EAAE,KAAK;QACZC,SAAS,EAAE,+BAA+B;QAC1CC,eAAe,EAAE,SAAS;QAC1B,CAAC,KAAK5E,YAAY,CAACa,SAAS,EAAE,GAAG;UAC/B8D,SAAS,EAAE;QACb;MACF;IACF,CAAC,EAAE;MACD5C,KAAK,EAAEoD,KAAA;QAAA,IAAC;UACNzE;QACF,CAAC,GAAAyE,KAAA;QAAA,OAAKzE,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,QAAQ,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,OAAO,IAAIX,UAAU,CAACI,OAAO,KAAK,UAAU;MAAA;MAC1IqD,KAAK,EAAE;QACLW,MAAM,EAAE,KAAK;QACbJ,KAAK,EAAE,KAAK;QACZC,SAAS,EAAE,8BAA8B;QACzCC,eAAe,EAAE,WAAW;QAC5B,CAAC,KAAK5E,YAAY,CAACa,SAAS,EAAE,GAAG;UAC/B8D,SAAS,EAAE;QACb;MACF;IACF,CAAC,EAAE;MACD5C,KAAK,EAAEqD,KAAA;QAAA,IAAC;UACN1E;QACF,CAAC,GAAA0E,KAAA;QAAA,OAAK1E,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,KAAK,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,MAAM,IAAIX,UAAU,CAACI,OAAO,KAAK,UAAU;MAAA;MACtIqD,KAAK,EAAE;QACLM,GAAG,EAAE,KAAK;QACVO,IAAI,EAAE,KAAK;QACXL,SAAS,EAAE,gCAAgC;QAC3CC,eAAe,EAAE,OAAO;QACxB,CAAC,KAAK5E,YAAY,CAACa,SAAS,EAAE,GAAG;UAC/B8D,SAAS,EAAE;QACb;MACF;IACF,CAAC,EAAE;MACD5C,KAAK,EAAEsD,KAAA;QAAA,IAAC;UACN3E;QACF,CAAC,GAAA2E,KAAA;QAAA,OAAK3E,UAAU,CAACE,YAAY,CAACQ,QAAQ,KAAK,QAAQ,IAAIV,UAAU,CAACE,YAAY,CAACS,UAAU,KAAK,MAAM,IAAIX,UAAU,CAACI,OAAO,KAAK,UAAU;MAAA;MACzIqD,KAAK,EAAE;QACLW,MAAM,EAAE,KAAK;QACbE,IAAI,EAAE,KAAK;QACXL,SAAS,EAAE,+BAA+B;QAC1CC,eAAe,EAAE,SAAS;QAC1B,CAAC,KAAK5E,YAAY,CAACa,SAAS,EAAE,GAAG;UAC/B8D,SAAS,EAAE;QACb;MACF;IACF,CAAC,EAAE;MACD5C,KAAK,EAAE;QACLlB,SAAS,EAAE;MACb,CAAC;MACDsD,KAAK,EAAE;QACLd,UAAU,EAAEnB,KAAK,CAACoB,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;UAChDC,MAAM,EAAEtB,KAAK,CAACoB,WAAW,CAACE,MAAM,CAACC,SAAS;UAC1CC,QAAQ,EAAExB,KAAK,CAACoB,WAAW,CAACI,QAAQ,CAAC4B;QACvC,CAAC;MACH;IACF,CAAC;EACH,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,SAASC,eAAeA,CAAC3E,YAAY,EAAE;EACrC,OAAO;IACLQ,QAAQ,EAAER,YAAY,EAAEQ,QAAQ,IAAI,KAAK;IACzCC,UAAU,EAAET,YAAY,EAAES,UAAU,IAAI;EAC1C,CAAC;AACH;AACA,MAAMmE,KAAK,GAAG,aAAanG,KAAK,CAACoG,UAAU,CAAC,SAASD,KAAKA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvE,MAAM5D,KAAK,GAAGjC,eAAe,CAAC;IAC5BiC,KAAK,EAAE2D,OAAO;IACdnE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJX,YAAY,EAAEgF,gBAAgB;IAC9BC,SAAS;IACT7E,OAAO,EAAE8E,WAAW;IACpBC,SAAS;IACTC,UAAU,GAAG,CAAC,CAAC;IACfC,eAAe,GAAG,CAAC,CAAC;IACpBC,QAAQ;IACRpF,OAAO,EAAEqF,WAAW,GAAG,aAAa;IACpCxF,KAAK,EAAEyF,SAAS,GAAG,SAAS;IAC5BvF,SAAS,EAAEwF,aAAa,GAAG,KAAK;IAChCC,GAAG,EAAEC,OAAO,GAAG,EAAE;IACjBC,YAAY,EAAEC,gBAAgB;IAC9BxF,KAAK;IACLyF,SAAS;IACTC,QAAQ,GAAG,KAAK;IAChB5F,OAAO,EAAE6F,WAAW,GAAG,UAAU;IACjC,GAAGC;EACL,CAAC,GAAG9E,KAAK;EACT,MAAM;IACJyE,YAAY;IACZ3F,SAAS,EAAEiG,iBAAiB;IAC5BR,GAAG;IACHS,YAAY,EAAEC;EAChB,CAAC,GAAGtH,QAAQ,CAAC;IACX4G,GAAG,EAAEC,OAAO;IACZ1F,SAAS,EAAEwF,aAAa;IACxBG,YAAY,EAAEC,gBAAgB;IAC9BE;EACF,CAAC,CAAC;EACF,MAAMM,SAAS,GAAGzH,gBAAgB,CAAC;IACjCoB,YAAY,EAAE2E,eAAe,CAACK,gBAAgB,CAAC;IAC/CjF,KAAK,EAAEyF,SAAS;IAChBtF,OAAO,EAAEqF,WAAW;IACpBpF,OAAO,EAAE6F,WAAW;IACpBJ,YAAY,EAAEC;EAChB,CAAC,CAAC;EACF,MAAM5F,SAAS,GAAGiG,iBAAiB,IAAIN,YAAY,IAAI,IAAI,IAAII,WAAW,KAAK,KAAK;EACpF,MAAM;IACJjG,KAAK,GAAGyF,SAAS;IACjBtF,OAAO,GAAGqF,WAAW;IACrBvF,YAAY,EAAEsG,oBAAoB;IAClCnG,OAAO,GAAG6F;EACZ,CAAC,GAAG/F,SAAS,GAAGoG,SAAS,GAAGlF,KAAK;EACjC,MAAMnB,YAAY,GAAG2E,eAAe,CAAC2B,oBAAoB,CAAC;EAC1D,MAAMH,YAAY,GAAGhG,OAAO,KAAK,KAAK,GAAGiG,oBAAoB,GAAGG,SAAS;EACzE,MAAMzG,UAAU,GAAG;IACjB,GAAGqB,KAAK;IACRyE,YAAY;IACZ3F,SAAS;IACTyF,GAAG;IACHS,YAAY;IACZJ,QAAQ;IACR/F,YAAY;IACZD,KAAK;IACLG,OAAO;IACPC;EACF,CAAC;EACD,MAAMC,OAAO,GAAGP,iBAAiB,CAACC,UAAU,CAAC;;EAE7C;EACA,MAAM0G,sBAAsB,GAAG;IAC7BnG,KAAK,EAAE;MACLC,IAAI,EAAED,KAAK,EAAEC,IAAI,IAAI8E,UAAU,CAACqB,IAAI;MACpClG,KAAK,EAAEF,KAAK,EAAEE,KAAK,IAAI6E,UAAU,CAACR;IACpC,CAAC;IACDkB,SAAS,EAAE;MACTxF,IAAI,EAAEwF,SAAS,EAAExF,IAAI,IAAI+E,eAAe,CAAC/E,IAAI;MAC7CC,KAAK,EAAEuF,SAAS,EAAEvF,KAAK,IAAI8E,eAAe,CAAC9E;IAC7C;EACF,CAAC;EACD,MAAM,CAACmG,QAAQ,EAAEC,SAAS,CAAC,GAAGrH,OAAO,CAAC,MAAM,EAAE;IAC5CsH,WAAW,EAAElG,SAAS;IACtB8F,sBAAsB,EAAE;MACtB,GAAGA,sBAAsB;MACzB,GAAGP;IACL,CAAC;IACDnG,UAAU;IACVmF,SAAS,EAAEtG,IAAI,CAACyB,OAAO,CAACE,IAAI,EAAE2E,SAAS,CAAC;IACxCF,GAAG;IACH8B,eAAe,EAAE;MACfC,EAAE,EAAE3B;IACN;EACF,CAAC,CAAC;EACF,MAAM,CAAC4B,SAAS,EAAEC,UAAU,CAAC,GAAG1H,OAAO,CAAC,OAAO,EAAE;IAC/CsH,WAAW,EAAE3F,UAAU;IACvBuF,sBAAsB;IACtB1G,UAAU;IACVmF,SAAS,EAAE7E,OAAO,CAACG;EACrB,CAAC,CAAC;EACF,OAAO,aAAab,KAAK,CAACgH,QAAQ,EAAE;IAClC,GAAGC,SAAS;IACZrB,QAAQ,EAAE,CAACA,QAAQ,EAAE,aAAa9F,IAAI,CAACuH,SAAS,EAAE;MAChD,GAAGC,UAAU;MACb1B,QAAQ,EAAEa;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvC,KAAK,CAACwC,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEpH,YAAY,EAAEtB,SAAS,CAAC2I,KAAK,CAAC;IAC5B5G,UAAU,EAAE/B,SAAS,CAAC4I,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC9C9G,QAAQ,EAAE9B,SAAS,CAAC4I,KAAK,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC;EAC7C,CAAC,CAAC;EACF;AACF;AACA;EACE1B,YAAY,EAAElH,SAAS,CAAC6I,IAAI;EAC5B;AACF;AACA;EACEjC,QAAQ,EAAE5G,SAAS,CAAC6I,IAAI;EACxB;AACF;AACA;EACEnH,OAAO,EAAE1B,SAAS,CAAC8I,MAAM;EACzB;AACF;AACA;EACEvC,SAAS,EAAEvG,SAAS,CAAC+I,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACE1H,KAAK,EAAErB,SAAS,CAAC,sCAAsCgJ,SAAS,CAAC,CAAChJ,SAAS,CAAC4I,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE5I,SAAS,CAAC+I,MAAM,CAAC,CAAC;EACjL;AACF;AACA;AACA;EACEtC,SAAS,EAAEzG,SAAS,CAACkI,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;EACExB,UAAU,EAAE1G,SAAS,CAAC2I,KAAK,CAAC;IAC1BzC,KAAK,EAAElG,SAAS,CAACkI,WAAW;IAC5BH,IAAI,EAAE/H,SAAS,CAACkI;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEvB,eAAe,EAAE3G,SAAS,CAAC2I,KAAK,CAAC;IAC/B9G,KAAK,EAAE7B,SAAS,CAACgJ,SAAS,CAAC,CAAChJ,SAAS,CAACiJ,IAAI,EAAEjJ,SAAS,CAAC8I,MAAM,CAAC,CAAC;IAC9DlH,IAAI,EAAE5B,SAAS,CAACgJ,SAAS,CAAC,CAAChJ,SAAS,CAACiJ,IAAI,EAAEjJ,SAAS,CAAC8I,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEvH,SAAS,EAAEvB,SAAS,CAACkJ,IAAI;EACzB;AACF;AACA;AACA;EACElC,GAAG,EAAEhH,SAAS,CAACmJ,MAAM;EACrB;AACF;AACA;AACA;EACE3H,OAAO,EAAExB,SAAS,CAAC4I,KAAK,CAAC,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;EACrD;AACF;AACA;AACA;EACEvB,QAAQ,EAAErH,SAAS,CAACkJ,IAAI;EACxB;AACF;AACA;AACA;EACE9B,SAAS,EAAEpH,SAAS,CAAC2I,KAAK,CAAC;IACzB9G,KAAK,EAAE7B,SAAS,CAACgJ,SAAS,CAAC,CAAChJ,SAAS,CAACiJ,IAAI,EAAEjJ,SAAS,CAAC8I,MAAM,CAAC,CAAC;IAC9DlH,IAAI,EAAE5B,SAAS,CAACgJ,SAAS,CAAC,CAAChJ,SAAS,CAACiJ,IAAI,EAAEjJ,SAAS,CAAC8I,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEnH,KAAK,EAAE3B,SAAS,CAAC2I,KAAK,CAAC;IACrB9G,KAAK,EAAE7B,SAAS,CAACkI,WAAW;IAC5BtG,IAAI,EAAE5B,SAAS,CAACkI;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEkB,EAAE,EAAEpJ,SAAS,CAACgJ,SAAS,CAAC,CAAChJ,SAAS,CAACqJ,OAAO,CAACrJ,SAAS,CAACgJ,SAAS,CAAC,CAAChJ,SAAS,CAACiJ,IAAI,EAAEjJ,SAAS,CAAC8I,MAAM,EAAE9I,SAAS,CAACkJ,IAAI,CAAC,CAAC,CAAC,EAAElJ,SAAS,CAACiJ,IAAI,EAAEjJ,SAAS,CAAC8I,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACErH,OAAO,EAAEzB,SAAS,CAAC,sCAAsCgJ,SAAS,CAAC,CAAChJ,SAAS,CAAC4I,KAAK,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE5I,SAAS,CAAC+I,MAAM,CAAC;AAC7H,CAAC,GAAG,KAAK,CAAC;AACV,eAAe7C,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}