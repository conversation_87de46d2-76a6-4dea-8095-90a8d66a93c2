{"ast": null, "code": "import React from'react';import{Navbar as Bootstrap<PERSON><PERSON><PERSON>,Nav,NavDropdown,Container,But<PERSON>}from'react-bootstrap';import{Link,useNavigate}from'react-router-dom';import{useAuth}from'../../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Navbar=()=>{const{user,isAuthenticated,logout}=useAuth();const navigate=useNavigate();const handleLogout=async()=>{try{await logout();}catch(error){console.error('Logout error:',error);}};return/*#__PURE__*/_jsx(BootstrapNavbar,{bg:\"dark\",variant:\"dark\",expand:\"lg\",sticky:\"top\",children:/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(BootstrapNavbar.Brand,{as:Link,to:\"/\",className:\"text-decoration-none\",children:\"Full Stack CMS\"}),/*#__PURE__*/_jsx(BootstrapNavbar.Toggle,{\"aria-controls\":\"basic-navbar-nav\"}),/*#__PURE__*/_jsxs(BootstrapNavbar.Collapse,{id:\"basic-navbar-nav\",children:[/*#__PURE__*/_jsx(Nav,{className:\"me-auto\",children:/*#__PURE__*/_jsx(Nav.Link,{as:Link,to:\"/\",className:\"text-decoration-none\",children:\"Home\"})}),/*#__PURE__*/_jsx(Nav,{className:\"ms-auto\",children:isAuthenticated?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Nav.Link,{as:Link,to:\"/dashboard\",className:\"text-decoration-none\",children:\"Dashboard\"}),/*#__PURE__*/_jsxs(NavDropdown,{title:(user===null||user===void 0?void 0:user.name)||'User',id:\"user-dropdown\",align:\"end\",children:[/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>navigate('/profile'),children:\"Profile\"}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:()=>navigate('/profile/edit'),children:\"Edit Profile\"}),/*#__PURE__*/_jsx(NavDropdown.Divider,{}),/*#__PURE__*/_jsx(NavDropdown.Item,{onClick:handleLogout,children:\"Logout\"})]}),!(user!==null&&user!==void 0&&user.email_verified_at)&&/*#__PURE__*/_jsx(Button,{variant:\"outline-warning\",size:\"sm\",className:\"ms-2\",onClick:()=>navigate('/email-verification'),children:\"Verify Email\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Nav.Link,{as:Link,to:\"/login\",className:\"text-decoration-none\",children:\"Login\"}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",className:\"ms-2\",onClick:()=>navigate('/register'),children:\"Register\"})]})})]})]})});};export default Navbar;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "BootstrapNavbar", "Nav", "NavDropdown", "Container", "<PERSON><PERSON>", "Link", "useNavigate", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "user", "isAuthenticated", "logout", "navigate", "handleLogout", "error", "console", "bg", "variant", "expand", "sticky", "children", "Brand", "as", "to", "className", "Toggle", "Collapse", "id", "title", "name", "align", "<PERSON><PERSON>", "onClick", "Divider", "email_verified_at", "size"], "sources": ["C:/laragon/www/frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navbar as BootstrapN<PERSON>bar, Nav, NavDropdown, Container, But<PERSON> } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst Navbar: React.FC = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  return (\n    <BootstrapNavbar bg=\"dark\" variant=\"dark\" expand=\"lg\" sticky=\"top\">\n      <Container>\n        <BootstrapNavbar.Brand as={Link} to=\"/\" className=\"text-decoration-none\">\n          Full Stack CMS\n        </BootstrapNavbar.Brand>\n\n        <BootstrapNavbar.Toggle aria-controls=\"basic-navbar-nav\" />\n        <BootstrapNavbar.Collapse id=\"basic-navbar-nav\">\n          <Nav className=\"me-auto\">\n            <Nav.Link as={Link} to=\"/\" className=\"text-decoration-none\">\n              Home\n            </Nav.Link>\n          </Nav>\n\n          <Nav className=\"ms-auto\">\n            {isAuthenticated ? (\n              <>\n                <Nav.Link as={Link} to=\"/dashboard\" className=\"text-decoration-none\">\n                  Dashboard\n                </Nav.Link>\n                <NavDropdown\n                  title={user?.name || 'User'}\n                  id=\"user-dropdown\"\n                  align=\"end\"\n                >\n                  <NavDropdown.Item onClick={() => navigate('/profile')}>\n                    Profile\n                  </NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => navigate('/profile/edit')}>\n                    Edit Profile\n                  </NavDropdown.Item>\n                  <NavDropdown.Divider />\n                  <NavDropdown.Item onClick={handleLogout}>\n                    Logout\n                  </NavDropdown.Item>\n                </NavDropdown>\n\n                {!user?.email_verified_at && (\n                  <Button\n                    variant=\"outline-warning\"\n                    size=\"sm\"\n                    className=\"ms-2\"\n                    onClick={() => navigate('/email-verification')}\n                  >\n                    Verify Email\n                  </Button>\n                )}\n              </>\n            ) : (\n              <>\n                <Nav.Link as={Link} to=\"/login\" className=\"text-decoration-none\">\n                  Login\n                </Nav.Link>\n                <Button\n                  variant=\"primary\"\n                  className=\"ms-2\"\n                  onClick={() => navigate('/register')}\n                >\n                  Register\n                </Button>\n              </>\n            )}\n          </Nav>\n        </BootstrapNavbar.Collapse>\n      </Container>\n    </BootstrapNavbar>\n  );\n};\n\nexport default Navbar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,GAAI,CAAAC,eAAe,CAAEC,GAAG,CAAEC,WAAW,CAAEC,SAAS,CAAEC,MAAM,KAAQ,iBAAiB,CAChG,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,OAAO,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErD,KAAM,CAAAd,MAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAEe,IAAI,CAAEC,eAAe,CAAEC,MAAO,CAAC,CAAGT,OAAO,CAAC,CAAC,CACnD,KAAM,CAAAU,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAY,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAF,MAAM,CAAC,CAAC,CAChB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CACF,CAAC,CAED,mBACEV,IAAA,CAACT,eAAe,EAACqB,EAAE,CAAC,MAAM,CAACC,OAAO,CAAC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACC,MAAM,CAAC,KAAK,CAAAC,QAAA,cAChEd,KAAA,CAACR,SAAS,EAAAsB,QAAA,eACRhB,IAAA,CAACT,eAAe,CAAC0B,KAAK,EAACC,EAAE,CAAEtB,IAAK,CAACuB,EAAE,CAAC,GAAG,CAACC,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,gBAEzE,CAAuB,CAAC,cAExBhB,IAAA,CAACT,eAAe,CAAC8B,MAAM,EAAC,gBAAc,kBAAkB,CAAE,CAAC,cAC3DnB,KAAA,CAACX,eAAe,CAAC+B,QAAQ,EAACC,EAAE,CAAC,kBAAkB,CAAAP,QAAA,eAC7ChB,IAAA,CAACR,GAAG,EAAC4B,SAAS,CAAC,SAAS,CAAAJ,QAAA,cACtBhB,IAAA,CAACR,GAAG,CAACI,IAAI,EAACsB,EAAE,CAAEtB,IAAK,CAACuB,EAAE,CAAC,GAAG,CAACC,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,MAE5D,CAAU,CAAC,CACR,CAAC,cAENhB,IAAA,CAACR,GAAG,EAAC4B,SAAS,CAAC,SAAS,CAAAJ,QAAA,CACrBV,eAAe,cACdJ,KAAA,CAAAE,SAAA,EAAAY,QAAA,eACEhB,IAAA,CAACR,GAAG,CAACI,IAAI,EAACsB,EAAE,CAAEtB,IAAK,CAACuB,EAAE,CAAC,YAAY,CAACC,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,WAErE,CAAU,CAAC,cACXd,KAAA,CAACT,WAAW,EACV+B,KAAK,CAAE,CAAAnB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoB,IAAI,GAAI,MAAO,CAC5BF,EAAE,CAAC,eAAe,CAClBG,KAAK,CAAC,KAAK,CAAAV,QAAA,eAEXhB,IAAA,CAACP,WAAW,CAACkC,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAMpB,QAAQ,CAAC,UAAU,CAAE,CAAAQ,QAAA,CAAC,SAEvD,CAAkB,CAAC,cACnBhB,IAAA,CAACP,WAAW,CAACkC,IAAI,EAACC,OAAO,CAAEA,CAAA,GAAMpB,QAAQ,CAAC,eAAe,CAAE,CAAAQ,QAAA,CAAC,cAE5D,CAAkB,CAAC,cACnBhB,IAAA,CAACP,WAAW,CAACoC,OAAO,GAAE,CAAC,cACvB7B,IAAA,CAACP,WAAW,CAACkC,IAAI,EAACC,OAAO,CAAEnB,YAAa,CAAAO,QAAA,CAAC,QAEzC,CAAkB,CAAC,EACR,CAAC,CAEb,EAACX,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEyB,iBAAiB,gBACvB9B,IAAA,CAACL,MAAM,EACLkB,OAAO,CAAC,iBAAiB,CACzBkB,IAAI,CAAC,IAAI,CACTX,SAAS,CAAC,MAAM,CAChBQ,OAAO,CAAEA,CAAA,GAAMpB,QAAQ,CAAC,qBAAqB,CAAE,CAAAQ,QAAA,CAChD,cAED,CAAQ,CACT,EACD,CAAC,cAEHd,KAAA,CAAAE,SAAA,EAAAY,QAAA,eACEhB,IAAA,CAACR,GAAG,CAACI,IAAI,EAACsB,EAAE,CAAEtB,IAAK,CAACuB,EAAE,CAAC,QAAQ,CAACC,SAAS,CAAC,sBAAsB,CAAAJ,QAAA,CAAC,OAEjE,CAAU,CAAC,cACXhB,IAAA,CAACL,MAAM,EACLkB,OAAO,CAAC,SAAS,CACjBO,SAAS,CAAC,MAAM,CAChBQ,OAAO,CAAEA,CAAA,GAAMpB,QAAQ,CAAC,WAAW,CAAE,CAAAQ,QAAA,CACtC,UAED,CAAQ,CAAC,EACT,CACH,CACE,CAAC,EACkB,CAAC,EAClB,CAAC,CACG,CAAC,CAEtB,CAAC,CAED,cAAe,CAAA1B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}