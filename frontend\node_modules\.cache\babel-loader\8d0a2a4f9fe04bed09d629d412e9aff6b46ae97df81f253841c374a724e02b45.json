{"ast": null, "code": "import React,{useState}from'react';import{<PERSON>}from'react-router-dom';import{Container,Row,Col,Card,Form,Button,Al<PERSON>,Spinner}from'react-bootstrap';import{useForm}from'react-hook-form';import{yupResolver}from'@hookform/resolvers/yup';import*as yup from'yup';import{useAuth}from'../../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const schema=yup.object({email:yup.string().email('Invalid email').required('Email is required')});const ForgotPassword=()=>{var _errors$email;const[loading,setLoading]=useState(false);const[message,setMessage]=useState('');const[error,setError]=useState('');const{forgotPassword}=useAuth();const{register,handleSubmit,formState:{errors}}=useForm({resolver:yupResolver(schema)});const onSubmit=async data=>{try{setLoading(true);setError('');setMessage('');const response=await forgotPassword(data.email);setMessage(response.message);}catch(err){var _err$response,_err$response$data;setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'Failed to send reset email. Please try again.');}finally{setLoading(false);}};return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{className:\"text-center mb-4\",children:\"Forgot Password\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted text-center mb-4\",children:\"Enter your email address and we'll send you a link to reset your password.\"}),message&&/*#__PURE__*/_jsx(Alert,{variant:\"success\",children:message}),error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error}),/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit(onSubmit),children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Email Address\"}),/*#__PURE__*/_jsx(Form.Control,{type:\"email\",...register('email'),isInvalid:!!errors.email,placeholder:\"Enter your email\"}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:(_errors$email=errors.email)===null||_errors$email===void 0?void 0:_errors$email.message})]}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",type:\"submit\",className:\"w-100 mb-3\",disabled:loading,children:loading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\",className:\"me-2\"}),\"Sending...\"]}):'Send Reset Link'})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"text-decoration-none\",children:\"Back to Login\"})})]})})})})});};export default ForgotPassword;", "map": {"version": 3, "names": ["React", "useState", "Link", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "useForm", "yupResolver", "yup", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "schema", "object", "email", "string", "required", "ForgotPassword", "_errors$email", "loading", "setLoading", "message", "setMessage", "error", "setError", "forgotPassword", "register", "handleSubmit", "formState", "errors", "resolver", "onSubmit", "data", "response", "err", "_err$response", "_err$response$data", "children", "className", "md", "lg", "Body", "Title", "variant", "Group", "Label", "Control", "type", "isInvalid", "placeholder", "<PERSON><PERSON><PERSON>", "disabled", "as", "animation", "size", "role", "to"], "sources": ["C:/laragon/www/frontend/src/pages/auth/ForgotPassword.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport { Container, Row, Col, Card, Form, But<PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst schema = yup.object({\n  email: yup.string().email('Invalid email').required('Email is required'),\n});\n\ninterface ForgotPasswordForm {\n  email: string;\n}\n\nconst ForgotPassword: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState<string>('');\n  const [error, setError] = useState<string>('');\n  const { forgotPassword } = useAuth();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<ForgotPasswordForm>({\n    resolver: yupResolver(schema),\n  });\n\n  const onSubmit = async (data: ForgotPasswordForm) => {\n    try {\n      setLoading(true);\n      setError('');\n      setMessage('');\n      \n      const response = await forgotPassword(data.email);\n      setMessage(response.message);\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to send reset email. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container>\n      <Row className=\"justify-content-center\">\n        <Col md={6} lg={4}>\n          <Card>\n            <Card.Body>\n              <Card.Title className=\"text-center mb-4\">Forgot Password</Card.Title>\n              \n              <p className=\"text-muted text-center mb-4\">\n                Enter your email address and we'll send you a link to reset your password.\n              </p>\n\n              {message && <Alert variant=\"success\">{message}</Alert>}\n              {error && <Alert variant=\"danger\">{error}</Alert>}\n\n              <Form onSubmit={handleSubmit(onSubmit)}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Email Address</Form.Label>\n                  <Form.Control\n                    type=\"email\"\n                    {...register('email')}\n                    isInvalid={!!errors.email}\n                    placeholder=\"Enter your email\"\n                  />\n                  <Form.Control.Feedback type=\"invalid\">\n                    {errors.email?.message}\n                  </Form.Control.Feedback>\n                </Form.Group>\n\n                <Button\n                  variant=\"primary\"\n                  type=\"submit\"\n                  className=\"w-100 mb-3\"\n                  disabled={loading}\n                >\n                  {loading ? (\n                    <>\n                      <Spinner\n                        as=\"span\"\n                        animation=\"border\"\n                        size=\"sm\"\n                        role=\"status\"\n                        aria-hidden=\"true\"\n                        className=\"me-2\"\n                      />\n                      Sending...\n                    </>\n                  ) : (\n                    'Send Reset Link'\n                  )}\n                </Button>\n              </Form>\n\n              <div className=\"text-center\">\n                <Link to=\"/login\" className=\"text-decoration-none\">\n                  Back to Login\n                </Link>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default ForgotPassword;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,IAAI,CAAEC,MAAM,CAAEC,KAAK,CAAEC,OAAO,KAAQ,iBAAiB,CACzF,OAASC,OAAO,KAAQ,iBAAiB,CACzC,OAASC,WAAW,KAAQ,yBAAyB,CACrD,MAAO,GAAK,CAAAC,GAAG,KAAM,KAAK,CAC1B,OAASC,OAAO,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAErD,KAAM,CAAAC,MAAM,CAAGR,GAAG,CAACS,MAAM,CAAC,CACxBC,KAAK,CAAEV,GAAG,CAACW,MAAM,CAAC,CAAC,CAACD,KAAK,CAAC,eAAe,CAAC,CAACE,QAAQ,CAAC,mBAAmB,CACzE,CAAC,CAAC,CAMF,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,KAAAC,aAAA,CACrC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC6B,OAAO,CAAEC,UAAU,CAAC,CAAG9B,QAAQ,CAAS,EAAE,CAAC,CAClD,KAAM,CAAC+B,KAAK,CAAEC,QAAQ,CAAC,CAAGhC,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAAEiC,cAAe,CAAC,CAAGpB,OAAO,CAAC,CAAC,CAEpC,KAAM,CACJqB,QAAQ,CACRC,YAAY,CACZC,SAAS,CAAE,CAAEC,MAAO,CACtB,CAAC,CAAG3B,OAAO,CAAqB,CAC9B4B,QAAQ,CAAE3B,WAAW,CAACS,MAAM,CAC9B,CAAC,CAAC,CAEF,KAAM,CAAAmB,QAAQ,CAAG,KAAO,CAAAC,IAAwB,EAAK,CACnD,GAAI,CACFZ,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,EAAE,CAAC,CACZF,UAAU,CAAC,EAAE,CAAC,CAEd,KAAM,CAAAW,QAAQ,CAAG,KAAM,CAAAR,cAAc,CAACO,IAAI,CAAClB,KAAK,CAAC,CACjDQ,UAAU,CAACW,QAAQ,CAACZ,OAAO,CAAC,CAC9B,CAAE,MAAOa,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjBZ,QAAQ,CAAC,EAAAW,aAAA,CAAAD,GAAG,CAACD,QAAQ,UAAAE,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcH,IAAI,UAAAI,kBAAA,iBAAlBA,kBAAA,CAAoBf,OAAO,GAAI,+CAA+C,CAAC,CAC1F,CAAC,OAAS,CACRD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACEb,IAAA,CAACb,SAAS,EAAA2C,QAAA,cACR9B,IAAA,CAACZ,GAAG,EAAC2C,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cACrC9B,IAAA,CAACX,GAAG,EAAC2C,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAH,QAAA,cAChB9B,IAAA,CAACV,IAAI,EAAAwC,QAAA,cACH5B,KAAA,CAACZ,IAAI,CAAC4C,IAAI,EAAAJ,QAAA,eACR9B,IAAA,CAACV,IAAI,CAAC6C,KAAK,EAACJ,SAAS,CAAC,kBAAkB,CAAAD,QAAA,CAAC,iBAAe,CAAY,CAAC,cAErE9B,IAAA,MAAG+B,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,4EAE3C,CAAG,CAAC,CAEHhB,OAAO,eAAId,IAAA,CAACP,KAAK,EAAC2C,OAAO,CAAC,SAAS,CAAAN,QAAA,CAAEhB,OAAO,CAAQ,CAAC,CACrDE,KAAK,eAAIhB,IAAA,CAACP,KAAK,EAAC2C,OAAO,CAAC,QAAQ,CAAAN,QAAA,CAAEd,KAAK,CAAQ,CAAC,cAEjDd,KAAA,CAACX,IAAI,EAACiC,QAAQ,CAAEJ,YAAY,CAACI,QAAQ,CAAE,CAAAM,QAAA,eACrC5B,KAAA,CAACX,IAAI,CAAC8C,KAAK,EAACN,SAAS,CAAC,MAAM,CAAAD,QAAA,eAC1B9B,IAAA,CAACT,IAAI,CAAC+C,KAAK,EAAAR,QAAA,CAAC,eAAa,CAAY,CAAC,cACtC9B,IAAA,CAACT,IAAI,CAACgD,OAAO,EACXC,IAAI,CAAC,OAAO,IACRrB,QAAQ,CAAC,OAAO,CAAC,CACrBsB,SAAS,CAAE,CAAC,CAACnB,MAAM,CAACf,KAAM,CAC1BmC,WAAW,CAAC,kBAAkB,CAC/B,CAAC,cACF1C,IAAA,CAACT,IAAI,CAACgD,OAAO,CAACI,QAAQ,EAACH,IAAI,CAAC,SAAS,CAAAV,QAAA,EAAAnB,aAAA,CAClCW,MAAM,CAACf,KAAK,UAAAI,aAAA,iBAAZA,aAAA,CAAcG,OAAO,CACD,CAAC,EACd,CAAC,cAEbd,IAAA,CAACR,MAAM,EACL4C,OAAO,CAAC,SAAS,CACjBI,IAAI,CAAC,QAAQ,CACbT,SAAS,CAAC,YAAY,CACtBa,QAAQ,CAAEhC,OAAQ,CAAAkB,QAAA,CAEjBlB,OAAO,cACNV,KAAA,CAAAE,SAAA,EAAA0B,QAAA,eACE9B,IAAA,CAACN,OAAO,EACNmD,EAAE,CAAC,MAAM,CACTC,SAAS,CAAC,QAAQ,CAClBC,IAAI,CAAC,IAAI,CACTC,IAAI,CAAC,QAAQ,CACb,cAAY,MAAM,CAClBjB,SAAS,CAAC,MAAM,CACjB,CAAC,aAEJ,EAAE,CAAC,CAEH,iBACD,CACK,CAAC,EACL,CAAC,cAEP/B,IAAA,QAAK+B,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1B9B,IAAA,CAACd,IAAI,EAAC+D,EAAE,CAAC,QAAQ,CAAClB,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,eAEnD,CAAM,CAAC,CACJ,CAAC,EACG,CAAC,CACR,CAAC,CACJ,CAAC,CACH,CAAC,CACG,CAAC,CAEhB,CAAC,CAED,cAAe,CAAApB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}