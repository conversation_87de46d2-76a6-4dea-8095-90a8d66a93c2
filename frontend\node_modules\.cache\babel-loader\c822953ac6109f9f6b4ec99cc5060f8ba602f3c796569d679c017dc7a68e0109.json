{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navbar as BootstrapNavbar, Nav, NavDropdown, Container, Button } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(BootstrapNavbar, {\n    bg: \"dark\",\n    variant: \"dark\",\n    expand: \"lg\",\n    sticky: \"top\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(BootstrapNavbar.Brand, {\n        as: Link,\n        to: \"/\",\n        className: \"text-decoration-none\",\n        children: \"Full Stack CMS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BootstrapNavbar.Toggle, {\n        \"aria-controls\": \"basic-navbar-nav\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BootstrapNavbar.Collapse, {\n        id: \"basic-navbar-nav\",\n        children: [/*#__PURE__*/_jsxDEV(Nav, {\n          className: \"me-auto\",\n          children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/\",\n            className: \"text-decoration-none\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav, {\n          className: \"ms-auto\",\n          children: isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/dashboard\",\n              className: \"text-decoration-none\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(NavDropdown, {\n              title: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n              id: \"user-dropdown\",\n              align: \"end\",\n              children: [/*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                onClick: () => navigate('/profile'),\n                children: \"Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                onClick: () => navigate('/profile/edit'),\n                children: \"Edit Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                onClick: handleLogout,\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 17\n            }, this), !(user !== null && user !== void 0 && user.email_verified_at) && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-warning\",\n              size: \"sm\",\n              className: \"ms-2\",\n              onClick: () => navigate('/email-verification'),\n              children: \"Verify Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/login\",\n              className: \"text-decoration-none\",\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              className: \"ms-2\",\n              onClick: () => navigate('/register'),\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"B5aHiu91piX0w1CsOFDPXF/GbhU=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "BootstrapNavbar", "Nav", "NavDropdown", "Container", "<PERSON><PERSON>", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "_s", "user", "isAuthenticated", "logout", "navigate", "handleLogout", "error", "console", "bg", "variant", "expand", "sticky", "children", "Brand", "as", "to", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Toggle", "Collapse", "id", "title", "name", "align", "<PERSON><PERSON>", "onClick", "Divider", "email_verified_at", "size", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navbar as BootstrapN<PERSON>bar, Nav, NavDropdown, Container, But<PERSON> } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst Navbar: React.FC = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  return (\n    <BootstrapNavbar bg=\"dark\" variant=\"dark\" expand=\"lg\" sticky=\"top\">\n      <Container>\n        <BootstrapNavbar.Brand as={Link} to=\"/\" className=\"text-decoration-none\">\n          Full Stack CMS\n        </BootstrapNavbar.Brand>\n\n        <BootstrapNavbar.Toggle aria-controls=\"basic-navbar-nav\" />\n        <BootstrapNavbar.Collapse id=\"basic-navbar-nav\">\n          <Nav className=\"me-auto\">\n            <Nav.Link as={Link} to=\"/\" className=\"text-decoration-none\">\n              Home\n            </Nav.Link>\n          </Nav>\n\n          <Nav className=\"ms-auto\">\n            {isAuthenticated ? (\n              <>\n                <Nav.Link as={Link} to=\"/dashboard\" className=\"text-decoration-none\">\n                  Dashboard\n                </Nav.Link>\n                <NavDropdown\n                  title={user?.name || 'User'}\n                  id=\"user-dropdown\"\n                  align=\"end\"\n                >\n                  <NavDropdown.Item onClick={() => navigate('/profile')}>\n                    Profile\n                  </NavDropdown.Item>\n                  <NavDropdown.Item onClick={() => navigate('/profile/edit')}>\n                    Edit Profile\n                  </NavDropdown.Item>\n                  <NavDropdown.Divider />\n                  <NavDropdown.Item onClick={handleLogout}>\n                    Logout\n                  </NavDropdown.Item>\n                </NavDropdown>\n\n                {!user?.email_verified_at && (\n                  <Button\n                    variant=\"outline-warning\"\n                    size=\"sm\"\n                    className=\"ms-2\"\n                    onClick={() => navigate('/email-verification')}\n                  >\n                    Verify Email\n                  </Button>\n                )}\n              </>\n            ) : (\n              <>\n                <Nav.Link as={Link} to=\"/login\" className=\"text-decoration-none\">\n                  Login\n                </Nav.Link>\n                <Button\n                  variant=\"primary\"\n                  className=\"ms-2\"\n                  onClick={() => navigate('/register')}\n                >\n                  Register\n                </Button>\n              </>\n            )}\n          </Nav>\n        </BootstrapNavbar.Collapse>\n      </Container>\n    </BootstrapNavbar>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,IAAIC,eAAe,EAAEC,GAAG,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,iBAAiB;AAChG,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMZ,MAAgB,GAAGA,CAAA,KAAM;EAAAa,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGR,OAAO,CAAC,CAAC;EACnD,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMF,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;EAED,oBACET,OAAA,CAACT,eAAe;IAACoB,EAAE,EAAC,MAAM;IAACC,OAAO,EAAC,MAAM;IAACC,MAAM,EAAC,IAAI;IAACC,MAAM,EAAC,KAAK;IAAAC,QAAA,eAChEf,OAAA,CAACN,SAAS;MAAAqB,QAAA,gBACRf,OAAA,CAACT,eAAe,CAACyB,KAAK;QAACC,EAAE,EAAErB,IAAK;QAACsB,EAAE,EAAC,GAAG;QAACC,SAAS,EAAC,sBAAsB;QAAAJ,QAAA,EAAC;MAEzE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAuB,CAAC,eAExBvB,OAAA,CAACT,eAAe,CAACiC,MAAM;QAAC,iBAAc;MAAkB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DvB,OAAA,CAACT,eAAe,CAACkC,QAAQ;QAACC,EAAE,EAAC,kBAAkB;QAAAX,QAAA,gBAC7Cf,OAAA,CAACR,GAAG;UAAC2B,SAAS,EAAC,SAAS;UAAAJ,QAAA,eACtBf,OAAA,CAACR,GAAG,CAACI,IAAI;YAACqB,EAAE,EAAErB,IAAK;YAACsB,EAAE,EAAC,GAAG;YAACC,SAAS,EAAC,sBAAsB;YAAAJ,QAAA,EAAC;UAE5D;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENvB,OAAA,CAACR,GAAG;UAAC2B,SAAS,EAAC,SAAS;UAAAJ,QAAA,EACrBV,eAAe,gBACdL,OAAA,CAAAE,SAAA;YAAAa,QAAA,gBACEf,OAAA,CAACR,GAAG,CAACI,IAAI;cAACqB,EAAE,EAAErB,IAAK;cAACsB,EAAE,EAAC,YAAY;cAACC,SAAS,EAAC,sBAAsB;cAAAJ,QAAA,EAAC;YAErE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXvB,OAAA,CAACP,WAAW;cACVkC,KAAK,EAAE,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,KAAI,MAAO;cAC5BF,EAAE,EAAC,eAAe;cAClBG,KAAK,EAAC,KAAK;cAAAd,QAAA,gBAEXf,OAAA,CAACP,WAAW,CAACqC,IAAI;gBAACC,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAAC,UAAU,CAAE;gBAAAQ,QAAA,EAAC;cAEvD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,eACnBvB,OAAA,CAACP,WAAW,CAACqC,IAAI;gBAACC,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAAC,eAAe,CAAE;gBAAAQ,QAAA,EAAC;cAE5D;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC,eACnBvB,OAAA,CAACP,WAAW,CAACuC,OAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvBvB,OAAA,CAACP,WAAW,CAACqC,IAAI;gBAACC,OAAO,EAAEvB,YAAa;gBAAAO,QAAA,EAAC;cAEzC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAkB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAEb,EAACnB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,iBAAiB,kBACvBjC,OAAA,CAACL,MAAM;cACLiB,OAAO,EAAC,iBAAiB;cACzBsB,IAAI,EAAC,IAAI;cACTf,SAAS,EAAC,MAAM;cAChBY,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAAC,qBAAqB,CAAE;cAAAQ,QAAA,EAChD;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA,eACD,CAAC,gBAEHvB,OAAA,CAAAE,SAAA;YAAAa,QAAA,gBACEf,OAAA,CAACR,GAAG,CAACI,IAAI;cAACqB,EAAE,EAAErB,IAAK;cAACsB,EAAE,EAAC,QAAQ;cAACC,SAAS,EAAC,sBAAsB;cAAAJ,QAAA,EAAC;YAEjE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXvB,OAAA,CAACL,MAAM;cACLiB,OAAO,EAAC,SAAS;cACjBO,SAAS,EAAC,MAAM;cAChBY,OAAO,EAAEA,CAAA,KAAMxB,QAAQ,CAAC,WAAW,CAAE;cAAAQ,QAAA,EACtC;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACkB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAACpB,EAAA,CAhFIb,MAAgB;EAAA,QACsBQ,OAAO,EAChCD,WAAW;AAAA;AAAAsC,EAAA,GAFxB7C,MAAgB;AAkFtB,eAAeA,MAAM;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}