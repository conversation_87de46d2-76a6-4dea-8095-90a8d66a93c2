<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CreditPackage;
use App\Models\WalletTransaction;
use App\Services\BillplzService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class PaymentController extends Controller
{
    protected $billplzService;

    public function __construct(BillplzService $billplzService)
    {
        $this->billplzService = $billplzService;
    }

    /**
     * Create a payment for wallet top-up (credit package purchase)
     */
    public function createPayment(Request $request): JsonResponse
    {
        $request->validate([
            'package_id' => 'required|exists:credit_packages,id',
            'redirect_url' => 'nullable|url',
        ]);

        $user = $request->user();
        $package = CreditPackage::findOrFail($request->package_id);

        if (!$package->is_active) {
            throw ValidationException::withMessages([
                'package_id' => 'This package is not available for purchase.',
            ]);
        }

        if (!$this->billplzService->isEnabled()) {
            return response()->json([
                'error' => 'Payment gateway is currently disabled. Please contact support.',
            ], 503);
        }

        try {
            $result = $this->billplzService->createBill($user, $package, [
                'redirect_url' => $request->redirect_url ?? url('/dashboard/wallet'),
            ]);

            if (!$result['success']) {
                // Log the error for debugging
                \Log::error('Payment creation failed', [
                    'user_id' => $user->id,
                    'package_id' => $package->id,
                    'error' => $result['error'],
                ]);

                return response()->json([
                    'error' => $result['error'],
                ], 500);
            }
        } catch (\Exception $e) {
            // Log the exception for debugging
            \Log::error('Payment creation exception', [
                'user_id' => $user->id,
                'package_id' => $package->id,
                'exception' => $e->getMessage(),
            ]);

            return response()->json([
                'error' => 'Payment gateway configuration error. Please contact support.',
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Wallet top-up payment created successfully',
            'payment_url' => $result['url'],
            'bill_id' => $result['bill_id'],
            'transaction_id' => $result['transaction_id'],
            'package' => [
                'id' => $package->id,
                'name' => $package->name,
                'price' => $package->price,
                'formatted_price' => 'RM ' . number_format($package->price, 2),
                'description' => $package->description,
            ],
        ]);
    }

    /**
     * Handle Billplz callback
     */
    public function billplzCallback(Request $request): JsonResponse
    {
        $result = $this->billplzService->handleCallback($request->all());

        if (!$result['success']) {
            return response()->json([
                'error' => $result['error'],
            ], 400);
        }

        return response()->json([
            'success' => true,
            'transaction_id' => $result['transaction_id'],
            'status' => $result['status'],
        ]);
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus(Request $request): JsonResponse
    {
        $request->validate([
            'transaction_id' => 'required|exists:credit_transactions,id',
        ]);

        $user = $request->user();
        $transaction = WalletTransaction::where('id', $request->transaction_id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        // If payment is still pending and we have a bill ID, check with Billplz
        if ($transaction->payment_status === 'pending' && $transaction->payment_reference) {
            $billStatus = $this->billplzService->getBillStatus($transaction->payment_reference);

            if ($billStatus['success']) {
                $billData = $billStatus['data'];

                // Update transaction if status changed
                if (isset($billData['state']) && $billData['state'] === 'paid' && $transaction->payment_status !== 'completed') {
                    $this->billplzService->handleCallback($billData);
                    $transaction->refresh();
                }
            }
        }

        return response()->json([
            'transaction_id' => $transaction->id,
            'payment_status' => $transaction->payment_status,
            'type' => $transaction->type,
            'amount' => $transaction->amount,
            'formatted_amount' => $transaction->getFormattedAmount(),
            'amount_paid' => $transaction->amount_paid,
            'formatted_amount_paid' => $transaction->getFormattedAmountPaid(),
            'processed_at' => $transaction->processed_at?->format('Y-m-d H:i:s'),
            'description' => $transaction->description,
        ]);
    }

    /**
     * Get payment configuration
     */
    public function getPaymentConfig(): JsonResponse
    {
        return response()->json([
            'billplz_enabled' => $this->billplzService->isEnabled(),
            'billplz_configured' => $this->billplzService->isConfigured(),
        ]);
    }
}
