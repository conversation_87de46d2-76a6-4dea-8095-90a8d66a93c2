{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles } from '@mui/styled-engine';\nimport { useTheme as muiUseTheme } from '@mui/private-theming';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport ThemeProvider from \"../ThemeProvider/index.js\";\nimport InitColorSchemeScript, { DEFAULT_COLOR_SCHEME_STORAGE_KEY, DEFAULT_MODE_STORAGE_KEY } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nimport useCurrentColorScheme from \"./useCurrentColorScheme.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const DISABLE_CSS_TRANSITION = '*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}';\nexport default function createCssVarsProvider(options) {\n  const {\n    themeId,\n    /**\n     * This `theme` object needs to follow a certain structure to\n     * be used correctly by the finel `CssVarsProvider`. It should have a\n     * `colorSchemes` key with the light and dark (and any other) palette.\n     * It should also ideally have a vars object created using `prepareCssVars`.\n     */\n    theme: defaultTheme = {},\n    modeStorageKey: defaultModeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey: defaultColorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    disableTransitionOnChange: designSystemTransitionOnChange = false,\n    defaultColorScheme,\n    resolveTheme\n  } = options;\n  const defaultContext = {\n    allColorSchemes: [],\n    colorScheme: undefined,\n    darkColorScheme: undefined,\n    lightColorScheme: undefined,\n    mode: undefined,\n    setColorScheme: () => {},\n    setMode: () => {},\n    systemMode: undefined\n  };\n  const ColorSchemeContext = /*#__PURE__*/React.createContext(undefined);\n  if (process.env.NODE_ENV !== 'production') {\n    ColorSchemeContext.displayName = 'ColorSchemeContext';\n  }\n  const useColorScheme = () => React.useContext(ColorSchemeContext) || defaultContext;\n  const defaultColorSchemes = {};\n  const defaultComponents = {};\n  function CssVarsProvider(props) {\n    const {\n      children,\n      theme: themeProp,\n      modeStorageKey = defaultModeStorageKey,\n      colorSchemeStorageKey = defaultColorSchemeStorageKey,\n      disableTransitionOnChange = designSystemTransitionOnChange,\n      storageManager,\n      storageWindow = typeof window === 'undefined' ? undefined : window,\n      documentNode = typeof document === 'undefined' ? undefined : document,\n      colorSchemeNode = typeof document === 'undefined' ? undefined : document.documentElement,\n      disableNestedContext = false,\n      disableStyleSheetGeneration = false,\n      defaultMode: initialMode = 'system',\n      forceThemeRerender = false,\n      noSsr\n    } = props;\n    const hasMounted = React.useRef(false);\n    const upperTheme = muiUseTheme();\n    const ctx = React.useContext(ColorSchemeContext);\n    const nested = !!ctx && !disableNestedContext;\n    const initialTheme = React.useMemo(() => {\n      if (themeProp) {\n        return themeProp;\n      }\n      return typeof defaultTheme === 'function' ? defaultTheme() : defaultTheme;\n    }, [themeProp]);\n    const scopedTheme = initialTheme[themeId];\n    const restThemeProp = scopedTheme || initialTheme;\n    const {\n      colorSchemes = defaultColorSchemes,\n      components = defaultComponents,\n      cssVarPrefix\n    } = restThemeProp;\n    const joinedColorSchemes = Object.keys(colorSchemes).filter(k => !!colorSchemes[k]).join(',');\n    const allColorSchemes = React.useMemo(() => joinedColorSchemes.split(','), [joinedColorSchemes]);\n    const defaultLightColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.light;\n    const defaultDarkColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.dark;\n    const defaultMode = colorSchemes[defaultLightColorScheme] && colorSchemes[defaultDarkColorScheme] ? initialMode : colorSchemes[restThemeProp.defaultColorScheme]?.palette?.mode || restThemeProp.palette?.mode;\n\n    // 1. Get the data about the `mode`, `colorScheme`, and setter functions.\n    const {\n      mode: stateMode,\n      setMode,\n      systemMode,\n      lightColorScheme,\n      darkColorScheme,\n      colorScheme: stateColorScheme,\n      setColorScheme\n    } = useCurrentColorScheme({\n      supportedColorSchemes: allColorSchemes,\n      defaultLightColorScheme,\n      defaultDarkColorScheme,\n      modeStorageKey,\n      colorSchemeStorageKey,\n      defaultMode,\n      storageManager,\n      storageWindow,\n      noSsr\n    });\n    let mode = stateMode;\n    let colorScheme = stateColorScheme;\n    if (nested) {\n      mode = ctx.mode;\n      colorScheme = ctx.colorScheme;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (forceThemeRerender && !restThemeProp.vars) {\n        console.warn(['MUI: The `forceThemeRerender` prop should only be used with CSS theme variables.', 'Note that it will slow down the app when changing between modes, so only do this when you cannot find a better solution.'].join('\\n'));\n      }\n    }\n\n    // `colorScheme` is undefined on the server and hydration phase\n    let calculatedColorScheme = colorScheme || restThemeProp.defaultColorScheme;\n    if (restThemeProp.vars && !forceThemeRerender) {\n      calculatedColorScheme = restThemeProp.defaultColorScheme;\n    }\n    const memoTheme = React.useMemo(() => {\n      // 2. get the `vars` object that refers to the CSS custom properties\n      const themeVars = restThemeProp.generateThemeVars?.() || restThemeProp.vars;\n\n      // 3. Start composing the theme object\n      const theme = {\n        ...restThemeProp,\n        components,\n        colorSchemes,\n        cssVarPrefix,\n        vars: themeVars\n      };\n      if (typeof theme.generateSpacing === 'function') {\n        theme.spacing = theme.generateSpacing();\n      }\n\n      // 4. Resolve the color scheme and merge it to the theme\n      if (calculatedColorScheme) {\n        const scheme = colorSchemes[calculatedColorScheme];\n        if (scheme && typeof scheme === 'object') {\n          // 4.1 Merge the selected color scheme to the theme\n          Object.keys(scheme).forEach(schemeKey => {\n            if (scheme[schemeKey] && typeof scheme[schemeKey] === 'object') {\n              // shallow merge the 1st level structure of the theme.\n              theme[schemeKey] = {\n                ...theme[schemeKey],\n                ...scheme[schemeKey]\n              };\n            } else {\n              theme[schemeKey] = scheme[schemeKey];\n            }\n          });\n        }\n      }\n      return resolveTheme ? resolveTheme(theme) : theme;\n    }, [restThemeProp, calculatedColorScheme, components, colorSchemes, cssVarPrefix]);\n\n    // 5. Declaring effects\n    // 5.1 Updates the selector value to use the current color scheme which tells CSS to use the proper stylesheet.\n    const colorSchemeSelector = restThemeProp.colorSchemeSelector;\n    useEnhancedEffect(() => {\n      if (colorScheme && colorSchemeNode && colorSchemeSelector && colorSchemeSelector !== 'media') {\n        const selector = colorSchemeSelector;\n        let rule = colorSchemeSelector;\n        if (selector === 'class') {\n          rule = `.%s`;\n        }\n        if (selector === 'data') {\n          rule = `[data-%s]`;\n        }\n        if (selector?.startsWith('data-') && !selector.includes('%s')) {\n          // 'data-mui-color-scheme' -> '[data-mui-color-scheme=\"%s\"]'\n          rule = `[${selector}=\"%s\"]`;\n        }\n        if (rule.startsWith('.')) {\n          colorSchemeNode.classList.remove(...allColorSchemes.map(scheme => rule.substring(1).replace('%s', scheme)));\n          colorSchemeNode.classList.add(rule.substring(1).replace('%s', colorScheme));\n        } else {\n          const matches = rule.replace('%s', colorScheme).match(/\\[([^\\]]+)\\]/);\n          if (matches) {\n            const [attr, value] = matches[1].split('=');\n            if (!value) {\n              // for attributes like `data-theme-dark`, `data-theme-light`\n              // remove all the existing data attributes before setting the new one\n              allColorSchemes.forEach(scheme => {\n                colorSchemeNode.removeAttribute(attr.replace(colorScheme, scheme));\n              });\n            }\n            colorSchemeNode.setAttribute(attr, value ? value.replace(/\"|'/g, '') : '');\n          } else {\n            colorSchemeNode.setAttribute(rule, colorScheme);\n          }\n        }\n      }\n    }, [colorScheme, colorSchemeSelector, colorSchemeNode, allColorSchemes]);\n\n    // 5.2 Remove the CSS transition when color scheme changes to create instant experience.\n    // credit: https://github.com/pacocoursey/next-themes/blob/b5c2bad50de2d61ad7b52a9c5cdc801a78507d7a/index.tsx#L313\n    React.useEffect(() => {\n      let timer;\n      if (disableTransitionOnChange && hasMounted.current && documentNode) {\n        const css = documentNode.createElement('style');\n        css.appendChild(documentNode.createTextNode(DISABLE_CSS_TRANSITION));\n        documentNode.head.appendChild(css);\n\n        // Force browser repaint\n        (() => window.getComputedStyle(documentNode.body))();\n        timer = setTimeout(() => {\n          documentNode.head.removeChild(css);\n        }, 1);\n      }\n      return () => {\n        clearTimeout(timer);\n      };\n    }, [colorScheme, disableTransitionOnChange, documentNode]);\n    React.useEffect(() => {\n      hasMounted.current = true;\n      return () => {\n        hasMounted.current = false;\n      };\n    }, []);\n    const contextValue = React.useMemo(() => ({\n      allColorSchemes,\n      colorScheme,\n      darkColorScheme,\n      lightColorScheme,\n      mode,\n      setColorScheme,\n      setMode: process.env.NODE_ENV === 'production' ? setMode : newMode => {\n        if (memoTheme.colorSchemeSelector === 'media') {\n          console.error(['MUI: The `setMode` function has no effect if `colorSchemeSelector` is `media` (`media` is the default value).', 'To toggle the mode manually, please configure `colorSchemeSelector` to use a class or data attribute.', 'To learn more, visit https://mui.com/material-ui/customization/css-theme-variables/configuration/#toggling-dark-mode-manually'].join('\\n'));\n        }\n        setMode(newMode);\n      },\n      systemMode\n    }), [allColorSchemes, colorScheme, darkColorScheme, lightColorScheme, mode, setColorScheme, setMode, systemMode, memoTheme.colorSchemeSelector]);\n    let shouldGenerateStyleSheet = true;\n    if (disableStyleSheetGeneration || restThemeProp.cssVariables === false || nested && upperTheme?.cssVarPrefix === cssVarPrefix) {\n      shouldGenerateStyleSheet = false;\n    }\n    const element = /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [/*#__PURE__*/_jsx(ThemeProvider, {\n        themeId: scopedTheme ? themeId : undefined,\n        theme: memoTheme,\n        children: children\n      }), shouldGenerateStyleSheet && /*#__PURE__*/_jsx(GlobalStyles, {\n        styles: memoTheme.generateStyleSheets?.() || []\n      })]\n    });\n    if (nested) {\n      return element;\n    }\n    return /*#__PURE__*/_jsx(ColorSchemeContext.Provider, {\n      value: contextValue,\n      children: element\n    });\n  }\n  process.env.NODE_ENV !== \"production\" ? CssVarsProvider.propTypes = {\n    /**\n     * The component tree.\n     */\n    children: PropTypes.node,\n    /**\n     * The node used to attach the color-scheme attribute\n     */\n    colorSchemeNode: PropTypes.any,\n    /**\n     * localStorage key used to store `colorScheme`\n     */\n    colorSchemeStorageKey: PropTypes.string,\n    /**\n     * The default mode when the storage is empty,\n     * require the theme to have `colorSchemes` with light and dark.\n     */\n    defaultMode: PropTypes.string,\n    /**\n     * If `true`, the provider creates its own context and generate stylesheet as if it is a root `CssVarsProvider`.\n     */\n    disableNestedContext: PropTypes.bool,\n    /**\n     * If `true`, the style sheet won't be generated.\n     *\n     * This is useful for controlling nested CssVarsProvider behavior.\n     */\n    disableStyleSheetGeneration: PropTypes.bool,\n    /**\n     * Disable CSS transitions when switching between modes or color schemes.\n     */\n    disableTransitionOnChange: PropTypes.bool,\n    /**\n     * The document to attach the attribute to.\n     */\n    documentNode: PropTypes.any,\n    /**\n     * If `true`, theme values are recalculated when the mode changes.\n     */\n    forceThemeRerender: PropTypes.bool,\n    /**\n     * The key in the local storage used to store current color scheme.\n     */\n    modeStorageKey: PropTypes.string,\n    /**\n     * If `true`, the mode will be the same value as the storage without an extra rerendering after the hydration.\n     * You should use this option in conjuction with `InitColorSchemeScript` component.\n     */\n    noSsr: PropTypes.bool,\n    /**\n     * The storage manager to be used for storing the mode and color scheme\n     * @default using `window.localStorage`\n     */\n    storageManager: PropTypes.func,\n    /**\n     * The window that attaches the 'storage' event listener.\n     * @default window\n     */\n    storageWindow: PropTypes.any,\n    /**\n     * The calculated theme object that will be passed through context.\n     */\n    theme: PropTypes.object\n  } : void 0;\n  const defaultLightColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.light;\n  const defaultDarkColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.dark;\n  const getInitColorSchemeScript = params => InitColorSchemeScript({\n    colorSchemeStorageKey: defaultColorSchemeStorageKey,\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    modeStorageKey: defaultModeStorageKey,\n    ...params\n  });\n  return {\n    CssVarsProvider,\n    useColorScheme,\n    getInitColorSchemeScript\n  };\n}", "map": {"version": 3, "names": ["React", "PropTypes", "GlobalStyles", "useTheme", "muiUseTheme", "useEnhancedEffect", "ThemeProvider", "InitColorSchemeScript", "DEFAULT_COLOR_SCHEME_STORAGE_KEY", "DEFAULT_MODE_STORAGE_KEY", "useCurrentColorScheme", "jsx", "_jsx", "jsxs", "_jsxs", "DISABLE_CSS_TRANSITION", "createCssVarsProvider", "options", "themeId", "theme", "defaultTheme", "modeStorageKey", "defaultModeStorageKey", "colorSchemeStorageKey", "defaultColorSchemeStorageKey", "disableTransitionOnChange", "designSystemTransitionOnChange", "defaultColorScheme", "resolveTheme", "defaultContext", "allColorSchemes", "colorScheme", "undefined", "darkColorScheme", "lightColorScheme", "mode", "setColorScheme", "setMode", "systemMode", "ColorSchemeContext", "createContext", "process", "env", "NODE_ENV", "displayName", "useColorScheme", "useContext", "defaultColorSchemes", "defaultComponents", "CssVarsProvider", "props", "children", "themeProp", "storageManager", "storageWindow", "window", "documentNode", "document", "colorSchemeNode", "documentElement", "disableNestedContext", "disableStyleSheetGeneration", "defaultMode", "initialMode", "forceThemeRerender", "noSsr", "hasMounted", "useRef", "upperTheme", "ctx", "nested", "initialTheme", "useMemo", "scopedTheme", "restThemeProp", "colorSchemes", "components", "cssVarPrefix", "joinedColorSchemes", "Object", "keys", "filter", "k", "join", "split", "defaultLightColorScheme", "light", "defaultDarkColorScheme", "dark", "palette", "stateMode", "stateColorScheme", "supportedColorSchemes", "vars", "console", "warn", "calculatedColorScheme", "memoTheme", "themeVars", "generateThemeVars", "generateSpacing", "spacing", "scheme", "for<PERSON>ach", "<PERSON><PERSON>ey", "colorSchemeSelector", "selector", "rule", "startsWith", "includes", "classList", "remove", "map", "substring", "replace", "add", "matches", "match", "attr", "value", "removeAttribute", "setAttribute", "useEffect", "timer", "current", "css", "createElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "getComputedStyle", "body", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "contextValue", "newMode", "error", "shouldGenerateStyleSheet", "cssVariables", "element", "Fragment", "styles", "generateStyleSheets", "Provider", "propTypes", "node", "any", "string", "bool", "func", "object", "getInitColorSchemeScript", "params"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/system/esm/cssVars/createCssVarsProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles } from '@mui/styled-engine';\nimport { useTheme as muiUseTheme } from '@mui/private-theming';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport ThemeProvider from \"../ThemeProvider/index.js\";\nimport InitColorSchemeScript, { DEFAULT_COLOR_SCHEME_STORAGE_KEY, DEFAULT_MODE_STORAGE_KEY } from \"../InitColorSchemeScript/InitColorSchemeScript.js\";\nimport useCurrentColorScheme from \"./useCurrentColorScheme.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const DISABLE_CSS_TRANSITION = '*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}';\nexport default function createCssVarsProvider(options) {\n  const {\n    themeId,\n    /**\n     * This `theme` object needs to follow a certain structure to\n     * be used correctly by the finel `CssVarsProvider`. It should have a\n     * `colorSchemes` key with the light and dark (and any other) palette.\n     * It should also ideally have a vars object created using `prepareCssVars`.\n     */\n    theme: defaultTheme = {},\n    modeStorageKey: defaultModeStorageKey = DEFAULT_MODE_STORAGE_KEY,\n    colorSchemeStorageKey: defaultColorSchemeStorageKey = DEFAULT_COLOR_SCHEME_STORAGE_KEY,\n    disableTransitionOnChange: designSystemTransitionOnChange = false,\n    defaultColorScheme,\n    resolveTheme\n  } = options;\n  const defaultContext = {\n    allColorSchemes: [],\n    colorScheme: undefined,\n    darkColorScheme: undefined,\n    lightColorScheme: undefined,\n    mode: undefined,\n    setColorScheme: () => {},\n    setMode: () => {},\n    systemMode: undefined\n  };\n  const ColorSchemeContext = /*#__PURE__*/React.createContext(undefined);\n  if (process.env.NODE_ENV !== 'production') {\n    ColorSchemeContext.displayName = 'ColorSchemeContext';\n  }\n  const useColorScheme = () => React.useContext(ColorSchemeContext) || defaultContext;\n  const defaultColorSchemes = {};\n  const defaultComponents = {};\n  function CssVarsProvider(props) {\n    const {\n      children,\n      theme: themeProp,\n      modeStorageKey = defaultModeStorageKey,\n      colorSchemeStorageKey = defaultColorSchemeStorageKey,\n      disableTransitionOnChange = designSystemTransitionOnChange,\n      storageManager,\n      storageWindow = typeof window === 'undefined' ? undefined : window,\n      documentNode = typeof document === 'undefined' ? undefined : document,\n      colorSchemeNode = typeof document === 'undefined' ? undefined : document.documentElement,\n      disableNestedContext = false,\n      disableStyleSheetGeneration = false,\n      defaultMode: initialMode = 'system',\n      forceThemeRerender = false,\n      noSsr\n    } = props;\n    const hasMounted = React.useRef(false);\n    const upperTheme = muiUseTheme();\n    const ctx = React.useContext(ColorSchemeContext);\n    const nested = !!ctx && !disableNestedContext;\n    const initialTheme = React.useMemo(() => {\n      if (themeProp) {\n        return themeProp;\n      }\n      return typeof defaultTheme === 'function' ? defaultTheme() : defaultTheme;\n    }, [themeProp]);\n    const scopedTheme = initialTheme[themeId];\n    const restThemeProp = scopedTheme || initialTheme;\n    const {\n      colorSchemes = defaultColorSchemes,\n      components = defaultComponents,\n      cssVarPrefix\n    } = restThemeProp;\n    const joinedColorSchemes = Object.keys(colorSchemes).filter(k => !!colorSchemes[k]).join(',');\n    const allColorSchemes = React.useMemo(() => joinedColorSchemes.split(','), [joinedColorSchemes]);\n    const defaultLightColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.light;\n    const defaultDarkColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.dark;\n    const defaultMode = colorSchemes[defaultLightColorScheme] && colorSchemes[defaultDarkColorScheme] ? initialMode : colorSchemes[restThemeProp.defaultColorScheme]?.palette?.mode || restThemeProp.palette?.mode;\n\n    // 1. Get the data about the `mode`, `colorScheme`, and setter functions.\n    const {\n      mode: stateMode,\n      setMode,\n      systemMode,\n      lightColorScheme,\n      darkColorScheme,\n      colorScheme: stateColorScheme,\n      setColorScheme\n    } = useCurrentColorScheme({\n      supportedColorSchemes: allColorSchemes,\n      defaultLightColorScheme,\n      defaultDarkColorScheme,\n      modeStorageKey,\n      colorSchemeStorageKey,\n      defaultMode,\n      storageManager,\n      storageWindow,\n      noSsr\n    });\n    let mode = stateMode;\n    let colorScheme = stateColorScheme;\n    if (nested) {\n      mode = ctx.mode;\n      colorScheme = ctx.colorScheme;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (forceThemeRerender && !restThemeProp.vars) {\n        console.warn(['MUI: The `forceThemeRerender` prop should only be used with CSS theme variables.', 'Note that it will slow down the app when changing between modes, so only do this when you cannot find a better solution.'].join('\\n'));\n      }\n    }\n\n    // `colorScheme` is undefined on the server and hydration phase\n    let calculatedColorScheme = colorScheme || restThemeProp.defaultColorScheme;\n    if (restThemeProp.vars && !forceThemeRerender) {\n      calculatedColorScheme = restThemeProp.defaultColorScheme;\n    }\n    const memoTheme = React.useMemo(() => {\n      // 2. get the `vars` object that refers to the CSS custom properties\n      const themeVars = restThemeProp.generateThemeVars?.() || restThemeProp.vars;\n\n      // 3. Start composing the theme object\n      const theme = {\n        ...restThemeProp,\n        components,\n        colorSchemes,\n        cssVarPrefix,\n        vars: themeVars\n      };\n      if (typeof theme.generateSpacing === 'function') {\n        theme.spacing = theme.generateSpacing();\n      }\n\n      // 4. Resolve the color scheme and merge it to the theme\n      if (calculatedColorScheme) {\n        const scheme = colorSchemes[calculatedColorScheme];\n        if (scheme && typeof scheme === 'object') {\n          // 4.1 Merge the selected color scheme to the theme\n          Object.keys(scheme).forEach(schemeKey => {\n            if (scheme[schemeKey] && typeof scheme[schemeKey] === 'object') {\n              // shallow merge the 1st level structure of the theme.\n              theme[schemeKey] = {\n                ...theme[schemeKey],\n                ...scheme[schemeKey]\n              };\n            } else {\n              theme[schemeKey] = scheme[schemeKey];\n            }\n          });\n        }\n      }\n      return resolveTheme ? resolveTheme(theme) : theme;\n    }, [restThemeProp, calculatedColorScheme, components, colorSchemes, cssVarPrefix]);\n\n    // 5. Declaring effects\n    // 5.1 Updates the selector value to use the current color scheme which tells CSS to use the proper stylesheet.\n    const colorSchemeSelector = restThemeProp.colorSchemeSelector;\n    useEnhancedEffect(() => {\n      if (colorScheme && colorSchemeNode && colorSchemeSelector && colorSchemeSelector !== 'media') {\n        const selector = colorSchemeSelector;\n        let rule = colorSchemeSelector;\n        if (selector === 'class') {\n          rule = `.%s`;\n        }\n        if (selector === 'data') {\n          rule = `[data-%s]`;\n        }\n        if (selector?.startsWith('data-') && !selector.includes('%s')) {\n          // 'data-mui-color-scheme' -> '[data-mui-color-scheme=\"%s\"]'\n          rule = `[${selector}=\"%s\"]`;\n        }\n        if (rule.startsWith('.')) {\n          colorSchemeNode.classList.remove(...allColorSchemes.map(scheme => rule.substring(1).replace('%s', scheme)));\n          colorSchemeNode.classList.add(rule.substring(1).replace('%s', colorScheme));\n        } else {\n          const matches = rule.replace('%s', colorScheme).match(/\\[([^\\]]+)\\]/);\n          if (matches) {\n            const [attr, value] = matches[1].split('=');\n            if (!value) {\n              // for attributes like `data-theme-dark`, `data-theme-light`\n              // remove all the existing data attributes before setting the new one\n              allColorSchemes.forEach(scheme => {\n                colorSchemeNode.removeAttribute(attr.replace(colorScheme, scheme));\n              });\n            }\n            colorSchemeNode.setAttribute(attr, value ? value.replace(/\"|'/g, '') : '');\n          } else {\n            colorSchemeNode.setAttribute(rule, colorScheme);\n          }\n        }\n      }\n    }, [colorScheme, colorSchemeSelector, colorSchemeNode, allColorSchemes]);\n\n    // 5.2 Remove the CSS transition when color scheme changes to create instant experience.\n    // credit: https://github.com/pacocoursey/next-themes/blob/b5c2bad50de2d61ad7b52a9c5cdc801a78507d7a/index.tsx#L313\n    React.useEffect(() => {\n      let timer;\n      if (disableTransitionOnChange && hasMounted.current && documentNode) {\n        const css = documentNode.createElement('style');\n        css.appendChild(documentNode.createTextNode(DISABLE_CSS_TRANSITION));\n        documentNode.head.appendChild(css);\n\n        // Force browser repaint\n        (() => window.getComputedStyle(documentNode.body))();\n        timer = setTimeout(() => {\n          documentNode.head.removeChild(css);\n        }, 1);\n      }\n      return () => {\n        clearTimeout(timer);\n      };\n    }, [colorScheme, disableTransitionOnChange, documentNode]);\n    React.useEffect(() => {\n      hasMounted.current = true;\n      return () => {\n        hasMounted.current = false;\n      };\n    }, []);\n    const contextValue = React.useMemo(() => ({\n      allColorSchemes,\n      colorScheme,\n      darkColorScheme,\n      lightColorScheme,\n      mode,\n      setColorScheme,\n      setMode: process.env.NODE_ENV === 'production' ? setMode : newMode => {\n        if (memoTheme.colorSchemeSelector === 'media') {\n          console.error(['MUI: The `setMode` function has no effect if `colorSchemeSelector` is `media` (`media` is the default value).', 'To toggle the mode manually, please configure `colorSchemeSelector` to use a class or data attribute.', 'To learn more, visit https://mui.com/material-ui/customization/css-theme-variables/configuration/#toggling-dark-mode-manually'].join('\\n'));\n        }\n        setMode(newMode);\n      },\n      systemMode\n    }), [allColorSchemes, colorScheme, darkColorScheme, lightColorScheme, mode, setColorScheme, setMode, systemMode, memoTheme.colorSchemeSelector]);\n    let shouldGenerateStyleSheet = true;\n    if (disableStyleSheetGeneration || restThemeProp.cssVariables === false || nested && upperTheme?.cssVarPrefix === cssVarPrefix) {\n      shouldGenerateStyleSheet = false;\n    }\n    const element = /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [/*#__PURE__*/_jsx(ThemeProvider, {\n        themeId: scopedTheme ? themeId : undefined,\n        theme: memoTheme,\n        children: children\n      }), shouldGenerateStyleSheet && /*#__PURE__*/_jsx(GlobalStyles, {\n        styles: memoTheme.generateStyleSheets?.() || []\n      })]\n    });\n    if (nested) {\n      return element;\n    }\n    return /*#__PURE__*/_jsx(ColorSchemeContext.Provider, {\n      value: contextValue,\n      children: element\n    });\n  }\n  process.env.NODE_ENV !== \"production\" ? CssVarsProvider.propTypes = {\n    /**\n     * The component tree.\n     */\n    children: PropTypes.node,\n    /**\n     * The node used to attach the color-scheme attribute\n     */\n    colorSchemeNode: PropTypes.any,\n    /**\n     * localStorage key used to store `colorScheme`\n     */\n    colorSchemeStorageKey: PropTypes.string,\n    /**\n     * The default mode when the storage is empty,\n     * require the theme to have `colorSchemes` with light and dark.\n     */\n    defaultMode: PropTypes.string,\n    /**\n     * If `true`, the provider creates its own context and generate stylesheet as if it is a root `CssVarsProvider`.\n     */\n    disableNestedContext: PropTypes.bool,\n    /**\n     * If `true`, the style sheet won't be generated.\n     *\n     * This is useful for controlling nested CssVarsProvider behavior.\n     */\n    disableStyleSheetGeneration: PropTypes.bool,\n    /**\n     * Disable CSS transitions when switching between modes or color schemes.\n     */\n    disableTransitionOnChange: PropTypes.bool,\n    /**\n     * The document to attach the attribute to.\n     */\n    documentNode: PropTypes.any,\n    /**\n     * If `true`, theme values are recalculated when the mode changes.\n     */\n    forceThemeRerender: PropTypes.bool,\n    /**\n     * The key in the local storage used to store current color scheme.\n     */\n    modeStorageKey: PropTypes.string,\n    /**\n     * If `true`, the mode will be the same value as the storage without an extra rerendering after the hydration.\n     * You should use this option in conjuction with `InitColorSchemeScript` component.\n     */\n    noSsr: PropTypes.bool,\n    /**\n     * The storage manager to be used for storing the mode and color scheme\n     * @default using `window.localStorage`\n     */\n    storageManager: PropTypes.func,\n    /**\n     * The window that attaches the 'storage' event listener.\n     * @default window\n     */\n    storageWindow: PropTypes.any,\n    /**\n     * The calculated theme object that will be passed through context.\n     */\n    theme: PropTypes.object\n  } : void 0;\n  const defaultLightColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.light;\n  const defaultDarkColorScheme = typeof defaultColorScheme === 'string' ? defaultColorScheme : defaultColorScheme.dark;\n  const getInitColorSchemeScript = params => InitColorSchemeScript({\n    colorSchemeStorageKey: defaultColorSchemeStorageKey,\n    defaultLightColorScheme,\n    defaultDarkColorScheme,\n    modeStorageKey: defaultModeStorageKey,\n    ...params\n  });\n  return {\n    CssVarsProvider,\n    useColorScheme,\n    getInitColorSchemeScript\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,QAAQ,IAAIC,WAAW,QAAQ,sBAAsB;AAC9D,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,qBAAqB,IAAIC,gCAAgC,EAAEC,wBAAwB,QAAQ,mDAAmD;AACrJ,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,OAAO,MAAMC,sBAAsB,GAAG,0JAA0J;AAChM,eAAe,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EACrD,MAAM;IACJC,OAAO;IACP;AACJ;AACA;AACA;AACA;AACA;IACIC,KAAK,EAAEC,YAAY,GAAG,CAAC,CAAC;IACxBC,cAAc,EAAEC,qBAAqB,GAAGb,wBAAwB;IAChEc,qBAAqB,EAAEC,4BAA4B,GAAGhB,gCAAgC;IACtFiB,yBAAyB,EAAEC,8BAA8B,GAAG,KAAK;IACjEC,kBAAkB;IAClBC;EACF,CAAC,GAAGX,OAAO;EACX,MAAMY,cAAc,GAAG;IACrBC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAEC,SAAS;IACtBC,eAAe,EAAED,SAAS;IAC1BE,gBAAgB,EAAEF,SAAS;IAC3BG,IAAI,EAAEH,SAAS;IACfI,cAAc,EAAEA,CAAA,KAAM,CAAC,CAAC;IACxBC,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAC;IACjBC,UAAU,EAAEN;EACd,CAAC;EACD,MAAMO,kBAAkB,GAAG,aAAavC,KAAK,CAACwC,aAAa,CAACR,SAAS,CAAC;EACtE,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCJ,kBAAkB,CAACK,WAAW,GAAG,oBAAoB;EACvD;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM7C,KAAK,CAAC8C,UAAU,CAACP,kBAAkB,CAAC,IAAIV,cAAc;EACnF,MAAMkB,mBAAmB,GAAG,CAAC,CAAC;EAC9B,MAAMC,iBAAiB,GAAG,CAAC,CAAC;EAC5B,SAASC,eAAeA,CAACC,KAAK,EAAE;IAC9B,MAAM;MACJC,QAAQ;MACRhC,KAAK,EAAEiC,SAAS;MAChB/B,cAAc,GAAGC,qBAAqB;MACtCC,qBAAqB,GAAGC,4BAA4B;MACpDC,yBAAyB,GAAGC,8BAA8B;MAC1D2B,cAAc;MACdC,aAAa,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGvB,SAAS,GAAGuB,MAAM;MAClEC,YAAY,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGzB,SAAS,GAAGyB,QAAQ;MACrEC,eAAe,GAAG,OAAOD,QAAQ,KAAK,WAAW,GAAGzB,SAAS,GAAGyB,QAAQ,CAACE,eAAe;MACxFC,oBAAoB,GAAG,KAAK;MAC5BC,2BAA2B,GAAG,KAAK;MACnCC,WAAW,EAAEC,WAAW,GAAG,QAAQ;MACnCC,kBAAkB,GAAG,KAAK;MAC1BC;IACF,CAAC,GAAGf,KAAK;IACT,MAAMgB,UAAU,GAAGlE,KAAK,CAACmE,MAAM,CAAC,KAAK,CAAC;IACtC,MAAMC,UAAU,GAAGhE,WAAW,CAAC,CAAC;IAChC,MAAMiE,GAAG,GAAGrE,KAAK,CAAC8C,UAAU,CAACP,kBAAkB,CAAC;IAChD,MAAM+B,MAAM,GAAG,CAAC,CAACD,GAAG,IAAI,CAACT,oBAAoB;IAC7C,MAAMW,YAAY,GAAGvE,KAAK,CAACwE,OAAO,CAAC,MAAM;MACvC,IAAIpB,SAAS,EAAE;QACb,OAAOA,SAAS;MAClB;MACA,OAAO,OAAOhC,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC,CAAC,GAAGA,YAAY;IAC3E,CAAC,EAAE,CAACgC,SAAS,CAAC,CAAC;IACf,MAAMqB,WAAW,GAAGF,YAAY,CAACrD,OAAO,CAAC;IACzC,MAAMwD,aAAa,GAAGD,WAAW,IAAIF,YAAY;IACjD,MAAM;MACJI,YAAY,GAAG5B,mBAAmB;MAClC6B,UAAU,GAAG5B,iBAAiB;MAC9B6B;IACF,CAAC,GAAGH,aAAa;IACjB,MAAMI,kBAAkB,GAAGC,MAAM,CAACC,IAAI,CAACL,YAAY,CAAC,CAACM,MAAM,CAACC,CAAC,IAAI,CAAC,CAACP,YAAY,CAACO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IAC7F,MAAMrD,eAAe,GAAG9B,KAAK,CAACwE,OAAO,CAAC,MAAMM,kBAAkB,CAACM,KAAK,CAAC,GAAG,CAAC,EAAE,CAACN,kBAAkB,CAAC,CAAC;IAChG,MAAMO,uBAAuB,GAAG,OAAO1D,kBAAkB,KAAK,QAAQ,GAAGA,kBAAkB,GAAGA,kBAAkB,CAAC2D,KAAK;IACtH,MAAMC,sBAAsB,GAAG,OAAO5D,kBAAkB,KAAK,QAAQ,GAAGA,kBAAkB,GAAGA,kBAAkB,CAAC6D,IAAI;IACpH,MAAM1B,WAAW,GAAGa,YAAY,CAACU,uBAAuB,CAAC,IAAIV,YAAY,CAACY,sBAAsB,CAAC,GAAGxB,WAAW,GAAGY,YAAY,CAACD,aAAa,CAAC/C,kBAAkB,CAAC,EAAE8D,OAAO,EAAEtD,IAAI,IAAIuC,aAAa,CAACe,OAAO,EAAEtD,IAAI;;IAE9M;IACA,MAAM;MACJA,IAAI,EAAEuD,SAAS;MACfrD,OAAO;MACPC,UAAU;MACVJ,gBAAgB;MAChBD,eAAe;MACfF,WAAW,EAAE4D,gBAAgB;MAC7BvD;IACF,CAAC,GAAG1B,qBAAqB,CAAC;MACxBkF,qBAAqB,EAAE9D,eAAe;MACtCuD,uBAAuB;MACvBE,sBAAsB;MACtBlE,cAAc;MACdE,qBAAqB;MACrBuC,WAAW;MACXT,cAAc;MACdC,aAAa;MACbW;IACF,CAAC,CAAC;IACF,IAAI9B,IAAI,GAAGuD,SAAS;IACpB,IAAI3D,WAAW,GAAG4D,gBAAgB;IAClC,IAAIrB,MAAM,EAAE;MACVnC,IAAI,GAAGkC,GAAG,CAAClC,IAAI;MACfJ,WAAW,GAAGsC,GAAG,CAACtC,WAAW;IAC/B;IACA,IAAIU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIqB,kBAAkB,IAAI,CAACU,aAAa,CAACmB,IAAI,EAAE;QAC7CC,OAAO,CAACC,IAAI,CAAC,CAAC,kFAAkF,EAAE,0HAA0H,CAAC,CAACZ,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3O;IACF;;IAEA;IACA,IAAIa,qBAAqB,GAAGjE,WAAW,IAAI2C,aAAa,CAAC/C,kBAAkB;IAC3E,IAAI+C,aAAa,CAACmB,IAAI,IAAI,CAAC7B,kBAAkB,EAAE;MAC7CgC,qBAAqB,GAAGtB,aAAa,CAAC/C,kBAAkB;IAC1D;IACA,MAAMsE,SAAS,GAAGjG,KAAK,CAACwE,OAAO,CAAC,MAAM;MACpC;MACA,MAAM0B,SAAS,GAAGxB,aAAa,CAACyB,iBAAiB,GAAG,CAAC,IAAIzB,aAAa,CAACmB,IAAI;;MAE3E;MACA,MAAM1E,KAAK,GAAG;QACZ,GAAGuD,aAAa;QAChBE,UAAU;QACVD,YAAY;QACZE,YAAY;QACZgB,IAAI,EAAEK;MACR,CAAC;MACD,IAAI,OAAO/E,KAAK,CAACiF,eAAe,KAAK,UAAU,EAAE;QAC/CjF,KAAK,CAACkF,OAAO,GAAGlF,KAAK,CAACiF,eAAe,CAAC,CAAC;MACzC;;MAEA;MACA,IAAIJ,qBAAqB,EAAE;QACzB,MAAMM,MAAM,GAAG3B,YAAY,CAACqB,qBAAqB,CAAC;QAClD,IAAIM,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UACxC;UACAvB,MAAM,CAACC,IAAI,CAACsB,MAAM,CAAC,CAACC,OAAO,CAACC,SAAS,IAAI;YACvC,IAAIF,MAAM,CAACE,SAAS,CAAC,IAAI,OAAOF,MAAM,CAACE,SAAS,CAAC,KAAK,QAAQ,EAAE;cAC9D;cACArF,KAAK,CAACqF,SAAS,CAAC,GAAG;gBACjB,GAAGrF,KAAK,CAACqF,SAAS,CAAC;gBACnB,GAAGF,MAAM,CAACE,SAAS;cACrB,CAAC;YACH,CAAC,MAAM;cACLrF,KAAK,CAACqF,SAAS,CAAC,GAAGF,MAAM,CAACE,SAAS,CAAC;YACtC;UACF,CAAC,CAAC;QACJ;MACF;MACA,OAAO5E,YAAY,GAAGA,YAAY,CAACT,KAAK,CAAC,GAAGA,KAAK;IACnD,CAAC,EAAE,CAACuD,aAAa,EAAEsB,qBAAqB,EAAEpB,UAAU,EAAED,YAAY,EAAEE,YAAY,CAAC,CAAC;;IAElF;IACA;IACA,MAAM4B,mBAAmB,GAAG/B,aAAa,CAAC+B,mBAAmB;IAC7DpG,iBAAiB,CAAC,MAAM;MACtB,IAAI0B,WAAW,IAAI2B,eAAe,IAAI+C,mBAAmB,IAAIA,mBAAmB,KAAK,OAAO,EAAE;QAC5F,MAAMC,QAAQ,GAAGD,mBAAmB;QACpC,IAAIE,IAAI,GAAGF,mBAAmB;QAC9B,IAAIC,QAAQ,KAAK,OAAO,EAAE;UACxBC,IAAI,GAAG,KAAK;QACd;QACA,IAAID,QAAQ,KAAK,MAAM,EAAE;UACvBC,IAAI,GAAG,WAAW;QACpB;QACA,IAAID,QAAQ,EAAEE,UAAU,CAAC,OAAO,CAAC,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;UAC7D;UACAF,IAAI,GAAG,IAAID,QAAQ,QAAQ;QAC7B;QACA,IAAIC,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;UACxBlD,eAAe,CAACoD,SAAS,CAACC,MAAM,CAAC,GAAGjF,eAAe,CAACkF,GAAG,CAACV,MAAM,IAAIK,IAAI,CAACM,SAAS,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAEZ,MAAM,CAAC,CAAC,CAAC;UAC3G5C,eAAe,CAACoD,SAAS,CAACK,GAAG,CAACR,IAAI,CAACM,SAAS,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAEnF,WAAW,CAAC,CAAC;QAC7E,CAAC,MAAM;UACL,MAAMqF,OAAO,GAAGT,IAAI,CAACO,OAAO,CAAC,IAAI,EAAEnF,WAAW,CAAC,CAACsF,KAAK,CAAC,cAAc,CAAC;UACrE,IAAID,OAAO,EAAE;YACX,MAAM,CAACE,IAAI,EAAEC,KAAK,CAAC,GAAGH,OAAO,CAAC,CAAC,CAAC,CAAChC,KAAK,CAAC,GAAG,CAAC;YAC3C,IAAI,CAACmC,KAAK,EAAE;cACV;cACA;cACAzF,eAAe,CAACyE,OAAO,CAACD,MAAM,IAAI;gBAChC5C,eAAe,CAAC8D,eAAe,CAACF,IAAI,CAACJ,OAAO,CAACnF,WAAW,EAAEuE,MAAM,CAAC,CAAC;cACpE,CAAC,CAAC;YACJ;YACA5C,eAAe,CAAC+D,YAAY,CAACH,IAAI,EAAEC,KAAK,GAAGA,KAAK,CAACL,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;UAC5E,CAAC,MAAM;YACLxD,eAAe,CAAC+D,YAAY,CAACd,IAAI,EAAE5E,WAAW,CAAC;UACjD;QACF;MACF;IACF,CAAC,EAAE,CAACA,WAAW,EAAE0E,mBAAmB,EAAE/C,eAAe,EAAE5B,eAAe,CAAC,CAAC;;IAExE;IACA;IACA9B,KAAK,CAAC0H,SAAS,CAAC,MAAM;MACpB,IAAIC,KAAK;MACT,IAAIlG,yBAAyB,IAAIyC,UAAU,CAAC0D,OAAO,IAAIpE,YAAY,EAAE;QACnE,MAAMqE,GAAG,GAAGrE,YAAY,CAACsE,aAAa,CAAC,OAAO,CAAC;QAC/CD,GAAG,CAACE,WAAW,CAACvE,YAAY,CAACwE,cAAc,CAACjH,sBAAsB,CAAC,CAAC;QACpEyC,YAAY,CAACyE,IAAI,CAACF,WAAW,CAACF,GAAG,CAAC;;QAElC;QACA,CAAC,MAAMtE,MAAM,CAAC2E,gBAAgB,CAAC1E,YAAY,CAAC2E,IAAI,CAAC,EAAE,CAAC;QACpDR,KAAK,GAAGS,UAAU,CAAC,MAAM;UACvB5E,YAAY,CAACyE,IAAI,CAACI,WAAW,CAACR,GAAG,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC;MACP;MACA,OAAO,MAAM;QACXS,YAAY,CAACX,KAAK,CAAC;MACrB,CAAC;IACH,CAAC,EAAE,CAAC5F,WAAW,EAAEN,yBAAyB,EAAE+B,YAAY,CAAC,CAAC;IAC1DxD,KAAK,CAAC0H,SAAS,CAAC,MAAM;MACpBxD,UAAU,CAAC0D,OAAO,GAAG,IAAI;MACzB,OAAO,MAAM;QACX1D,UAAU,CAAC0D,OAAO,GAAG,KAAK;MAC5B,CAAC;IACH,CAAC,EAAE,EAAE,CAAC;IACN,MAAMW,YAAY,GAAGvI,KAAK,CAACwE,OAAO,CAAC,OAAO;MACxC1C,eAAe;MACfC,WAAW;MACXE,eAAe;MACfC,gBAAgB;MAChBC,IAAI;MACJC,cAAc;MACdC,OAAO,EAAEI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGN,OAAO,GAAGmG,OAAO,IAAI;QACpE,IAAIvC,SAAS,CAACQ,mBAAmB,KAAK,OAAO,EAAE;UAC7CX,OAAO,CAAC2C,KAAK,CAAC,CAAC,+GAA+G,EAAE,uGAAuG,EAAE,+HAA+H,CAAC,CAACtD,IAAI,CAAC,IAAI,CAAC,CAAC;QACvX;QACA9C,OAAO,CAACmG,OAAO,CAAC;MAClB,CAAC;MACDlG;IACF,CAAC,CAAC,EAAE,CAACR,eAAe,EAAEC,WAAW,EAAEE,eAAe,EAAEC,gBAAgB,EAAEC,IAAI,EAAEC,cAAc,EAAEC,OAAO,EAAEC,UAAU,EAAE2D,SAAS,CAACQ,mBAAmB,CAAC,CAAC;IAChJ,IAAIiC,wBAAwB,GAAG,IAAI;IACnC,IAAI7E,2BAA2B,IAAIa,aAAa,CAACiE,YAAY,KAAK,KAAK,IAAIrE,MAAM,IAAIF,UAAU,EAAES,YAAY,KAAKA,YAAY,EAAE;MAC9H6D,wBAAwB,GAAG,KAAK;IAClC;IACA,MAAME,OAAO,GAAG,aAAa9H,KAAK,CAACd,KAAK,CAAC6I,QAAQ,EAAE;MACjD1F,QAAQ,EAAE,CAAC,aAAavC,IAAI,CAACN,aAAa,EAAE;QAC1CY,OAAO,EAAEuD,WAAW,GAAGvD,OAAO,GAAGc,SAAS;QAC1Cb,KAAK,EAAE8E,SAAS;QAChB9C,QAAQ,EAAEA;MACZ,CAAC,CAAC,EAAEuF,wBAAwB,IAAI,aAAa9H,IAAI,CAACV,YAAY,EAAE;QAC9D4I,MAAM,EAAE7C,SAAS,CAAC8C,mBAAmB,GAAG,CAAC,IAAI;MAC/C,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIzE,MAAM,EAAE;MACV,OAAOsE,OAAO;IAChB;IACA,OAAO,aAAahI,IAAI,CAAC2B,kBAAkB,CAACyG,QAAQ,EAAE;MACpDzB,KAAK,EAAEgB,YAAY;MACnBpF,QAAQ,EAAEyF;IACZ,CAAC,CAAC;EACJ;EACAnG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGM,eAAe,CAACgG,SAAS,GAAG;IAClE;AACJ;AACA;IACI9F,QAAQ,EAAElD,SAAS,CAACiJ,IAAI;IACxB;AACJ;AACA;IACIxF,eAAe,EAAEzD,SAAS,CAACkJ,GAAG;IAC9B;AACJ;AACA;IACI5H,qBAAqB,EAAEtB,SAAS,CAACmJ,MAAM;IACvC;AACJ;AACA;AACA;IACItF,WAAW,EAAE7D,SAAS,CAACmJ,MAAM;IAC7B;AACJ;AACA;IACIxF,oBAAoB,EAAE3D,SAAS,CAACoJ,IAAI;IACpC;AACJ;AACA;AACA;AACA;IACIxF,2BAA2B,EAAE5D,SAAS,CAACoJ,IAAI;IAC3C;AACJ;AACA;IACI5H,yBAAyB,EAAExB,SAAS,CAACoJ,IAAI;IACzC;AACJ;AACA;IACI7F,YAAY,EAAEvD,SAAS,CAACkJ,GAAG;IAC3B;AACJ;AACA;IACInF,kBAAkB,EAAE/D,SAAS,CAACoJ,IAAI;IAClC;AACJ;AACA;IACIhI,cAAc,EAAEpB,SAAS,CAACmJ,MAAM;IAChC;AACJ;AACA;AACA;IACInF,KAAK,EAAEhE,SAAS,CAACoJ,IAAI;IACrB;AACJ;AACA;AACA;IACIhG,cAAc,EAAEpD,SAAS,CAACqJ,IAAI;IAC9B;AACJ;AACA;AACA;IACIhG,aAAa,EAAErD,SAAS,CAACkJ,GAAG;IAC5B;AACJ;AACA;IACIhI,KAAK,EAAElB,SAAS,CAACsJ;EACnB,CAAC,GAAG,KAAK,CAAC;EACV,MAAMlE,uBAAuB,GAAG,OAAO1D,kBAAkB,KAAK,QAAQ,GAAGA,kBAAkB,GAAGA,kBAAkB,CAAC2D,KAAK;EACtH,MAAMC,sBAAsB,GAAG,OAAO5D,kBAAkB,KAAK,QAAQ,GAAGA,kBAAkB,GAAGA,kBAAkB,CAAC6D,IAAI;EACpH,MAAMgE,wBAAwB,GAAGC,MAAM,IAAIlJ,qBAAqB,CAAC;IAC/DgB,qBAAqB,EAAEC,4BAA4B;IACnD6D,uBAAuB;IACvBE,sBAAsB;IACtBlE,cAAc,EAAEC,qBAAqB;IACrC,GAAGmI;EACL,CAAC,CAAC;EACF,OAAO;IACLxG,eAAe;IACfJ,cAAc;IACd2G;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}