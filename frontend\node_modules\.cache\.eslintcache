[{"C:\\laragon\\www\\frontend\\src\\index.tsx": "1", "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts": "2", "C:\\laragon\\www\\frontend\\src\\App.tsx": "3", "C:\\laragon\\www\\frontend\\src\\pages\\Home.tsx": "4", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx": "5", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx": "6", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx": "7", "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx": "8", "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx": "9", "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx": "10", "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx": "11", "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx": "12", "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "13", "C:\\laragon\\www\\frontend\\src\\pages\\cms\\PageView.tsx": "14", "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx": "15", "C:\\laragon\\www\\frontend\\src\\services\\cmsService.ts": "16", "C:\\laragon\\www\\frontend\\src\\services\\authService.ts": "17", "C:\\laragon\\www\\frontend\\src\\services\\api.ts": "18", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Settings.tsx": "19", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx": "20", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Users.tsx": "21", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx": "22", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardLayout.tsx": "23", "C:\\laragon\\www\\frontend\\src\\theme\\materialDashboardTheme.ts": "24", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx": "25", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DataTable.tsx": "26", "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditPackages.tsx": "27", "C:\\laragon\\www\\frontend\\src\\components\\credit\\TransactionHistory.tsx": "28", "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditBalance.tsx": "29", "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts": "30", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx": "31", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx": "32", "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts": "33", "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx": "34", "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx": "35", "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx": "36"}, {"size": 554, "mtime": 1752503120783, "results": "37", "hashOfConfig": "38"}, {"size": 425, "mtime": 1752503120799, "results": "39", "hashOfConfig": "38"}, {"size": 4222, "mtime": 1752840768018, "results": "40", "hashOfConfig": "38"}, {"size": 7912, "mtime": 1752503120830, "results": "41", "hashOfConfig": "38"}, {"size": 4298, "mtime": 1752503120814, "results": "42", "hashOfConfig": "38"}, {"size": 7734, "mtime": 1752504934229, "results": "43", "hashOfConfig": "38"}, {"size": 3447, "mtime": 1752503120814, "results": "44", "hashOfConfig": "38"}, {"size": 5805, "mtime": 1752503120814, "results": "45", "hashOfConfig": "38"}, {"size": 4114, "mtime": 1752503120767, "results": "46", "hashOfConfig": "38"}, {"size": 4077, "mtime": 1752504117348, "results": "47", "hashOfConfig": "38"}, {"size": 11533, "mtime": 1752504965088, "results": "48", "hashOfConfig": "38"}, {"size": 2843, "mtime": 1752535330345, "results": "49", "hashOfConfig": "38"}, {"size": 1355, "mtime": 1752503120861, "results": "50", "hashOfConfig": "38"}, {"size": 4072, "mtime": 1752503120830, "results": "51", "hashOfConfig": "38"}, {"size": 3494, "mtime": 1752503120861, "results": "52", "hashOfConfig": "38"}, {"size": 2009, "mtime": 1752504140096, "results": "53", "hashOfConfig": "38"}, {"size": 3988, "mtime": 1752504129163, "results": "54", "hashOfConfig": "38"}, {"size": 1850, "mtime": 1752619528779, "results": "55", "hashOfConfig": "38"}, {"size": 5100, "mtime": 1752536025085, "results": "56", "hashOfConfig": "38"}, {"size": 3040, "mtime": 1752536704783, "results": "57", "hashOfConfig": "38"}, {"size": 2464, "mtime": 1752536620414, "results": "58", "hashOfConfig": "38"}, {"size": 708, "mtime": 1752534344051, "results": "59", "hashOfConfig": "38"}, {"size": 5570, "mtime": 1752840607407, "results": "60", "hashOfConfig": "38"}, {"size": 2924, "mtime": 1752534243344, "results": "61", "hashOfConfig": "38"}, {"size": 4729, "mtime": 1752536669944, "results": "62", "hashOfConfig": "38"}, {"size": 6604, "mtime": 1752536575892, "results": "63", "hashOfConfig": "38"}, {"size": 9496, "mtime": 1752840723303, "results": "64", "hashOfConfig": "38"}, {"size": 9031, "mtime": 1752584266287, "results": "65", "hashOfConfig": "38"}, {"size": 6155, "mtime": 1752584379676, "results": "66", "hashOfConfig": "38"}, {"size": 5397, "mtime": 1752794386649, "results": "67", "hashOfConfig": "38"}, {"size": 28953, "mtime": 1752707482487, "results": "68", "hashOfConfig": "38"}, {"size": 27549, "mtime": 1752710086718, "results": "69", "hashOfConfig": "38"}, {"size": 10226, "mtime": 1752621266300, "results": "70", "hashOfConfig": "38"}, {"size": 17358, "mtime": 1752708893705, "results": "71", "hashOfConfig": "38"}, {"size": 1430, "mtime": 1752673572781, "results": "72", "hashOfConfig": "38"}, {"size": 4607, "mtime": 1752841180101, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tace3p", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\laragon\\www\\frontend\\src\\index.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\laragon\\www\\frontend\\src\\App.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\Home.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Login.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\Register.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ForgotPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\auth\\ResetPassword.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\Profile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\user\\EditProfile.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\layout\\Navbar.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\cms\\PageView.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\auth\\EmailVerificationNotice.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\cmsService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\services\\authService.ts", [], [], "C:\\laragon\\www\\frontend\\src\\services\\api.ts", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Settings.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Dashboard.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Users.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardRoute.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DashboardLayout.tsx", ["182", "183"], [], "C:\\laragon\\www\\frontend\\src\\theme\\materialDashboardTheme.ts", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\ChartExample.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\DataTable.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditPackages.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\components\\credit\\TransactionHistory.tsx", ["184"], [], "C:\\laragon\\www\\frontend\\src\\components\\credit\\CreditBalance.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\services\\creditService.ts", ["185", "186"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Order.tsx", ["187", "188", "189", "190", "191", "192"], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Orders.tsx", ["193", "194", "195"], [], "C:\\laragon\\www\\frontend\\src\\services\\printingService.ts", ["196"], [], "C:\\laragon\\www\\frontend\\src\\components\\dashboard\\FileUpload.tsx", ["197", "198"], [], "C:\\laragon\\www\\frontend\\src\\components\\common\\ErrorBoundary.tsx", [], [], "C:\\laragon\\www\\frontend\\src\\pages\\dashboard\\Wallet.tsx", [], [], {"ruleId": "199", "severity": 1, "message": "200", "line": 26, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 26, "endColumn": 14}, {"ruleId": "199", "severity": 1, "message": "203", "line": 42, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 42, "endColumn": 17}, {"ruleId": "199", "severity": 1, "message": "204", "line": 41, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 41, "endColumn": 20}, {"ruleId": "199", "severity": 1, "message": "205", "line": 1, "column": 15, "nodeType": "201", "messageId": "202", "endLine": 1, "endColumn": 24}, {"ruleId": "206", "severity": 1, "message": "207", "line": 217, "column": 1, "nodeType": "208", "endLine": 217, "endColumn": 36}, {"ruleId": "199", "severity": 1, "message": "209", "line": 18, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 18, "endColumn": 14}, {"ruleId": "199", "severity": 1, "message": "210", "line": 19, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 19, "endColumn": 13}, {"ruleId": "199", "severity": 1, "message": "211", "line": 20, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 20, "endColumn": 9}, {"ruleId": "199", "severity": 1, "message": "212", "line": 21, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 21, "endColumn": 11}, {"ruleId": "199", "severity": 1, "message": "213", "line": 37, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 37, "endColumn": 20}, {"ruleId": "214", "severity": 1, "message": "215", "line": 135, "column": 6, "nodeType": "216", "endLine": 135, "endColumn": 50, "suggestions": "217"}, {"ruleId": "199", "severity": 1, "message": "218", "line": 27, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 27, "endColumn": 10}, {"ruleId": "199", "severity": 1, "message": "219", "line": 41, "column": 17, "nodeType": "201", "messageId": "202", "endLine": 41, "endColumn": 27}, {"ruleId": "214", "severity": 1, "message": "220", "line": 65, "column": 6, "nodeType": "216", "endLine": 65, "endColumn": 12, "suggestions": "221"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 335, "column": 1, "nodeType": "208", "endLine": 335, "endColumn": 38}, {"ruleId": "214", "severity": 1, "message": "222", "line": 76, "column": 6, "nodeType": "216", "endLine": 76, "endColumn": 15, "suggestions": "223"}, {"ruleId": "214", "severity": 1, "message": "224", "line": 188, "column": 6, "nodeType": "216", "endLine": 188, "endColumn": 40, "suggestions": "225"}, "@typescript-eslint/no-unused-vars", "'ChevronLeft' is defined but never used.", "Identifier", "unusedVar", "'isMobile' is assigned a value but never used.", "'totalPages' is assigned a value but never used.", "'endpoints' is defined but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'orderItems' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'calculatePrice'. Either include it or remove the dependency array.", "ArrayExpression", ["226"], "'Divider' is defined but never used.", "'FilterIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadOrders'. Either include it or remove the dependency array.", ["227"], "React Hook useEffect has missing dependencies: 'loadSettings' and 'loadUploadedFiles'. Either include them or remove the dependency array.", ["228"], "React Hook useCallback has missing dependencies: 'onError', 'uploadFiles', and 'validateFiles'. Either include them or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["229"], {"desc": "230", "fix": "231"}, {"desc": "232", "fix": "233"}, {"desc": "234", "fix": "235"}, {"desc": "236", "fix": "237"}, "Update the dependencies array to be: [selectedProduct, quantity, selectedOptions, calculatePrice]", {"range": "238", "text": "239"}, "Update the dependencies array to be: [loadOrders, page]", {"range": "240", "text": "241"}, "Update the dependencies array to be: [loadSettings, loadUploadedFiles, orderId]", {"range": "242", "text": "243"}, "Update the dependencies array to be: [onError, orderId, uploadFiles, validateFiles]", {"range": "244", "text": "245"}, [4644, 4688], "[selectedProduct, quantity, selectedOptions, calculatePrice]", [1616, 1622], "[loadOrders, page]", [2001, 2010], "[loadSettings, loadUploadedFiles, orderId]", [5634, 5668], "[onError, orderId, uploadFiles, validateFiles]"]