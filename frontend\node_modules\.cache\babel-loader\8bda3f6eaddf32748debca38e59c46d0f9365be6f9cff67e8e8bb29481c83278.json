{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormContext from './FormContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormSelect = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    size,\n    htmlSize,\n    className,\n    isValid = false,\n    isInvalid = false,\n    id,\n    ...props\n  } = _ref;\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-select');\n  return /*#__PURE__*/_jsx(\"select\", {\n    ...props,\n    size: htmlSize,\n    ref: ref,\n    className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, isValid && `is-valid`, isInvalid && `is-invalid`),\n    id: id || controlId\n  });\n});\nFormSelect.displayName = 'FormSelect';\nexport default FormSelect;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useBootstrapPrefix", "FormContext", "jsx", "_jsx", "FormSelect", "forwardRef", "_ref", "ref", "bsPrefix", "size", "htmlSize", "className", "<PERSON><PERSON><PERSON><PERSON>", "isInvalid", "id", "props", "controlId", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/FormSelect.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormContext from './FormContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormSelect = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  htmlSize,\n  className,\n  isValid = false,\n  isInvalid = false,\n  id,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-select');\n  return /*#__PURE__*/_jsx(\"select\", {\n    ...props,\n    size: htmlSize,\n    ref: ref,\n    className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, isValid && `is-valid`, isInvalid && `is-invalid`),\n    id: id || controlId\n  });\n});\nFormSelect.displayName = 'FormSelect';\nexport default FormSelect;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAAC,IAAA,EAS9CC,GAAG,KAAK;EAAA,IATuC;IAChDC,QAAQ;IACRC,IAAI;IACJC,QAAQ;IACRC,SAAS;IACTC,OAAO,GAAG,KAAK;IACfC,SAAS,GAAG,KAAK;IACjBC,EAAE;IACF,GAAGC;EACL,CAAC,GAAAT,IAAA;EACC,MAAM;IACJU;EACF,CAAC,GAAGjB,UAAU,CAACE,WAAW,CAAC;EAC3BO,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,aAAa,CAAC;EACtD,OAAO,aAAaL,IAAI,CAAC,QAAQ,EAAE;IACjC,GAAGY,KAAK;IACRN,IAAI,EAAEC,QAAQ;IACdH,GAAG,EAAEA,GAAG;IACRI,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAEH,QAAQ,EAAEC,IAAI,IAAI,GAAGD,QAAQ,IAAIC,IAAI,EAAE,EAAEG,OAAO,IAAI,UAAU,EAAEC,SAAS,IAAI,YAAY,CAAC;IAC3HC,EAAE,EAAEA,EAAE,IAAIE;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFZ,UAAU,CAACa,WAAW,GAAG,YAAY;AACrC,eAAeb,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}