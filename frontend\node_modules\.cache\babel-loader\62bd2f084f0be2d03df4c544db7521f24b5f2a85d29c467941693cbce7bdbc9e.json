{"ast": null, "code": "export { default } from \"./Breadcrumbs.js\";\nexport { default as breadcrumbsClasses } from \"./breadcrumbsClasses.js\";\nexport * from \"./breadcrumbsClasses.js\";", "map": {"version": 3, "names": ["default", "breadcrumbsClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Breadcrumbs/index.js"], "sourcesContent": ["export { default } from \"./Breadcrumbs.js\";\nexport { default as breadcrumbsClasses } from \"./breadcrumbsClasses.js\";\nexport * from \"./breadcrumbsClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,yBAAyB;AACvE,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}