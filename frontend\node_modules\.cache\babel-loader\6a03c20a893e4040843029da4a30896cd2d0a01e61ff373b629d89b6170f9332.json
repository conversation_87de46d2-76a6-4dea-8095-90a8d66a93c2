{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    className,\n    bsPrefix,\n    as: Component = Anchor,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "map": {"version": 3, "names": ["React", "classNames", "<PERSON><PERSON>", "useBootstrapPrefix", "jsx", "_jsx", "AlertLink", "forwardRef", "_ref", "ref", "className", "bsPrefix", "as", "Component", "props", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/AlertLink.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAAC,IAAA,EAK7CC,GAAG,KAAK;EAAA,IALsC;IAC/CC,SAAS;IACTC,QAAQ;IACRC,EAAE,EAAEC,SAAS,GAAGX,MAAM;IACtB,GAAGY;EACL,CAAC,GAAAN,IAAA;EACCG,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,YAAY,CAAC;EACrD,OAAO,aAAaN,IAAI,CAACQ,SAAS,EAAE;IAClCJ,GAAG,EAAEA,GAAG;IACRC,SAAS,EAAET,UAAU,CAACS,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,SAAS,CAACS,WAAW,GAAG,WAAW;AACnC,eAAeT,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}