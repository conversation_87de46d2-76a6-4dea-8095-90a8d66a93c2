{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\pages\\\\dashboard\\\\Dashboard.tsx\";\nimport React from 'react';\nimport { Card, CardContent, Typography, Box, Paper, Avatar } from '@mui/material';\nimport { DashboardCharts } from '../../components/dashboard/ChartExample';\nimport { TrendingUp, People, ShoppingCart, AttachMoney } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatCard = ({\n  title,\n  value,\n  icon,\n  color,\n  change\n}) => /*#__PURE__*/_jsxDEV(Card, {\n  sx: {\n    height: '100%'\n  },\n  children: /*#__PURE__*/_jsxDEV(CardContent, {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          color: \"textSecondary\",\n          gutterBottom: true,\n          variant: \"overline\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"div\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), change && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'success.main',\n            mt: 1\n          },\n          children: change\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          bgcolor: color,\n          width: 56,\n          height: 56\n        },\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 27,\n  columnNumber: 3\n}, this);\n_c = StatCard;\nconst Dashboard = () => {\n  const stats = [{\n    title: 'Total Users',\n    value: '1,234',\n    icon: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 13\n    }, this),\n    color: '#1976d2',\n    change: '+12% from last month'\n  }, {\n    title: 'Revenue',\n    value: '$45,678',\n    icon: /*#__PURE__*/_jsxDEV(AttachMoney, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    color: '#4caf50',\n    change: '+8% from last month'\n  }, {\n    title: 'Orders',\n    value: '567',\n    icon: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 13\n    }, this),\n    color: '#ff9800',\n    change: '+15% from last month'\n  }, {\n    title: 'Growth',\n    value: '23.5%',\n    icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this),\n    color: '#9c27b0',\n    change: '+3% from last month'\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"h1\",\n      gutterBottom: true,\n      children: \"Dashboard Overview\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"textSecondary\",\n      sx: {\n        mb: 2\n      },\n      children: \"Welcome to your Material Dashboard! Here's an overview of your key metrics.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'grid',\n        gridTemplateColumns: {\n          xs: '1fr',\n          sm: '1fr 1fr',\n          md: '1fr 1fr 1fr 1fr'\n        },\n        gap: 3\n      },\n      children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(StatCard, {\n        ...stat\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        gutterBottom: true,\n        children: \"Analytics Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DashboardCharts, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: \"Add quick action buttons or widgets here for common tasks like creating new users, generating reports, or accessing frequently used features.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n};\n_c2 = Dashboard;\nexport default Dashboard;\nvar _c, _c2;\n$RefreshReg$(_c, \"StatCard\");\n$RefreshReg$(_c2, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Box", "Paper", "Avatar", "DashboardCharts", "TrendingUp", "People", "ShoppingCart", "AttachMoney", "jsxDEV", "_jsxDEV", "StatCard", "title", "value", "icon", "color", "change", "sx", "height", "children", "display", "alignItems", "justifyContent", "gutterBottom", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "mt", "bgcolor", "width", "_c", "Dashboard", "stats", "mb", "gridTemplateColumns", "xs", "sm", "md", "gap", "map", "stat", "index", "p", "_c2", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/pages/dashboard/Dashboard.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Card,\n  CardContent,\n  Typography,\n  Box,\n  Paper,\n  Avatar,\n} from '@mui/material';\nimport { DashboardCharts } from '../../components/dashboard/ChartExample';\nimport {\n  TrendingUp,\n  People,\n  ShoppingCart,\n  AttachMoney,\n} from '@mui/icons-material';\n\ninterface StatCardProps {\n  title: string;\n  value: string;\n  icon: React.ReactNode;\n  color: string;\n  change?: string;\n}\n\nconst StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, change }) => (\n  <Card sx={{ height: '100%' }}>\n    <CardContent>\n      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box>\n          <Typography color=\"textSecondary\" gutterBottom variant=\"overline\">\n            {title}\n          </Typography>\n          <Typography variant=\"h4\" component=\"div\">\n            {value}\n          </Typography>\n          {change && (\n            <Typography variant=\"body2\" sx={{ color: 'success.main', mt: 1 }}>\n              {change}\n            </Typography>\n          )}\n        </Box>\n        <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>\n          {icon}\n        </Avatar>\n      </Box>\n    </CardContent>\n  </Card>\n);\n\nconst Dashboard: React.FC = () => {\n  const stats = [\n    {\n      title: 'Total Users',\n      value: '1,234',\n      icon: <People />,\n      color: '#1976d2',\n      change: '+12% from last month',\n    },\n    {\n      title: 'Revenue',\n      value: '$45,678',\n      icon: <AttachMoney />,\n      color: '#4caf50',\n      change: '+8% from last month',\n    },\n    {\n      title: 'Orders',\n      value: '567',\n      icon: <ShoppingCart />,\n      color: '#ff9800',\n      change: '+15% from last month',\n    },\n    {\n      title: 'Growth',\n      value: '23.5%',\n      icon: <TrendingUp />,\n      color: '#9c27b0',\n      change: '+3% from last month',\n    },\n  ];\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n        Dashboard Overview\n      </Typography>\n      <Typography variant=\"body1\" color=\"textSecondary\" sx={{ mb: 2 }}>\n        Welcome to your Material Dashboard! Here's an overview of your key metrics.\n      </Typography>\n\n      <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' }, gap: 3 }}>\n        {stats.map((stat, index) => (\n          <StatCard key={index} {...stat} />\n        ))}\n      </Box>\n\n      <Box sx={{ mt: 3 }}>\n        <Typography variant=\"h5\" gutterBottom>\n          Analytics Overview\n        </Typography>\n        <DashboardCharts />\n      </Box>\n\n      <Box sx={{ mt: 3 }}>\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Quick Actions\n          </Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">\n            Add quick action buttons or widgets here for common tasks like creating new users,\n            generating reports, or accessing frequently used features.\n          </Typography>\n        </Paper>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,MAAM,QACD,eAAe;AACtB,SAASC,eAAe,QAAQ,yCAAyC;AACzE,SACEC,UAAU,EACVC,MAAM,EACNC,YAAY,EACZC,WAAW,QACN,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU7B,MAAMC,QAAiC,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,IAAI;EAAEC,KAAK;EAAEC;AAAO,CAAC,kBAC9EN,OAAA,CAACZ,IAAI;EAACmB,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAO,CAAE;EAAAC,QAAA,eAC3BT,OAAA,CAACX,WAAW;IAAAoB,QAAA,eACVT,OAAA,CAACT,GAAG;MAACgB,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAH,QAAA,gBAClFT,OAAA,CAACT,GAAG;QAAAkB,QAAA,gBACFT,OAAA,CAACV,UAAU;UAACe,KAAK,EAAC,eAAe;UAACQ,YAAY;UAACC,OAAO,EAAC,UAAU;UAAAL,QAAA,EAC9DP;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACblB,OAAA,CAACV,UAAU;UAACwB,OAAO,EAAC,IAAI;UAACK,SAAS,EAAC,KAAK;UAAAV,QAAA,EACrCN;QAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EACZZ,MAAM,iBACLN,OAAA,CAACV,UAAU;UAACwB,OAAO,EAAC,OAAO;UAACP,EAAE,EAAE;YAAEF,KAAK,EAAE,cAAc;YAAEe,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,EAC9DH;QAAM;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNlB,OAAA,CAACP,MAAM;QAACc,EAAE,EAAE;UAAEc,OAAO,EAAEhB,KAAK;UAAEiB,KAAK,EAAE,EAAE;UAAEd,MAAM,EAAE;QAAG,CAAE;QAAAC,QAAA,EACnDL;MAAI;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACV,CACP;AAACK,EAAA,GAvBItB,QAAiC;AAyBvC,MAAMuB,SAAmB,GAAGA,CAAA,KAAM;EAChC,MAAMC,KAAK,GAAG,CACZ;IACEvB,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,OAAO;IACdC,IAAI,eAAEJ,OAAA,CAACJ,MAAM;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBb,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,SAAS;IAChBC,IAAI,eAAEJ,OAAA,CAACF,WAAW;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBb,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,KAAK;IACZC,IAAI,eAAEJ,OAAA,CAACH,YAAY;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBb,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,OAAO;IACdC,IAAI,eAAEJ,OAAA,CAACL,UAAU;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBb,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE;EACV,CAAC,CACF;EAED,oBACEN,OAAA,CAACT,GAAG;IAAAkB,QAAA,gBACFT,OAAA,CAACV,UAAU;MAACwB,OAAO,EAAC,IAAI;MAACK,SAAS,EAAC,IAAI;MAACN,YAAY;MAAAJ,QAAA,EAAC;IAErD;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACblB,OAAA,CAACV,UAAU;MAACwB,OAAO,EAAC,OAAO;MAACT,KAAK,EAAC,eAAe;MAACE,EAAE,EAAE;QAAEmB,EAAE,EAAE;MAAE,CAAE;MAAAjB,QAAA,EAAC;IAEjE;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEblB,OAAA,CAACT,GAAG;MAACgB,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEiB,mBAAmB,EAAE;UAAEC,EAAE,EAAE,KAAK;UAAEC,EAAE,EAAE,SAAS;UAAEC,EAAE,EAAE;QAAkB,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAAtB,QAAA,EAC5GgB,KAAK,CAACO,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBlC,OAAA,CAACC,QAAQ;QAAA,GAAiBgC;MAAI,GAAfC,KAAK;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAClC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENlB,OAAA,CAACT,GAAG;MAACgB,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACjBT,OAAA,CAACV,UAAU;QAACwB,OAAO,EAAC,IAAI;QAACD,YAAY;QAAAJ,QAAA,EAAC;MAEtC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblB,OAAA,CAACN,eAAe;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAENlB,OAAA,CAACT,GAAG;MAACgB,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,eACjBT,OAAA,CAACR,KAAK;QAACe,EAAE,EAAE;UAAE4B,CAAC,EAAE;QAAE,CAAE;QAAA1B,QAAA,gBAClBT,OAAA,CAACV,UAAU;UAACwB,OAAO,EAAC,IAAI;UAACD,YAAY;UAAAJ,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblB,OAAA,CAACV,UAAU;UAACwB,OAAO,EAAC,OAAO;UAACT,KAAK,EAAC,eAAe;UAAAI,QAAA,EAAC;QAGlD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACkB,GAAA,GAnEIZ,SAAmB;AAqEzB,eAAeA,SAAS;AAAC,IAAAD,EAAA,EAAAa,GAAA;AAAAC,YAAA,CAAAd,EAAA;AAAAc,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}