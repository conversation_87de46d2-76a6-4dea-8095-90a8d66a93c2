{"ast": null, "code": "import { unstable_createUseMediaQuery } from '@mui/system/useMediaQuery';\nimport THEME_ID from \"../styles/identifier.js\";\nconst useMediaQuery = unstable_createUseMediaQuery({\n  themeId: THEME_ID\n});\nexport default useMediaQuery;", "map": {"version": 3, "names": ["unstable_createUseMediaQuery", "THEME_ID", "useMediaQuery", "themeId"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/useMediaQuery/index.js"], "sourcesContent": ["import { unstable_createUseMediaQuery } from '@mui/system/useMediaQuery';\nimport THEME_ID from \"../styles/identifier.js\";\nconst useMediaQuery = unstable_createUseMediaQuery({\n  themeId: THEME_ID\n});\nexport default useMediaQuery;"], "mappings": "AAAA,SAASA,4BAA4B,QAAQ,2BAA2B;AACxE,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,MAAMC,aAAa,GAAGF,4BAA4B,CAAC;EACjDG,OAAO,EAAEF;AACX,CAAC,CAAC;AACF,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}