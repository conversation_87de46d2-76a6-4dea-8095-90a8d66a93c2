{"ast": null, "code": "export { default } from \"./AlertTitle.js\";\nexport { default as alertTitleClasses } from \"./alertTitleClasses.js\";\nexport * from \"./alertTitleClasses.js\";", "map": {"version": 3, "names": ["default", "alertTitleClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/AlertTitle/index.js"], "sourcesContent": ["export { default } from \"./AlertTitle.js\";\nexport { default as alertTitleClasses } from \"./alertTitleClasses.js\";\nexport * from \"./alertTitleClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,SAASA,OAAO,IAAIC,iBAAiB,QAAQ,wBAAwB;AACrE,cAAc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}