{"ast": null, "code": "export { default } from \"./Alert.js\";\nexport { default as alertClasses } from \"./alertClasses.js\";\nexport * from \"./alertClasses.js\";", "map": {"version": 3, "names": ["default", "alertClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Alert/index.js"], "sourcesContent": ["export { default } from \"./Alert.js\";\nexport { default as alertClasses } from \"./alertClasses.js\";\nexport * from \"./alertClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASA,OAAO,IAAIC,YAAY,QAAQ,mBAAmB;AAC3D,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}