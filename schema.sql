-- =====================================================
-- Comprehensive Database Schema for Wallet-Based Application
-- =====================================================
-- This schema reflects the current application state with wallet-based monetary system
-- Currency: Malaysian Ringgit (MYR) with DECIMAL(10,2) precision
-- Generated: 2025-07-18
-- =====================================================

SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- CORE AUTHENTICATION & USER MANAGEMENT TABLES
-- =====================================================

-- Users table with wallet balance (migrated from credit system)
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `role` enum('user','admin') NOT NULL DEFAULT 'user',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `wallet_balance` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'User wallet balance in MYR',
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  KEY `users_role_is_active_index` (`role`, `is_active`),
  KEY `users_wallet_balance_index` (`wallet_balance`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User accounts with wallet balance';

-- Password reset tokens
DROP TABLE IF EXISTS `password_reset_tokens`;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Password reset tokens';

-- User sessions
DROP TABLE IF EXISTS `sessions`;
CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User session data';

-- =====================================================
-- WALLET & PAYMENT SYSTEM TABLES
-- =====================================================

-- Credit packages (top-up packages) - Created first to avoid foreign key issues
DROP TABLE IF EXISTS `credit_packages`;
CREATE TABLE `credit_packages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL COMMENT 'Package price in MYR',
  `credit_amount` int(11) NOT NULL COMMENT 'Credit amount provided',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `features` json DEFAULT NULL COMMENT 'Package features list',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `credit_packages_is_active_sort_index` (`is_active`, `sort_order`),
  KEY `credit_packages_price_index` (`price`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Wallet top-up packages';

-- Wallet transactions (migrated from credit_transactions)
DROP TABLE IF EXISTS `wallet_transactions`;
CREATE TABLE `wallet_transactions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `package_id` bigint(20) unsigned DEFAULT NULL COMMENT 'Reference to credit_packages for top-up transactions',
  `type` enum('top_up','payment','withdrawal','refund','bonus','adjustment') NOT NULL COMMENT 'Transaction type',
  `amount` decimal(10,2) NOT NULL COMMENT 'Transaction amount in MYR',
  `amount_paid` decimal(10,2) DEFAULT NULL COMMENT 'Actual amount paid (for top-ups)',
  `payment_method` varchar(255) DEFAULT NULL COMMENT 'Payment method (billplz, manual, etc.)',
  `payment_reference` varchar(255) DEFAULT NULL COMMENT 'External payment reference',
  `payment_status` enum('pending','completed','failed','cancelled','refunded') NOT NULL DEFAULT 'pending',
  `description` text NOT NULL COMMENT 'Transaction description',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT 'When transaction was processed',
  `withdrawal_method` varchar(255) DEFAULT NULL COMMENT 'Withdrawal method for withdrawal transactions',
  `withdrawal_reference` varchar(255) DEFAULT NULL COMMENT 'Withdrawal reference number',
  `withdrawal_processed_at` timestamp NULL DEFAULT NULL COMMENT 'When withdrawal was processed',
  `metadata` json DEFAULT NULL COMMENT 'Additional transaction metadata',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `wallet_transactions_user_id_foreign` (`user_id`),
  KEY `wallet_transactions_package_id_foreign` (`package_id`),
  KEY `wallet_transactions_user_type_status_index` (`user_id`, `type`, `payment_status`),
  KEY `wallet_transactions_type_created_index` (`type`, `created_at`),
  KEY `wallet_transactions_payment_status_index` (`payment_status`),
  CONSTRAINT `wallet_transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `wallet_transactions_package_id_foreign` FOREIGN KEY (`package_id`) REFERENCES `credit_packages` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Wallet transaction history';

-- Payment settings (Billplz and other payment configurations)
DROP TABLE IF EXISTS `payment_settings`;
CREATE TABLE `payment_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) NOT NULL,
  `value` text DEFAULT NULL,
  `type` varchar(255) NOT NULL DEFAULT 'string' COMMENT 'string, boolean, integer, decimal, json',
  `description` text DEFAULT NULL,
  `is_encrypted` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether value is encrypted',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payment_settings_key_unique` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Payment gateway settings';

-- =====================================================
-- CONTENT MANAGEMENT SYSTEM TABLES
-- =====================================================

-- CMS Pages
DROP TABLE IF EXISTS `cms_pages`;
CREATE TABLE `cms_pages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` varchar(255) DEFAULT NULL,
  `is_published` tinyint(1) NOT NULL DEFAULT 0,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `featured_image` varchar(255) DEFAULT NULL,
  `created_by` bigint(20) unsigned NOT NULL,
  `updated_by` bigint(20) unsigned DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cms_pages_slug_unique` (`slug`),
  KEY `cms_pages_created_by_foreign` (`created_by`),
  KEY `cms_pages_updated_by_foreign` (`updated_by`),
  KEY `cms_pages_published_index` (`is_published`, `published_at`),
  KEY `cms_pages_slug_index` (`slug`),
  CONSTRAINT `cms_pages_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `cms_pages_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS pages and content';

-- =====================================================
-- EMAIL SYSTEM TABLES
-- =====================================================

-- Email templates
DROP TABLE IF EXISTS `email_templates`;
CREATE TABLE `email_templates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL COMMENT 'registration, password_reset, welcome, etc.',
  `subject` varchar(255) NOT NULL,
  `body_html` longtext NOT NULL,
  `body_text` longtext DEFAULT NULL,
  `variables` json DEFAULT NULL COMMENT 'Available template variables',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email_templates_name_unique` (`name`),
  KEY `email_templates_type_active_index` (`type`, `is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Email templates for notifications';

-- Email notifications log
DROP TABLE IF EXISTS `email_notifications`;
CREATE TABLE `email_notifications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `type` varchar(255) NOT NULL COMMENT 'registration, password_reset, etc.',
  `subject` varchar(255) NOT NULL,
  `body` longtext NOT NULL,
  `data` json DEFAULT NULL COMMENT 'Additional notification data',
  `sent_at` timestamp NULL DEFAULT NULL,
  `is_sent` tinyint(1) NOT NULL DEFAULT 0,
  `status` varchar(255) NOT NULL DEFAULT 'pending' COMMENT 'pending, sent, failed',
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `email_notifications_user_id_foreign` (`user_id`),
  KEY `email_notifications_user_type_index` (`user_id`, `type`),
  KEY `email_notifications_sent_status_index` (`is_sent`, `status`),
  CONSTRAINT `email_notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Email notification tracking';

-- =====================================================
-- PRINTING & ORDER MANAGEMENT TABLES
-- =====================================================

-- Printing categories
DROP TABLE IF EXISTS `printing_categories`;
CREATE TABLE `printing_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `slug` varchar(255) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `printing_categories_slug_unique` (`slug`),
  KEY `printing_categories_active_sort_index` (`is_active`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Printing service categories';

-- Printing products
DROP TABLE IF EXISTS `printing_products`;
CREATE TABLE `printing_products` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `printing_category_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `slug` varchar(255) NOT NULL,
  `base_price` decimal(10,2) NOT NULL COMMENT 'Base price in MYR',
  `image` varchar(255) DEFAULT NULL,
  `specifications` json DEFAULT NULL COMMENT 'Size, material, finish options',
  `options` json DEFAULT NULL COMMENT 'Pricing tiers, customization options',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `min_quantity` int(11) NOT NULL DEFAULT 1,
  `max_quantity` int(11) DEFAULT NULL,
  `production_time_days` int(11) NOT NULL DEFAULT 3,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `printing_products_slug_unique` (`slug`),
  KEY `printing_products_category_id_foreign` (`printing_category_id`),
  KEY `printing_products_category_active_index` (`printing_category_id`, `is_active`),
  KEY `printing_products_active_sort_index` (`is_active`, `sort_order`),
  CONSTRAINT `printing_products_category_id_foreign` FOREIGN KEY (`printing_category_id`) REFERENCES `printing_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Printing products and services';

-- Printing orders
DROP TABLE IF EXISTS `printing_orders`;
CREATE TABLE `printing_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `order_number` varchar(255) NOT NULL,
  `status` enum('pending','confirmed','in_production','quality_check','completed','shipped','delivered','cancelled') NOT NULL DEFAULT 'pending',
  `total_amount` decimal(10,2) NOT NULL COMMENT 'Total order amount in MYR',
  `payment_status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending',
  `payment_method` varchar(255) DEFAULT NULL,
  `payment_reference` varchar(255) DEFAULT NULL,
  `special_instructions` text DEFAULT NULL,
  `delivery_address` json DEFAULT NULL COMMENT 'Delivery address details',
  `delivery_method` varchar(255) DEFAULT NULL,
  `estimated_completion_date` date DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `shipped_at` timestamp NULL DEFAULT NULL,
  `notes` text DEFAULT NULL COMMENT 'Admin notes',
  `metadata` json DEFAULT NULL COMMENT 'Additional order data',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `printing_orders_order_number_unique` (`order_number`),
  KEY `printing_orders_user_id_foreign` (`user_id`),
  KEY `printing_orders_user_status_index` (`user_id`, `status`),
  KEY `printing_orders_status_created_index` (`status`, `created_at`),
  KEY `printing_orders_order_number_index` (`order_number`),
  CONSTRAINT `printing_orders_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Printing orders';

-- Order items
DROP TABLE IF EXISTS `order_items`;
CREATE TABLE `order_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `printing_order_id` bigint(20) unsigned NOT NULL,
  `printing_product_id` bigint(20) unsigned NOT NULL,
  `quantity` int(11) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL COMMENT 'Unit price in MYR',
  `total_price` decimal(10,2) NOT NULL COMMENT 'Total price in MYR',
  `specifications` json DEFAULT NULL COMMENT 'Size, material, finish selected',
  `selected_options` json DEFAULT NULL COMMENT 'Customer selections',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `order_items_printing_order_id_foreign` (`printing_order_id`),
  KEY `order_items_printing_product_id_foreign` (`printing_product_id`),
  KEY `order_items_order_index` (`printing_order_id`),
  KEY `order_items_product_index` (`printing_product_id`),
  CONSTRAINT `order_items_printing_order_id_foreign` FOREIGN KEY (`printing_order_id`) REFERENCES `printing_orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `order_items_printing_product_id_foreign` FOREIGN KEY (`printing_product_id`) REFERENCES `printing_products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Individual items within printing orders';

-- Order files
DROP TABLE IF EXISTS `order_files`;
CREATE TABLE `order_files` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `printing_order_id` bigint(20) unsigned NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_size` bigint(20) NOT NULL COMMENT 'File size in bytes',
  `mime_type` varchar(255) NOT NULL,
  `file_type` enum('artwork','reference','proof') NOT NULL DEFAULT 'artwork',
  `dimensions` json DEFAULT NULL COMMENT 'Width, height in pixels',
  `dpi` int(11) DEFAULT NULL,
  `is_approved` tinyint(1) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL,
  `uploaded_by` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `order_files_printing_order_id_foreign` (`printing_order_id`),
  KEY `order_files_uploaded_by_foreign` (`uploaded_by`),
  KEY `order_files_order_type_index` (`printing_order_id`, `file_type`),
  CONSTRAINT `order_files_printing_order_id_foreign` FOREIGN KEY (`printing_order_id`) REFERENCES `printing_orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `order_files_uploaded_by_foreign` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Files uploaded for printing orders';

-- =====================================================
-- SYSTEM CONFIGURATION TABLES
-- =====================================================

-- File upload settings
DROP TABLE IF EXISTS `file_upload_settings`;
CREATE TABLE `file_upload_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) NOT NULL,
  `value` text DEFAULT NULL,
  `type` varchar(255) NOT NULL DEFAULT 'string' COMMENT 'string, boolean, integer, decimal, json',
  `description` text DEFAULT NULL,
  `is_encrypted` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether value is encrypted',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `file_upload_settings_key_unique` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='File upload configuration settings';

-- =====================================================
-- INDEXES AND CONSTRAINTS SUMMARY
-- =====================================================
-- Additional performance indexes for wallet system

-- Wallet transaction performance indexes
ALTER TABLE `wallet_transactions` ADD INDEX `wallet_transactions_amount_index` (`amount`);
ALTER TABLE `wallet_transactions` ADD INDEX `wallet_transactions_processed_at_index` (`processed_at`);

-- User wallet balance index for admin queries
ALTER TABLE `users` ADD INDEX `users_wallet_balance_desc_index` (`wallet_balance` DESC);

-- Order performance indexes
ALTER TABLE `printing_orders` ADD INDEX `printing_orders_total_amount_index` (`total_amount`);
ALTER TABLE `printing_orders` ADD INDEX `printing_orders_payment_status_index` (`payment_status`);

-- =====================================================
-- ENABLE FOREIGN KEY CHECKS
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- SCHEMA NOTES
-- =====================================================
/*
WALLET SYSTEM MIGRATION NOTES:
- All monetary fields use DECIMAL(10,2) for Malaysian Ringgit (MYR) precision
- Users table contains wallet_balance instead of credit_balance
- wallet_transactions table replaces credit_transactions with enhanced features
- Transaction types: top_up, payment, withdrawal, refund, bonus, adjustment
- All foreign key constraints are properly defined for data integrity
- Indexes are optimized for common query patterns

CURRENCY HANDLING:
- All prices and amounts are stored in MYR with 2 decimal places
- 1:1 conversion rate maintained during migration from credit system
- No balance limits enforced at database level (handled in application)
- Strict overdraft prevention implemented in application layer

BILLPLZ INTEGRATION:
- payment_settings table stores Billplz configuration
- wallet_transactions.payment_reference stores Billplz bill IDs
- payment_status tracks transaction states for payment gateway

PERFORMANCE OPTIMIZATIONS:
- Composite indexes on frequently queried column combinations
- Separate indexes for admin dashboard queries
- JSON columns for flexible metadata storage
- Proper foreign key relationships for data integrity

BACKUP RECOMMENDATIONS:
- Regular backups of wallet_transactions table (financial data)
- Point-in-time recovery capability for payment processing
- Audit trail preservation for compliance requirements
*/
