{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\FileUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Box, Typography, Paper, Button, LinearProgress, Alert, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, Card, CardContent, CardActions, Tooltip } from '@mui/material';\nimport { CloudUpload as UploadIcon, Delete as DeleteIcon, Warning as WarningIcon, CheckCircle as CheckIcon, Error as ErrorIcon, Image as ImageIcon, PictureAsPdf as PdfIcon, Description as FileIcon } from '@mui/icons-material';\nimport { useDropzone } from 'react-dropzone';\nimport printingService from '../../services/printingService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FileUpload = ({\n  orderId,\n  onFilesUploaded,\n  onError\n}) => {\n  _s();\n  const [settings, setSettings] = useState(null);\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [uploadingFiles, setUploadingFiles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [fileToDelete, setFileToDelete] = useState(null);\n\n  // Load settings and files on component mount\n  useEffect(() => {\n    loadSettings();\n    if (orderId) {\n      loadUploadedFiles();\n    }\n  }, [orderId]);\n  const loadSettings = async () => {\n    try {\n      const uploadSettings = await printingService.getUploadSettings();\n\n      // Ensure allowed_file_types is always an array\n      if (uploadSettings && !Array.isArray(uploadSettings.allowed_file_types)) {\n        uploadSettings.allowed_file_types = ['pdf', 'png', 'jpg', 'jpeg'];\n      }\n      setSettings(uploadSettings);\n    } catch (error) {\n      console.error('Failed to load upload settings:', error);\n      onError === null || onError === void 0 ? void 0 : onError('Failed to load upload settings');\n\n      // Set default settings if loading fails\n      setSettings({\n        allowed_file_types: ['pdf', 'png', 'jpg', 'jpeg'],\n        max_file_size_mb: 50,\n        max_total_upload_size_mb: 200,\n        max_files_per_order: 10,\n        min_dpi_requirement: 300,\n        dpi_warning_threshold: 150,\n        enable_dpi_validation: true,\n        min_width_px: 100,\n        min_height_px: 100,\n        max_width_px: 10000,\n        max_height_px: 10000,\n        enable_dimension_validation: true\n      });\n    }\n  };\n  const loadUploadedFiles = async () => {\n    if (!orderId) return;\n    try {\n      const files = await printingService.getOrderFiles(orderId);\n      setUploadedFiles(files);\n    } catch (error) {\n      console.error('Failed to load uploaded files:', error);\n    }\n  };\n\n  // Validate files before upload\n  const validateFiles = async files => {\n    if (!settings) return {\n      valid: [],\n      invalid: []\n    };\n    const valid = [];\n    const invalid = [];\n\n    // Check total file count\n    const totalFiles = uploadedFiles.length + files.length;\n    if (totalFiles > settings.max_files_per_order) {\n      const excess = totalFiles - settings.max_files_per_order;\n      for (let i = files.length - excess; i < files.length; i++) {\n        invalid.push({\n          file: files[i],\n          errors: [`Maximum ${settings.max_files_per_order} files allowed per order`]\n        });\n      }\n      files = files.slice(0, files.length - excess);\n    }\n\n    // Check total upload size\n    const currentTotalSize = uploadedFiles.reduce((sum, file) => sum + file.file_size, 0);\n    const newFilesSize = files.reduce((sum, file) => sum + file.size, 0);\n    const totalSize = currentTotalSize + newFilesSize;\n    const maxTotalSize = settings.max_total_upload_size_mb * 1024 * 1024;\n    if (totalSize > maxTotalSize) {\n      onError === null || onError === void 0 ? void 0 : onError(`Total upload size would exceed ${settings.max_total_upload_size_mb}MB limit`);\n      return {\n        valid: [],\n        invalid: files.map(file => ({\n          file,\n          errors: ['Total size limit exceeded']\n        }))\n      };\n    }\n\n    // Validate each file\n    for (const file of files) {\n      const validation = await printingService.validateFile(file);\n      if (validation.valid) {\n        valid.push(file);\n      } else {\n        invalid.push({\n          file,\n          errors: validation.message ? [validation.message] : ['Validation failed']\n        });\n      }\n    }\n    return {\n      valid,\n      invalid\n    };\n  };\n  const onDrop = useCallback(async acceptedFiles => {\n    if (!orderId) {\n      onError === null || onError === void 0 ? void 0 : onError('Please create an order first before uploading files');\n      return;\n    }\n    const {\n      valid,\n      invalid\n    } = await validateFiles(acceptedFiles);\n\n    // Show errors for invalid files\n    if (invalid.length > 0) {\n      const errorMessages = invalid.map(({\n        file,\n        errors\n      }) => `${file.name}: ${errors.join(', ')}`).join('\\n');\n      onError === null || onError === void 0 ? void 0 : onError(errorMessages);\n    }\n\n    // Upload valid files\n    if (valid.length > 0) {\n      await uploadFiles(valid);\n    }\n  }, [orderId, settings, uploadedFiles]);\n  const uploadFiles = async files => {\n    setLoading(true);\n\n    // Initialize uploading files state\n    const newUploadingFiles = files.map(file => ({\n      file,\n      progress: 0,\n      status: 'uploading'\n    }));\n    setUploadingFiles(newUploadingFiles);\n    try {\n      const uploadedFileResults = await printingService.uploadFiles(orderId, files, 'artwork');\n\n      // Update uploading files to success\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        progress: 100,\n        status: 'success'\n      })));\n\n      // Add to uploaded files list\n      setUploadedFiles(prev => [...prev, ...uploadedFileResults]);\n      onFilesUploaded === null || onFilesUploaded === void 0 ? void 0 : onFilesUploaded(uploadedFileResults);\n\n      // Clear uploading files after a delay\n      setTimeout(() => {\n        setUploadingFiles([]);\n      }, 2000);\n    } catch (error) {\n      // Update uploading files to error\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        status: 'error',\n        error: error.message || 'Upload failed'\n      })));\n      onError === null || onError === void 0 ? void 0 : onError(error.message || 'Failed to upload files');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteFile = async () => {\n    if (!fileToDelete || !orderId) return;\n    try {\n      await printingService.deleteFile(orderId, fileToDelete.id);\n      setUploadedFiles(prev => prev.filter(f => f.id !== fileToDelete.id));\n      setDeleteDialogOpen(false);\n      setFileToDelete(null);\n    } catch (error) {\n      onError === null || onError === void 0 ? void 0 : onError(error.message || 'Failed to delete file');\n    }\n  };\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: settings && Array.isArray(settings.allowed_file_types) ? {\n      'image/*': settings.allowed_file_types.filter(type => ['png', 'jpg', 'jpeg', 'tiff', 'svg'].includes(type)).map(type => `.${type}`),\n      'application/pdf': settings.allowed_file_types.includes('pdf') ? ['.pdf'] : [],\n      'application/postscript': settings.allowed_file_types.includes('eps') ? ['.eps'] : [],\n      'application/illustrator': settings.allowed_file_types.includes('ai') ? ['.ai'] : []\n    } : undefined,\n    maxSize: settings ? settings.max_file_size_mb * 1024 * 1024 : undefined,\n    disabled: loading || !orderId\n  });\n  const getFileIcon = mimeType => {\n    if (mimeType.startsWith('image/')) return /*#__PURE__*/_jsxDEV(ImageIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 47\n    }, this);\n    if (mimeType === 'application/pdf') return /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 48\n    }, this);\n    return /*#__PURE__*/_jsxDEV(FileIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 12\n    }, this);\n  };\n  const getStatusIcon = file => {\n    if (file.dpi && settings !== null && settings !== void 0 && settings.enable_dpi_validation) {\n      if (file.dpi >= settings.min_dpi_requirement) {\n        return /*#__PURE__*/_jsxDEV(CheckIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 16\n        }, this);\n      } else if (file.dpi >= settings.dpi_warning_threshold) {\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 16\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(ErrorIcon, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 16\n        }, this);\n      }\n    }\n    return file.is_approved ? /*#__PURE__*/_jsxDEV(CheckIcon, {\n      color: \"success\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 31\n    }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {\n      color: \"warning\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 63\n    }, this);\n  };\n  const formatFileSize = bytes => {\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let size = bytes;\n    let unitIndex = 0;\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  };\n  if (!settings) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"200px\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Loading upload settings...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Upload Limits:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), \" Max \", settings.max_files_per_order, \" files,\", settings.max_file_size_mb, \"MB per file, \", settings.max_total_upload_size_mb, \"MB total. Allowed types: \", settings.allowed_file_types.join(', ').toUpperCase()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      ...getRootProps(),\n      sx: {\n        p: 4,\n        border: '2px dashed',\n        borderColor: isDragActive ? 'primary.main' : 'grey.300',\n        backgroundColor: isDragActive ? 'action.hover' : 'background.paper',\n        cursor: orderId ? 'pointer' : 'not-allowed',\n        opacity: orderId ? 1 : 0.5,\n        textAlign: 'center',\n        mb: 3,\n        transition: 'all 0.2s ease-in-out'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        ...getInputProps()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UploadIcon, {\n        sx: {\n          fontSize: 48,\n          color: 'text.secondary',\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: isDragActive ? 'Drop files here' : 'Drag & drop files here'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: \"or click to browse files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        disabled: !orderId,\n        children: \"Browse Files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), !orderId && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"error\",\n        sx: {\n          display: 'block',\n          mt: 1\n        },\n        children: \"Please create an order first\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this), uploadingFiles.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Uploading Files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 11\n      }, this), uploadingFiles.map((uploadingFile, index) => /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 2,\n            children: [getFileIcon(uploadingFile.file.type), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                noWrap: true,\n                children: uploadingFile.file.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: formatFileSize(uploadingFile.file.size)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: 100\n              },\n              children: [uploadingFile.status === 'uploading' && /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"indeterminate\",\n                sx: {\n                  height: 6,\n                  borderRadius: 3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 23\n              }, this), uploadingFile.status === 'success' && /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 31\n                }, this),\n                label: \"Success\",\n                color: \"success\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 23\n              }, this), uploadingFile.status === 'error' && /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(ErrorIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 31\n                }, this),\n                label: \"Error\",\n                color: \"error\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 17\n          }, this), uploadingFile.error && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mt: 1\n            },\n            children: uploadingFile.error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 19\n          }, this), uploadingFile.warnings && uploadingFile.warnings.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mt: 1\n            },\n            children: uploadingFile.warnings.join(', ')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 9\n    }, this), uploadedFiles.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: [\"Uploaded Files (\", uploadedFiles.length, \"/\", settings.max_files_per_order, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n          gap: 2\n        },\n        children: uploadedFiles.map(file => /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 1,\n              mb: 1,\n              children: [getFileIcon(file.mime_type), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: getStatusIcon(file),\n                children: getStatusIcon(file)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                noWrap: true,\n                sx: {\n                  flex: 1\n                },\n                children: file.original_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              display: \"block\",\n              children: [\"Size: \", file.formatted_file_size]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              display: \"block\",\n              children: [\"Type: \", file.file_type_label]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 19\n            }, this), file.dimensions && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              display: \"block\",\n              children: [\"Dimensions: \", file.dimensions.width, \"\\xD7\", file.dimensions.height, \"px\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 21\n            }, this), file.dpi && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: file.dpi >= (settings.min_dpi_requirement || 300) ? 'success.main' : file.dpi >= (settings.dpi_warning_threshold || 150) ? 'warning.main' : 'error.main',\n              display: \"block\",\n              children: [\"DPI: \", file.dpi, \" \", file.dpi_status && `(${file.dpi_status})`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 21\n            }, this), file.notes && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              display: \"block\",\n              sx: {\n                mt: 1\n              },\n              children: [\"Notes: \", file.notes]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              gap: 1,\n              mt: 1,\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: file.is_approved ? 'Approved' : 'Pending Review',\n                color: file.is_approved ? 'success' : 'warning',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              href: file.file_url,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              children: \"View\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              color: \"error\",\n              onClick: () => {\n                setFileToDelete(file);\n                setDeleteDialogOpen(true);\n              },\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 17\n          }, this)]\n        }, file.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: () => setDeleteDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Delete File\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Are you sure you want to delete \\\"\", fileToDelete === null || fileToDelete === void 0 ? void 0 : fileToDelete.original_name, \"\\\"? This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDeleteDialogOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteFile,\n          color: \"error\",\n          variant: \"contained\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 307,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUpload, \"fyVuToa6R6cfjBlpHqCNkvF1j3Y=\", false, function () {\n  return [useDropzone];\n});\n_c = FileUpload;\nexport default FileUpload;\nvar _c;\n$RefreshReg$(_c, \"FileUpload\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Typography", "Paper", "<PERSON><PERSON>", "LinearProgress", "<PERSON><PERSON>", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON><PERSON>", "CloudUpload", "UploadIcon", "Delete", "DeleteIcon", "Warning", "WarningIcon", "CheckCircle", "CheckIcon", "Error", "ErrorIcon", "Image", "ImageIcon", "PictureAsPdf", "PdfIcon", "Description", "FileIcon", "useDropzone", "printingService", "jsxDEV", "_jsxDEV", "FileUpload", "orderId", "onFilesUploaded", "onError", "_s", "settings", "setSettings", "uploadedFiles", "setUploadedFiles", "uploadingFiles", "setUploadingFiles", "loading", "setLoading", "deleteDialogOpen", "setDeleteDialogOpen", "fileToDelete", "setFileToDelete", "loadSettings", "loadUploadedFiles", "uploadSettings", "getUploadSettings", "Array", "isArray", "allowed_file_types", "error", "console", "max_file_size_mb", "max_total_upload_size_mb", "max_files_per_order", "min_dpi_requirement", "dpi_warning_threshold", "enable_dpi_validation", "min_width_px", "min_height_px", "max_width_px", "max_height_px", "enable_dimension_validation", "files", "getOrderFiles", "validateFiles", "valid", "invalid", "totalFiles", "length", "excess", "i", "push", "file", "errors", "slice", "currentTotalSize", "reduce", "sum", "file_size", "newFilesSize", "size", "totalSize", "maxTotalSize", "map", "validation", "validateFile", "message", "onDrop", "acceptedFiles", "errorMessages", "name", "join", "uploadFiles", "newUploadingFiles", "progress", "status", "uploadedFileResults", "prev", "uf", "setTimeout", "handleDeleteFile", "deleteFile", "id", "filter", "f", "getRootProps", "getInputProps", "isDragActive", "accept", "type", "includes", "undefined", "maxSize", "disabled", "getFileIcon", "mimeType", "startsWith", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusIcon", "dpi", "color", "is_approved", "formatFileSize", "bytes", "units", "unitIndex", "toFixed", "display", "justifyContent", "alignItems", "minHeight", "children", "severity", "sx", "mb", "variant", "toUpperCase", "p", "border", "borderColor", "backgroundColor", "cursor", "opacity", "textAlign", "transition", "fontSize", "gutterBottom", "mt", "uploadingFile", "index", "py", "gap", "flex", "noWrap", "width", "height", "borderRadius", "icon", "label", "warnings", "gridTemplateColumns", "mime_type", "title", "original_name", "formatted_file_size", "file_type_label", "dimensions", "dpi_status", "notes", "href", "file_url", "target", "rel", "onClick", "open", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/FileUpload.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  LinearProgress,\n  Alert,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n\n  Card,\n  CardContent,\n  CardActions,\n  Tooltip,\n} from '@mui/material';\nimport {\n  CloudUpload as UploadIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckIcon,\n  Error as ErrorIcon,\n  Image as ImageIcon,\n  PictureAsPdf as PdfIcon,\n  Description as FileIcon,\n} from '@mui/icons-material';\nimport { useDropzone } from 'react-dropzone';\nimport printingService, { OrderFile } from '../../services/printingService';\n\ninterface FileUploadSettings {\n  allowed_file_types: string[];\n  max_file_size_mb: number;\n  max_total_upload_size_mb: number;\n  max_files_per_order: number;\n  min_dpi_requirement: number;\n  dpi_warning_threshold: number;\n  enable_dpi_validation: boolean;\n  min_width_px: number;\n  min_height_px: number;\n  max_width_px: number;\n  max_height_px: number;\n  enable_dimension_validation: boolean;\n}\n\ninterface FileUploadProps {\n  orderId?: number;\n  onFilesUploaded?: (files: OrderFile[]) => void;\n  onError?: (error: string) => void;\n}\n\ninterface UploadingFile {\n  file: File;\n  progress: number;\n  status: 'uploading' | 'success' | 'error';\n  error?: string;\n  warnings?: string[];\n}\n\nconst FileUpload: React.FC<FileUploadProps> = ({ orderId, onFilesUploaded, onError }) => {\n  const [settings, setSettings] = useState<FileUploadSettings | null>(null);\n  const [uploadedFiles, setUploadedFiles] = useState<OrderFile[]>([]);\n  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [fileToDelete, setFileToDelete] = useState<OrderFile | null>(null);\n\n  // Load settings and files on component mount\n  useEffect(() => {\n    loadSettings();\n    if (orderId) {\n      loadUploadedFiles();\n    }\n  }, [orderId]);\n\n  const loadSettings = async () => {\n    try {\n      const uploadSettings = await printingService.getUploadSettings();\n\n      // Ensure allowed_file_types is always an array\n      if (uploadSettings && !Array.isArray(uploadSettings.allowed_file_types)) {\n        uploadSettings.allowed_file_types = ['pdf', 'png', 'jpg', 'jpeg'];\n      }\n\n      setSettings(uploadSettings);\n    } catch (error) {\n      console.error('Failed to load upload settings:', error);\n      onError?.('Failed to load upload settings');\n\n      // Set default settings if loading fails\n      setSettings({\n        allowed_file_types: ['pdf', 'png', 'jpg', 'jpeg'],\n        max_file_size_mb: 50,\n        max_total_upload_size_mb: 200,\n        max_files_per_order: 10,\n        min_dpi_requirement: 300,\n        dpi_warning_threshold: 150,\n        enable_dpi_validation: true,\n        min_width_px: 100,\n        min_height_px: 100,\n        max_width_px: 10000,\n        max_height_px: 10000,\n        enable_dimension_validation: true,\n      });\n    }\n  };\n\n  const loadUploadedFiles = async () => {\n    if (!orderId) return;\n\n    try {\n      const files = await printingService.getOrderFiles(orderId);\n      setUploadedFiles(files);\n    } catch (error) {\n      console.error('Failed to load uploaded files:', error);\n    }\n  };\n\n  // Validate files before upload\n  const validateFiles = async (files: File[]): Promise<{ valid: File[]; invalid: { file: File; errors: string[] }[] }> => {\n    if (!settings) return { valid: [], invalid: [] };\n\n    const valid: File[] = [];\n    const invalid: { file: File; errors: string[] }[] = [];\n\n    // Check total file count\n    const totalFiles = uploadedFiles.length + files.length;\n    if (totalFiles > settings.max_files_per_order) {\n      const excess = totalFiles - settings.max_files_per_order;\n      for (let i = files.length - excess; i < files.length; i++) {\n        invalid.push({\n          file: files[i],\n          errors: [`Maximum ${settings.max_files_per_order} files allowed per order`]\n        });\n      }\n      files = files.slice(0, files.length - excess);\n    }\n\n    // Check total upload size\n    const currentTotalSize = uploadedFiles.reduce((sum, file) => sum + file.file_size, 0);\n    const newFilesSize = files.reduce((sum, file) => sum + file.size, 0);\n    const totalSize = currentTotalSize + newFilesSize;\n    const maxTotalSize = settings.max_total_upload_size_mb * 1024 * 1024;\n\n    if (totalSize > maxTotalSize) {\n      onError?.(`Total upload size would exceed ${settings.max_total_upload_size_mb}MB limit`);\n      return { valid: [], invalid: files.map(file => ({ file, errors: ['Total size limit exceeded'] })) };\n    }\n\n    // Validate each file\n    for (const file of files) {\n      const validation = await printingService.validateFile(file);\n      if (validation.valid) {\n        valid.push(file);\n      } else {\n        invalid.push({\n          file,\n          errors: validation.message ? [validation.message] : ['Validation failed']\n        });\n      }\n    }\n\n    return { valid, invalid };\n  };\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    if (!orderId) {\n      onError?.('Please create an order first before uploading files');\n      return;\n    }\n\n    const { valid, invalid } = await validateFiles(acceptedFiles);\n\n    // Show errors for invalid files\n    if (invalid.length > 0) {\n      const errorMessages = invalid.map(({ file, errors }) =>\n        `${file.name}: ${errors.join(', ')}`\n      ).join('\\n');\n      onError?.(errorMessages);\n    }\n\n    // Upload valid files\n    if (valid.length > 0) {\n      await uploadFiles(valid);\n    }\n  }, [orderId, settings, uploadedFiles]);\n\n  const uploadFiles = async (files: File[]) => {\n    setLoading(true);\n\n    // Initialize uploading files state\n    const newUploadingFiles: UploadingFile[] = files.map(file => ({\n      file,\n      progress: 0,\n      status: 'uploading'\n    }));\n    setUploadingFiles(newUploadingFiles);\n\n    try {\n      const uploadedFileResults = await printingService.uploadFiles(orderId!, files, 'artwork');\n\n      // Update uploading files to success\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        progress: 100,\n        status: 'success'\n      })));\n\n      // Add to uploaded files list\n      setUploadedFiles(prev => [...prev, ...uploadedFileResults]);\n      onFilesUploaded?.(uploadedFileResults);\n\n      // Clear uploading files after a delay\n      setTimeout(() => {\n        setUploadingFiles([]);\n      }, 2000);\n\n    } catch (error: any) {\n      // Update uploading files to error\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        status: 'error',\n        error: error.message || 'Upload failed'\n      })));\n\n      onError?.(error.message || 'Failed to upload files');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteFile = async () => {\n    if (!fileToDelete || !orderId) return;\n\n    try {\n      await printingService.deleteFile(orderId, fileToDelete.id);\n      setUploadedFiles(prev => prev.filter(f => f.id !== fileToDelete.id));\n      setDeleteDialogOpen(false);\n      setFileToDelete(null);\n    } catch (error: any) {\n      onError?.(error.message || 'Failed to delete file');\n    }\n  };\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: settings && Array.isArray(settings.allowed_file_types) ? {\n      'image/*': settings.allowed_file_types.filter(type =>\n        ['png', 'jpg', 'jpeg', 'tiff', 'svg'].includes(type)\n      ).map(type => `.${type}`),\n      'application/pdf': settings.allowed_file_types.includes('pdf') ? ['.pdf'] : [],\n      'application/postscript': settings.allowed_file_types.includes('eps') ? ['.eps'] : [],\n      'application/illustrator': settings.allowed_file_types.includes('ai') ? ['.ai'] : [],\n    } : undefined,\n    maxSize: settings ? settings.max_file_size_mb * 1024 * 1024 : undefined,\n    disabled: loading || !orderId\n  });\n\n  const getFileIcon = (mimeType: string) => {\n    if (mimeType.startsWith('image/')) return <ImageIcon />;\n    if (mimeType === 'application/pdf') return <PdfIcon />;\n    return <FileIcon />;\n  };\n\n  const getStatusIcon = (file: OrderFile) => {\n    if (file.dpi && settings?.enable_dpi_validation) {\n      if (file.dpi >= settings.min_dpi_requirement) {\n        return <CheckIcon color=\"success\" />;\n      } else if (file.dpi >= settings.dpi_warning_threshold) {\n        return <WarningIcon color=\"warning\" />;\n      } else {\n        return <ErrorIcon color=\"error\" />;\n      }\n    }\n    return file.is_approved ? <CheckIcon color=\"success\" /> : <WarningIcon color=\"warning\" />;\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let size = bytes;\n    let unitIndex = 0;\n\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  };\n\n  if (!settings) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"200px\">\n        <Typography>Loading upload settings...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Upload Settings Info */}\n      <Alert severity=\"info\" sx={{ mb: 2 }}>\n        <Typography variant=\"body2\">\n          <strong>Upload Limits:</strong> Max {settings.max_files_per_order} files,\n          {settings.max_file_size_mb}MB per file, {settings.max_total_upload_size_mb}MB total.\n          Allowed types: {settings.allowed_file_types.join(', ').toUpperCase()}\n        </Typography>\n      </Alert>\n\n\n\n      {/* Drag and Drop Upload Area */}\n      <Paper\n        {...getRootProps()}\n        sx={{\n          p: 4,\n          border: '2px dashed',\n          borderColor: isDragActive ? 'primary.main' : 'grey.300',\n          backgroundColor: isDragActive ? 'action.hover' : 'background.paper',\n          cursor: orderId ? 'pointer' : 'not-allowed',\n          opacity: orderId ? 1 : 0.5,\n          textAlign: 'center',\n          mb: 3,\n          transition: 'all 0.2s ease-in-out',\n        }}\n      >\n        <input {...getInputProps()} />\n        <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />\n        <Typography variant=\"h6\" gutterBottom>\n          {isDragActive ? 'Drop files here' : 'Drag & drop files here'}\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          or click to browse files\n        </Typography>\n        <Button variant=\"outlined\" disabled={!orderId}>\n          Browse Files\n        </Button>\n        {!orderId && (\n          <Typography variant=\"caption\" color=\"error\" sx={{ display: 'block', mt: 1 }}>\n            Please create an order first\n          </Typography>\n        )}\n      </Paper>\n\n      {/* Uploading Files */}\n      {uploadingFiles.length > 0 && (\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Uploading Files\n          </Typography>\n          {uploadingFiles.map((uploadingFile, index) => (\n            <Card key={index} sx={{ mb: 1 }}>\n              <CardContent sx={{ py: 2 }}>\n                <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                  {getFileIcon(uploadingFile.file.type)}\n                  <Box sx={{ flex: 1 }}>\n                    <Typography variant=\"body2\" noWrap>\n                      {uploadingFile.file.name}\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      {formatFileSize(uploadingFile.file.size)}\n                    </Typography>\n                  </Box>\n                  <Box sx={{ width: 100 }}>\n                    {uploadingFile.status === 'uploading' && (\n                      <LinearProgress\n                        variant=\"indeterminate\"\n                        sx={{ height: 6, borderRadius: 3 }}\n                      />\n                    )}\n                    {uploadingFile.status === 'success' && (\n                      <Chip\n                        icon={<CheckIcon />}\n                        label=\"Success\"\n                        color=\"success\"\n                        size=\"small\"\n                      />\n                    )}\n                    {uploadingFile.status === 'error' && (\n                      <Chip\n                        icon={<ErrorIcon />}\n                        label=\"Error\"\n                        color=\"error\"\n                        size=\"small\"\n                      />\n                    )}\n                  </Box>\n                </Box>\n                {uploadingFile.error && (\n                  <Alert severity=\"error\" sx={{ mt: 1 }}>\n                    {uploadingFile.error}\n                  </Alert>\n                )}\n                {uploadingFile.warnings && uploadingFile.warnings.length > 0 && (\n                  <Alert severity=\"warning\" sx={{ mt: 1 }}>\n                    {uploadingFile.warnings.join(', ')}\n                  </Alert>\n                )}\n              </CardContent>\n            </Card>\n          ))}\n        </Box>\n      )}\n\n      {/* Uploaded Files List */}\n      {uploadedFiles.length > 0 && (\n        <Box>\n          <Typography variant=\"h6\" gutterBottom>\n            Uploaded Files ({uploadedFiles.length}/{settings.max_files_per_order})\n          </Typography>\n          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: 2 }}>\n            {uploadedFiles.map((file) => (\n              <Card key={file.id}>\n                <CardContent>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1} mb={1}>\n                    {getFileIcon(file.mime_type)}\n                    <Tooltip title={getStatusIcon(file)}>\n                      {getStatusIcon(file)}\n                    </Tooltip>\n                    <Typography variant=\"body2\" noWrap sx={{ flex: 1 }}>\n                      {file.original_name}\n                    </Typography>\n                  </Box>\n\n                  <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                    Size: {file.formatted_file_size}\n                  </Typography>\n\n                  <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                    Type: {file.file_type_label}\n                  </Typography>\n\n                  {file.dimensions && (\n                    <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                      Dimensions: {file.dimensions.width}×{file.dimensions.height}px\n                    </Typography>\n                  )}\n\n                  {file.dpi && (\n                    <Typography\n                      variant=\"caption\"\n                      color={\n                        file.dpi >= (settings.min_dpi_requirement || 300) ? 'success.main' :\n                        file.dpi >= (settings.dpi_warning_threshold || 150) ? 'warning.main' : 'error.main'\n                      }\n                      display=\"block\"\n                    >\n                      DPI: {file.dpi} {file.dpi_status && `(${file.dpi_status})`}\n                    </Typography>\n                  )}\n\n                  {file.notes && (\n                    <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" sx={{ mt: 1 }}>\n                      Notes: {file.notes}\n                    </Typography>\n                  )}\n\n                  <Box display=\"flex\" gap={1} mt={1}>\n                    <Chip\n                      label={file.is_approved ? 'Approved' : 'Pending Review'}\n                      color={file.is_approved ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  </Box>\n                </CardContent>\n\n                <CardActions>\n                  <Button\n                    size=\"small\"\n                    href={file.file_url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    View\n                  </Button>\n                  <IconButton\n                    size=\"small\"\n                    color=\"error\"\n                    onClick={() => {\n                      setFileToDelete(file);\n                      setDeleteDialogOpen(true);\n                    }}\n                  >\n                    <DeleteIcon />\n                  </IconButton>\n                </CardActions>\n              </Card>\n            ))}\n          </Box>\n        </Box>\n      )}\n\n      {/* Delete Confirmation Dialog */}\n      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>\n        <DialogTitle>Delete File</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Are you sure you want to delete \"{fileToDelete?.original_name}\"? This action cannot be undone.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>\n          <Button onClick={handleDeleteFile} color=\"error\" variant=\"contained\">\n            Delete\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default FileUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,cAAc,EACdC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EAMbC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,OAAO,QACF,eAAe;AACtB,SACEC,WAAW,IAAIC,UAAU,EACzBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,SAAS,EACxBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,YAAY,IAAIC,OAAO,EACvBC,WAAW,IAAIC,QAAQ,QAClB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,eAAe,MAAqB,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA+B5E,MAAMC,UAAqC,GAAGA,CAAC;EAAEC,OAAO;EAAEC,eAAe;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAA4B,IAAI,CAAC;EACzE,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAc,EAAE,CAAC;EACnE,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsD,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAmB,IAAI,CAAC;;EAExE;EACAC,SAAS,CAAC,MAAM;IACduD,YAAY,CAAC,CAAC;IACd,IAAIhB,OAAO,EAAE;MACXiB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACjB,OAAO,CAAC,CAAC;EAEb,MAAMgB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAME,cAAc,GAAG,MAAMtB,eAAe,CAACuB,iBAAiB,CAAC,CAAC;;MAEhE;MACA,IAAID,cAAc,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,cAAc,CAACI,kBAAkB,CAAC,EAAE;QACvEJ,cAAc,CAACI,kBAAkB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;MACnE;MAEAjB,WAAW,CAACa,cAAc,CAAC;IAC7B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDrB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,gCAAgC,CAAC;;MAE3C;MACAG,WAAW,CAAC;QACViB,kBAAkB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;QACjDG,gBAAgB,EAAE,EAAE;QACpBC,wBAAwB,EAAE,GAAG;QAC7BC,mBAAmB,EAAE,EAAE;QACvBC,mBAAmB,EAAE,GAAG;QACxBC,qBAAqB,EAAE,GAAG;QAC1BC,qBAAqB,EAAE,IAAI;QAC3BC,YAAY,EAAE,GAAG;QACjBC,aAAa,EAAE,GAAG;QAClBC,YAAY,EAAE,KAAK;QACnBC,aAAa,EAAE,KAAK;QACpBC,2BAA2B,EAAE;MAC/B,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMlB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACjB,OAAO,EAAE;IAEd,IAAI;MACF,MAAMoC,KAAK,GAAG,MAAMxC,eAAe,CAACyC,aAAa,CAACrC,OAAO,CAAC;MAC1DO,gBAAgB,CAAC6B,KAAK,CAAC;IACzB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMe,aAAa,GAAG,MAAOF,KAAa,IAA8E;IACtH,IAAI,CAAChC,QAAQ,EAAE,OAAO;MAAEmC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC;IAEhD,MAAMD,KAAa,GAAG,EAAE;IACxB,MAAMC,OAA2C,GAAG,EAAE;;IAEtD;IACA,MAAMC,UAAU,GAAGnC,aAAa,CAACoC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACtD,IAAID,UAAU,GAAGrC,QAAQ,CAACuB,mBAAmB,EAAE;MAC7C,MAAMgB,MAAM,GAAGF,UAAU,GAAGrC,QAAQ,CAACuB,mBAAmB;MACxD,KAAK,IAAIiB,CAAC,GAAGR,KAAK,CAACM,MAAM,GAAGC,MAAM,EAAEC,CAAC,GAAGR,KAAK,CAACM,MAAM,EAAEE,CAAC,EAAE,EAAE;QACzDJ,OAAO,CAACK,IAAI,CAAC;UACXC,IAAI,EAAEV,KAAK,CAACQ,CAAC,CAAC;UACdG,MAAM,EAAE,CAAC,WAAW3C,QAAQ,CAACuB,mBAAmB,0BAA0B;QAC5E,CAAC,CAAC;MACJ;MACAS,KAAK,GAAGA,KAAK,CAACY,KAAK,CAAC,CAAC,EAAEZ,KAAK,CAACM,MAAM,GAAGC,MAAM,CAAC;IAC/C;;IAEA;IACA,MAAMM,gBAAgB,GAAG3C,aAAa,CAAC4C,MAAM,CAAC,CAACC,GAAG,EAAEL,IAAI,KAAKK,GAAG,GAAGL,IAAI,CAACM,SAAS,EAAE,CAAC,CAAC;IACrF,MAAMC,YAAY,GAAGjB,KAAK,CAACc,MAAM,CAAC,CAACC,GAAG,EAAEL,IAAI,KAAKK,GAAG,GAAGL,IAAI,CAACQ,IAAI,EAAE,CAAC,CAAC;IACpE,MAAMC,SAAS,GAAGN,gBAAgB,GAAGI,YAAY;IACjD,MAAMG,YAAY,GAAGpD,QAAQ,CAACsB,wBAAwB,GAAG,IAAI,GAAG,IAAI;IAEpE,IAAI6B,SAAS,GAAGC,YAAY,EAAE;MAC5BtD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,kCAAkCE,QAAQ,CAACsB,wBAAwB,UAAU,CAAC;MACxF,OAAO;QAAEa,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAEJ,KAAK,CAACqB,GAAG,CAACX,IAAI,KAAK;UAAEA,IAAI;UAAEC,MAAM,EAAE,CAAC,2BAA2B;QAAE,CAAC,CAAC;MAAE,CAAC;IACrG;;IAEA;IACA,KAAK,MAAMD,IAAI,IAAIV,KAAK,EAAE;MACxB,MAAMsB,UAAU,GAAG,MAAM9D,eAAe,CAAC+D,YAAY,CAACb,IAAI,CAAC;MAC3D,IAAIY,UAAU,CAACnB,KAAK,EAAE;QACpBA,KAAK,CAACM,IAAI,CAACC,IAAI,CAAC;MAClB,CAAC,MAAM;QACLN,OAAO,CAACK,IAAI,CAAC;UACXC,IAAI;UACJC,MAAM,EAAEW,UAAU,CAACE,OAAO,GAAG,CAACF,UAAU,CAACE,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAC1E,CAAC,CAAC;MACJ;IACF;IAEA,OAAO;MAAErB,KAAK;MAAEC;IAAQ,CAAC;EAC3B,CAAC;EAED,MAAMqB,MAAM,GAAGnG,WAAW,CAAC,MAAOoG,aAAqB,IAAK;IAC1D,IAAI,CAAC9D,OAAO,EAAE;MACZE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,qDAAqD,CAAC;MAChE;IACF;IAEA,MAAM;MAAEqC,KAAK;MAAEC;IAAQ,CAAC,GAAG,MAAMF,aAAa,CAACwB,aAAa,CAAC;;IAE7D;IACA,IAAItB,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMqB,aAAa,GAAGvB,OAAO,CAACiB,GAAG,CAAC,CAAC;QAAEX,IAAI;QAAEC;MAAO,CAAC,KACjD,GAAGD,IAAI,CAACkB,IAAI,KAAKjB,MAAM,CAACkB,IAAI,CAAC,IAAI,CAAC,EACpC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;MACZ/D,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG6D,aAAa,CAAC;IAC1B;;IAEA;IACA,IAAIxB,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MACpB,MAAMwB,WAAW,CAAC3B,KAAK,CAAC;IAC1B;EACF,CAAC,EAAE,CAACvC,OAAO,EAAEI,QAAQ,EAAEE,aAAa,CAAC,CAAC;EAEtC,MAAM4D,WAAW,GAAG,MAAO9B,KAAa,IAAK;IAC3CzB,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACA,MAAMwD,iBAAkC,GAAG/B,KAAK,CAACqB,GAAG,CAACX,IAAI,KAAK;MAC5DA,IAAI;MACJsB,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IACH5D,iBAAiB,CAAC0D,iBAAiB,CAAC;IAEpC,IAAI;MACF,MAAMG,mBAAmB,GAAG,MAAM1E,eAAe,CAACsE,WAAW,CAAClE,OAAO,EAAGoC,KAAK,EAAE,SAAS,CAAC;;MAEzF;MACA3B,iBAAiB,CAAC8D,IAAI,IAAIA,IAAI,CAACd,GAAG,CAACe,EAAE,KAAK;QACxC,GAAGA,EAAE;QACLJ,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC,CAAC;;MAEJ;MACA9D,gBAAgB,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGD,mBAAmB,CAAC,CAAC;MAC3DrE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGqE,mBAAmB,CAAC;;MAEtC;MACAG,UAAU,CAAC,MAAM;QACfhE,iBAAiB,CAAC,EAAE,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOc,KAAU,EAAE;MACnB;MACAd,iBAAiB,CAAC8D,IAAI,IAAIA,IAAI,CAACd,GAAG,CAACe,EAAE,KAAK;QACxC,GAAGA,EAAE;QACLH,MAAM,EAAE,OAAO;QACf9C,KAAK,EAAEA,KAAK,CAACqC,OAAO,IAAI;MAC1B,CAAC,CAAC,CAAC,CAAC;MAEJ1D,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGqB,KAAK,CAACqC,OAAO,IAAI,wBAAwB,CAAC;IACtD,CAAC,SAAS;MACRjD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC5D,YAAY,IAAI,CAACd,OAAO,EAAE;IAE/B,IAAI;MACF,MAAMJ,eAAe,CAAC+E,UAAU,CAAC3E,OAAO,EAAEc,YAAY,CAAC8D,EAAE,CAAC;MAC1DrE,gBAAgB,CAACgE,IAAI,IAAIA,IAAI,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAK9D,YAAY,CAAC8D,EAAE,CAAC,CAAC;MACpE/D,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOQ,KAAU,EAAE;MACnBrB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGqB,KAAK,CAACqC,OAAO,IAAI,uBAAuB,CAAC;IACrD;EACF,CAAC;EAED,MAAM;IAAEmB,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGtF,WAAW,CAAC;IAChEkE,MAAM;IACNqB,MAAM,EAAE9E,QAAQ,IAAIgB,KAAK,CAACC,OAAO,CAACjB,QAAQ,CAACkB,kBAAkB,CAAC,GAAG;MAC/D,SAAS,EAAElB,QAAQ,CAACkB,kBAAkB,CAACuD,MAAM,CAACM,IAAI,IAChD,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,IAAI,CACrD,CAAC,CAAC1B,GAAG,CAAC0B,IAAI,IAAI,IAAIA,IAAI,EAAE,CAAC;MACzB,iBAAiB,EAAE/E,QAAQ,CAACkB,kBAAkB,CAAC8D,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE;MAC9E,wBAAwB,EAAEhF,QAAQ,CAACkB,kBAAkB,CAAC8D,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE;MACrF,yBAAyB,EAAEhF,QAAQ,CAACkB,kBAAkB,CAAC8D,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG;IACpF,CAAC,GAAGC,SAAS;IACbC,OAAO,EAAElF,QAAQ,GAAGA,QAAQ,CAACqB,gBAAgB,GAAG,IAAI,GAAG,IAAI,GAAG4D,SAAS;IACvEE,QAAQ,EAAE7E,OAAO,IAAI,CAACV;EACxB,CAAC,CAAC;EAEF,MAAMwF,WAAW,GAAIC,QAAgB,IAAK;IACxC,IAAIA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE,oBAAO5F,OAAA,CAACR,SAAS;MAAAqG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvD,IAAIL,QAAQ,KAAK,iBAAiB,EAAE,oBAAO3F,OAAA,CAACN,OAAO;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtD,oBAAOhG,OAAA,CAACJ,QAAQ;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrB,CAAC;EAED,MAAMC,aAAa,GAAIjD,IAAe,IAAK;IACzC,IAAIA,IAAI,CAACkD,GAAG,IAAI5F,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAE0B,qBAAqB,EAAE;MAC/C,IAAIgB,IAAI,CAACkD,GAAG,IAAI5F,QAAQ,CAACwB,mBAAmB,EAAE;QAC5C,oBAAO9B,OAAA,CAACZ,SAAS;UAAC+G,KAAK,EAAC;QAAS;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtC,CAAC,MAAM,IAAIhD,IAAI,CAACkD,GAAG,IAAI5F,QAAQ,CAACyB,qBAAqB,EAAE;QACrD,oBAAO/B,OAAA,CAACd,WAAW;UAACiH,KAAK,EAAC;QAAS;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxC,CAAC,MAAM;QACL,oBAAOhG,OAAA,CAACV,SAAS;UAAC6G,KAAK,EAAC;QAAO;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpC;IACF;IACA,OAAOhD,IAAI,CAACoD,WAAW,gBAAGpG,OAAA,CAACZ,SAAS;MAAC+G,KAAK,EAAC;IAAS;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGhG,OAAA,CAACd,WAAW;MAACiH,KAAK,EAAC;IAAS;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3F,CAAC;EAED,MAAMK,cAAc,GAAIC,KAAa,IAAa;IAChD,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,IAAI/C,IAAI,GAAG8C,KAAK;IAChB,IAAIE,SAAS,GAAG,CAAC;IAEjB,OAAOhD,IAAI,IAAI,IAAI,IAAIgD,SAAS,GAAGD,KAAK,CAAC3D,MAAM,GAAG,CAAC,EAAE;MACnDY,IAAI,IAAI,IAAI;MACZgD,SAAS,EAAE;IACb;IAEA,OAAO,GAAGhD,IAAI,CAACiD,OAAO,CAAC,CAAC,CAAC,IAAIF,KAAK,CAACC,SAAS,CAAC,EAAE;EACjD,CAAC;EAED,IAAI,CAAClG,QAAQ,EAAE;IACb,oBACEN,OAAA,CAACnC,GAAG;MAAC6I,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/E9G,OAAA,CAAClC,UAAU;QAAAgJ,QAAA,EAAC;MAA0B;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACEhG,OAAA,CAACnC,GAAG;IAAAiJ,QAAA,gBAEF9G,OAAA,CAAC9B,KAAK;MAAC6I,QAAQ,EAAC,MAAM;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eACnC9G,OAAA,CAAClC,UAAU;QAACoJ,OAAO,EAAC,OAAO;QAAAJ,QAAA,gBACzB9G,OAAA;UAAA8G,QAAA,EAAQ;QAAc;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,SAAK,EAAC1F,QAAQ,CAACuB,mBAAmB,EAAC,SAClE,EAACvB,QAAQ,CAACqB,gBAAgB,EAAC,eAAa,EAACrB,QAAQ,CAACsB,wBAAwB,EAAC,2BAC5D,EAACtB,QAAQ,CAACkB,kBAAkB,CAAC2C,IAAI,CAAC,IAAI,CAAC,CAACgD,WAAW,CAAC,CAAC;MAAA;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAKRhG,OAAA,CAACjC,KAAK;MAAA,GACAkH,YAAY,CAAC,CAAC;MAClB+B,EAAE,EAAE;QACFI,CAAC,EAAE,CAAC;QACJC,MAAM,EAAE,YAAY;QACpBC,WAAW,EAAEnC,YAAY,GAAG,cAAc,GAAG,UAAU;QACvDoC,eAAe,EAAEpC,YAAY,GAAG,cAAc,GAAG,kBAAkB;QACnEqC,MAAM,EAAEtH,OAAO,GAAG,SAAS,GAAG,aAAa;QAC3CuH,OAAO,EAAEvH,OAAO,GAAG,CAAC,GAAG,GAAG;QAC1BwH,SAAS,EAAE,QAAQ;QACnBT,EAAE,EAAE,CAAC;QACLU,UAAU,EAAE;MACd,CAAE;MAAAb,QAAA,gBAEF9G,OAAA;QAAA,GAAWkF,aAAa,CAAC;MAAC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC9BhG,OAAA,CAAClB,UAAU;QAACkI,EAAE,EAAE;UAAEY,QAAQ,EAAE,EAAE;UAAEzB,KAAK,EAAE,gBAAgB;UAAEc,EAAE,EAAE;QAAE;MAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpEhG,OAAA,CAAClC,UAAU;QAACoJ,OAAO,EAAC,IAAI;QAACW,YAAY;QAAAf,QAAA,EAClC3B,YAAY,GAAG,iBAAiB,GAAG;MAAwB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACbhG,OAAA,CAAClC,UAAU;QAACoJ,OAAO,EAAC,OAAO;QAACf,KAAK,EAAC,gBAAgB;QAACa,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAElE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbhG,OAAA,CAAChC,MAAM;QAACkJ,OAAO,EAAC,UAAU;QAACzB,QAAQ,EAAE,CAACvF,OAAQ;QAAA4G,QAAA,EAAC;MAE/C;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR,CAAC9F,OAAO,iBACPF,OAAA,CAAClC,UAAU;QAACoJ,OAAO,EAAC,SAAS;QAACf,KAAK,EAAC,OAAO;QAACa,EAAE,EAAE;UAAEN,OAAO,EAAE,OAAO;UAAEoB,EAAE,EAAE;QAAE,CAAE;QAAAhB,QAAA,EAAC;MAE7E;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGPtF,cAAc,CAACkC,MAAM,GAAG,CAAC,iBACxB5C,OAAA,CAACnC,GAAG;MAACmJ,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACjB9G,OAAA,CAAClC,UAAU;QAACoJ,OAAO,EAAC,IAAI;QAACW,YAAY;QAAAf,QAAA,EAAC;MAEtC;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZtF,cAAc,CAACiD,GAAG,CAAC,CAACoE,aAAa,EAAEC,KAAK,kBACvChI,OAAA,CAACvB,IAAI;QAAauI,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,eAC9B9G,OAAA,CAACtB,WAAW;UAACsI,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBACzB9G,OAAA,CAACnC,GAAG;YAAC6I,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAACsB,GAAG,EAAE,CAAE;YAAApB,QAAA,GAC5CpB,WAAW,CAACqC,aAAa,CAAC/E,IAAI,CAACqC,IAAI,CAAC,eACrCrF,OAAA,CAACnC,GAAG;cAACmJ,EAAE,EAAE;gBAAEmB,IAAI,EAAE;cAAE,CAAE;cAAArB,QAAA,gBACnB9G,OAAA,CAAClC,UAAU;gBAACoJ,OAAO,EAAC,OAAO;gBAACkB,MAAM;gBAAAtB,QAAA,EAC/BiB,aAAa,CAAC/E,IAAI,CAACkB;cAAI;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACbhG,OAAA,CAAClC,UAAU;gBAACoJ,OAAO,EAAC,SAAS;gBAACf,KAAK,EAAC,gBAAgB;gBAAAW,QAAA,EACjDT,cAAc,CAAC0B,aAAa,CAAC/E,IAAI,CAACQ,IAAI;cAAC;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNhG,OAAA,CAACnC,GAAG;cAACmJ,EAAE,EAAE;gBAAEqB,KAAK,EAAE;cAAI,CAAE;cAAAvB,QAAA,GACrBiB,aAAa,CAACxD,MAAM,KAAK,WAAW,iBACnCvE,OAAA,CAAC/B,cAAc;gBACbiJ,OAAO,EAAC,eAAe;gBACvBF,EAAE,EAAE;kBAAEsB,MAAM,EAAE,CAAC;kBAAEC,YAAY,EAAE;gBAAE;cAAE;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CACF,EACA+B,aAAa,CAACxD,MAAM,KAAK,SAAS,iBACjCvE,OAAA,CAAC7B,IAAI;gBACHqK,IAAI,eAAExI,OAAA,CAACZ,SAAS;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpByC,KAAK,EAAC,SAAS;gBACftC,KAAK,EAAC,SAAS;gBACf3C,IAAI,EAAC;cAAO;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACF,EACA+B,aAAa,CAACxD,MAAM,KAAK,OAAO,iBAC/BvE,OAAA,CAAC7B,IAAI;gBACHqK,IAAI,eAAExI,OAAA,CAACV,SAAS;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpByC,KAAK,EAAC,OAAO;gBACbtC,KAAK,EAAC,OAAO;gBACb3C,IAAI,EAAC;cAAO;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACL+B,aAAa,CAACtG,KAAK,iBAClBzB,OAAA,CAAC9B,KAAK;YAAC6I,QAAQ,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEc,EAAE,EAAE;YAAE,CAAE;YAAAhB,QAAA,EACnCiB,aAAa,CAACtG;UAAK;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACR,EACA+B,aAAa,CAACW,QAAQ,IAAIX,aAAa,CAACW,QAAQ,CAAC9F,MAAM,GAAG,CAAC,iBAC1D5C,OAAA,CAAC9B,KAAK;YAAC6I,QAAQ,EAAC,SAAS;YAACC,EAAE,EAAE;cAAEc,EAAE,EAAE;YAAE,CAAE;YAAAhB,QAAA,EACrCiB,aAAa,CAACW,QAAQ,CAACvE,IAAI,CAAC,IAAI;UAAC;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC,GA/CLgC,KAAK;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgDV,CACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAxF,aAAa,CAACoC,MAAM,GAAG,CAAC,iBACvB5C,OAAA,CAACnC,GAAG;MAAAiJ,QAAA,gBACF9G,OAAA,CAAClC,UAAU;QAACoJ,OAAO,EAAC,IAAI;QAACW,YAAY;QAAAf,QAAA,GAAC,kBACpB,EAACtG,aAAa,CAACoC,MAAM,EAAC,GAAC,EAACtC,QAAQ,CAACuB,mBAAmB,EAAC,GACvE;MAAA;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbhG,OAAA,CAACnC,GAAG;QAACmJ,EAAE,EAAE;UAAEN,OAAO,EAAE,MAAM;UAAEiC,mBAAmB,EAAE,uCAAuC;UAAET,GAAG,EAAE;QAAE,CAAE;QAAApB,QAAA,EAChGtG,aAAa,CAACmD,GAAG,CAAEX,IAAI,iBACtBhD,OAAA,CAACvB,IAAI;UAAAqI,QAAA,gBACH9G,OAAA,CAACtB,WAAW;YAAAoI,QAAA,gBACV9G,OAAA,CAACnC,GAAG;cAAC6I,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACsB,GAAG,EAAE,CAAE;cAACjB,EAAE,EAAE,CAAE;cAAAH,QAAA,GACnDpB,WAAW,CAAC1C,IAAI,CAAC4F,SAAS,CAAC,eAC5B5I,OAAA,CAACpB,OAAO;gBAACiK,KAAK,EAAE5C,aAAa,CAACjD,IAAI,CAAE;gBAAA8D,QAAA,EACjCb,aAAa,CAACjD,IAAI;cAAC;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACVhG,OAAA,CAAClC,UAAU;gBAACoJ,OAAO,EAAC,OAAO;gBAACkB,MAAM;gBAACpB,EAAE,EAAE;kBAAEmB,IAAI,EAAE;gBAAE,CAAE;gBAAArB,QAAA,EAChD9D,IAAI,CAAC8F;cAAa;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENhG,OAAA,CAAClC,UAAU;cAACoJ,OAAO,EAAC,SAAS;cAACf,KAAK,EAAC,gBAAgB;cAACO,OAAO,EAAC,OAAO;cAAAI,QAAA,GAAC,QAC7D,EAAC9D,IAAI,CAAC+F,mBAAmB;YAAA;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eAEbhG,OAAA,CAAClC,UAAU;cAACoJ,OAAO,EAAC,SAAS;cAACf,KAAK,EAAC,gBAAgB;cAACO,OAAO,EAAC,OAAO;cAAAI,QAAA,GAAC,QAC7D,EAAC9D,IAAI,CAACgG,eAAe;YAAA;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EAEZhD,IAAI,CAACiG,UAAU,iBACdjJ,OAAA,CAAClC,UAAU;cAACoJ,OAAO,EAAC,SAAS;cAACf,KAAK,EAAC,gBAAgB;cAACO,OAAO,EAAC,OAAO;cAAAI,QAAA,GAAC,cACvD,EAAC9D,IAAI,CAACiG,UAAU,CAACZ,KAAK,EAAC,MAAC,EAACrF,IAAI,CAACiG,UAAU,CAACX,MAAM,EAAC,IAC9D;YAAA;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb,EAEAhD,IAAI,CAACkD,GAAG,iBACPlG,OAAA,CAAClC,UAAU;cACToJ,OAAO,EAAC,SAAS;cACjBf,KAAK,EACHnD,IAAI,CAACkD,GAAG,KAAK5F,QAAQ,CAACwB,mBAAmB,IAAI,GAAG,CAAC,GAAG,cAAc,GAClEkB,IAAI,CAACkD,GAAG,KAAK5F,QAAQ,CAACyB,qBAAqB,IAAI,GAAG,CAAC,GAAG,cAAc,GAAG,YACxE;cACD2E,OAAO,EAAC,OAAO;cAAAI,QAAA,GAChB,OACM,EAAC9D,IAAI,CAACkD,GAAG,EAAC,GAAC,EAAClD,IAAI,CAACkG,UAAU,IAAI,IAAIlG,IAAI,CAACkG,UAAU,GAAG;YAAA;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CACb,EAEAhD,IAAI,CAACmG,KAAK,iBACTnJ,OAAA,CAAClC,UAAU;cAACoJ,OAAO,EAAC,SAAS;cAACf,KAAK,EAAC,gBAAgB;cAACO,OAAO,EAAC,OAAO;cAACM,EAAE,EAAE;gBAAEc,EAAE,EAAE;cAAE,CAAE;cAAAhB,QAAA,GAAC,SAC3E,EAAC9D,IAAI,CAACmG,KAAK;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACb,eAEDhG,OAAA,CAACnC,GAAG;cAAC6I,OAAO,EAAC,MAAM;cAACwB,GAAG,EAAE,CAAE;cAACJ,EAAE,EAAE,CAAE;cAAAhB,QAAA,eAChC9G,OAAA,CAAC7B,IAAI;gBACHsK,KAAK,EAAEzF,IAAI,CAACoD,WAAW,GAAG,UAAU,GAAG,gBAAiB;gBACxDD,KAAK,EAAEnD,IAAI,CAACoD,WAAW,GAAG,SAAS,GAAG,SAAU;gBAChD5C,IAAI,EAAC;cAAO;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEdhG,OAAA,CAACrB,WAAW;YAAAmI,QAAA,gBACV9G,OAAA,CAAChC,MAAM;cACLwF,IAAI,EAAC,OAAO;cACZ4F,IAAI,EAAEpG,IAAI,CAACqG,QAAS;cACpBC,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cAAAzC,QAAA,EAC1B;YAED;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThG,OAAA,CAAC5B,UAAU;cACToF,IAAI,EAAC,OAAO;cACZ2C,KAAK,EAAC,OAAO;cACbqD,OAAO,EAAEA,CAAA,KAAM;gBACbvI,eAAe,CAAC+B,IAAI,CAAC;gBACrBjC,mBAAmB,CAAC,IAAI,CAAC;cAC3B,CAAE;cAAA+F,QAAA,eAEF9G,OAAA,CAAChB,UAAU;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,GAzELhD,IAAI,CAAC8B,EAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0EZ,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDhG,OAAA,CAAC3B,MAAM;MAACoL,IAAI,EAAE3I,gBAAiB;MAAC4I,OAAO,EAAEA,CAAA,KAAM3I,mBAAmB,CAAC,KAAK,CAAE;MAAA+F,QAAA,gBACxE9G,OAAA,CAAC1B,WAAW;QAAAwI,QAAA,EAAC;MAAW;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACtChG,OAAA,CAACzB,aAAa;QAAAuI,QAAA,eACZ9G,OAAA,CAAClC,UAAU;UAAAgJ,QAAA,GAAC,oCACuB,EAAC9F,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE8H,aAAa,EAAC,mCAChE;QAAA;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBhG,OAAA,CAACxB,aAAa;QAAAsI,QAAA,gBACZ9G,OAAA,CAAChC,MAAM;UAACwL,OAAO,EAAEA,CAAA,KAAMzI,mBAAmB,CAAC,KAAK,CAAE;UAAA+F,QAAA,EAAC;QAAM;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClEhG,OAAA,CAAChC,MAAM;UAACwL,OAAO,EAAE5E,gBAAiB;UAACuB,KAAK,EAAC,OAAO;UAACe,OAAO,EAAC,WAAW;UAAAJ,QAAA,EAAC;QAErE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC3F,EAAA,CAlcIJ,UAAqC;EAAA,QAyLaJ,WAAW;AAAA;AAAA8J,EAAA,GAzL7D1J,UAAqC;AAoc3C,eAAeA,UAAU;AAAC,IAAA0J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}