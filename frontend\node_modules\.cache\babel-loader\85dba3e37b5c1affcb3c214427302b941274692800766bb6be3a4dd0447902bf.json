{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport NoSsr from \"../NoSsr/index.js\";\nimport Drawer, { getAnchor, isHorizontal } from \"../Drawer/Drawer.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport useEnhancedEffect from \"../utils/useEnhancedEffect.js\";\nimport { useTheme } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTransitionProps } from \"../transitions/utils.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport SwipeArea from \"./SwipeArea.js\";\n\n// This value is closed to what browsers are using internally to\n// trigger a native scroll.\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst UNCERTAINTY_THRESHOLD = 3; // px\n\n// This is the part of the drawer displayed on touch start.\nconst DRAG_STARTED_SIGNAL = 20; // px\n\n// We can only have one instance at the time claiming ownership for handling the swipe.\n// Otherwise, the UX would be confusing.\n// That's why we use a singleton here.\nlet claimedSwipeInstance = null;\n\n// Exported for test purposes.\nexport function reset() {\n  claimedSwipeInstance = null;\n}\nfunction calculateCurrentX(anchor, touches, doc) {\n  return anchor === 'right' ? doc.body.offsetWidth - touches[0].pageX : touches[0].pageX;\n}\nfunction calculateCurrentY(anchor, touches, containerWindow) {\n  return anchor === 'bottom' ? containerWindow.innerHeight - touches[0].clientY : touches[0].clientY;\n}\nfunction getMaxTranslate(horizontalSwipe, paperInstance) {\n  return horizontalSwipe ? paperInstance.clientWidth : paperInstance.clientHeight;\n}\nfunction getTranslate(currentTranslate, startLocation, open, maxTranslate) {\n  return Math.min(Math.max(open ? startLocation - currentTranslate : maxTranslate + startLocation - currentTranslate, 0), maxTranslate);\n}\n\n/**\n * @param {Element | null} element\n * @param {Element} rootNode\n */\nfunction getDomTreeShapes(element, rootNode) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L129\n  const domTreeShapes = [];\n  while (element && element !== rootNode.parentElement) {\n    const style = ownerWindow(rootNode).getComputedStyle(element);\n    if (\n    // Ignore the scroll children if the element is absolute positioned.\n    style.getPropertyValue('position') === 'absolute' ||\n    // Ignore the scroll children if the element has an overflowX hidden\n    style.getPropertyValue('overflow-x') === 'hidden') {\n      // noop\n    } else if (element.clientWidth > 0 && element.scrollWidth > element.clientWidth || element.clientHeight > 0 && element.scrollHeight > element.clientHeight) {\n      // Ignore the nodes that have no width.\n      // Keep elements with a scroll\n      domTreeShapes.push(element);\n    }\n    element = element.parentElement;\n  }\n  return domTreeShapes;\n}\n\n/**\n * @param {object} param0\n * @param {ReturnType<getDomTreeShapes>} param0.domTreeShapes\n */\nfunction computeHasNativeHandler(_ref) {\n  let {\n    domTreeShapes,\n    start,\n    current,\n    anchor\n  } = _ref;\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L175\n  const axisProperties = {\n    scrollPosition: {\n      x: 'scrollLeft',\n      y: 'scrollTop'\n    },\n    scrollLength: {\n      x: 'scrollWidth',\n      y: 'scrollHeight'\n    },\n    clientLength: {\n      x: 'clientWidth',\n      y: 'clientHeight'\n    }\n  };\n  return domTreeShapes.some(shape => {\n    // Determine if we are going backward or forward.\n    let goingForward = current >= start;\n    if (anchor === 'top' || anchor === 'left') {\n      goingForward = !goingForward;\n    }\n    const axis = anchor === 'left' || anchor === 'right' ? 'x' : 'y';\n    const scrollPosition = Math.round(shape[axisProperties.scrollPosition[axis]]);\n    const areNotAtStart = scrollPosition > 0;\n    const areNotAtEnd = scrollPosition + shape[axisProperties.clientLength[axis]] < shape[axisProperties.scrollLength[axis]];\n    if (goingForward && areNotAtEnd || !goingForward && areNotAtStart) {\n      return true;\n    }\n    return false;\n  });\n}\nconst iOS = typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);\nconst SwipeableDrawer = /*#__PURE__*/React.forwardRef(function SwipeableDrawer(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiSwipeableDrawer',\n    props: inProps\n  });\n  const theme = useTheme();\n  const transitionDurationDefault = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n    anchor = 'left',\n    disableBackdropTransition = false,\n    disableDiscovery = false,\n    disableSwipeToOpen = iOS,\n    hideBackdrop,\n    hysteresis = 0.52,\n    allowSwipeInChildren = false,\n    minFlingVelocity = 450,\n    ModalProps: {\n      BackdropProps,\n      ...ModalPropsProp\n    } = {},\n    onClose,\n    onOpen,\n    open = false,\n    PaperProps = {},\n    SwipeAreaProps,\n    swipeAreaWidth = 20,\n    transitionDuration = transitionDurationDefault,\n    variant = 'temporary',\n    // Mobile first.\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const [maybeSwiping, setMaybeSwiping] = React.useState(false);\n  const swipeInstance = React.useRef({\n    isSwiping: null\n  });\n  const swipeAreaRef = React.useRef();\n  const backdropRef = React.useRef();\n  const paperRef = React.useRef();\n  const handleRef = useForkRef(PaperProps.ref, paperRef);\n  const touchDetected = React.useRef(false);\n\n  // Ref for transition duration based on / to match swipe speed\n  const calculatedDurationRef = React.useRef();\n\n  // Use a ref so the open value used is always up to date inside useCallback.\n  useEnhancedEffect(() => {\n    calculatedDurationRef.current = null;\n  }, [open]);\n  const setPosition = React.useCallback(function (translate) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const {\n      mode = null,\n      changeTransition = true\n    } = options;\n    const anchorRtl = getAnchor(theme, anchor);\n    const rtlTranslateMultiplier = ['right', 'bottom'].includes(anchorRtl) ? 1 : -1;\n    const horizontalSwipe = isHorizontal(anchor);\n    const transform = horizontalSwipe ? `translate(${rtlTranslateMultiplier * translate}px, 0)` : `translate(0, ${rtlTranslateMultiplier * translate}px)`;\n    const drawerStyle = paperRef.current.style;\n    drawerStyle.webkitTransform = transform;\n    drawerStyle.transform = transform;\n    let transition = '';\n    if (mode) {\n      transition = theme.transitions.create('all', getTransitionProps({\n        easing: undefined,\n        style: undefined,\n        timeout: transitionDuration\n      }, {\n        mode\n      }));\n    }\n    if (changeTransition) {\n      drawerStyle.webkitTransition = transition;\n      drawerStyle.transition = transition;\n    }\n    if (!disableBackdropTransition && !hideBackdrop) {\n      const backdropStyle = backdropRef.current.style;\n      backdropStyle.opacity = 1 - translate / getMaxTranslate(horizontalSwipe, paperRef.current);\n      if (changeTransition) {\n        backdropStyle.webkitTransition = transition;\n        backdropStyle.transition = transition;\n      }\n    }\n  }, [anchor, disableBackdropTransition, hideBackdrop, theme, transitionDuration]);\n  const handleBodyTouchEnd = useEventCallback(nativeEvent => {\n    if (!touchDetected.current) {\n      return;\n    }\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- claimedSwipeInstance is a singleton\n    claimedSwipeInstance = null;\n    touchDetected.current = false;\n    ReactDOM.flushSync(() => {\n      setMaybeSwiping(false);\n    });\n\n    // The swipe wasn't started.\n    if (!swipeInstance.current.isSwiping) {\n      swipeInstance.current.isSwiping = null;\n      return;\n    }\n    swipeInstance.current.isSwiping = null;\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontal = isHorizontal(anchor);\n    let current;\n    if (horizontal) {\n      current = calculateCurrentX(anchorRtl, nativeEvent.changedTouches, ownerDocument(nativeEvent.currentTarget));\n    } else {\n      current = calculateCurrentY(anchorRtl, nativeEvent.changedTouches, ownerWindow(nativeEvent.currentTarget));\n    }\n    const startLocation = horizontal ? swipeInstance.current.startX : swipeInstance.current.startY;\n    const maxTranslate = getMaxTranslate(horizontal, paperRef.current);\n    const currentTranslate = getTranslate(current, startLocation, open, maxTranslate);\n    const translateRatio = currentTranslate / maxTranslate;\n    if (Math.abs(swipeInstance.current.velocity) > minFlingVelocity) {\n      // Calculate transition duration to match swipe speed\n      calculatedDurationRef.current = Math.abs((maxTranslate - currentTranslate) / swipeInstance.current.velocity) * 1000;\n    }\n    if (open) {\n      if (swipeInstance.current.velocity > minFlingVelocity || translateRatio > hysteresis) {\n        onClose();\n      } else {\n        // Reset the position, the swipe was aborted.\n        setPosition(0, {\n          mode: 'exit'\n        });\n      }\n      return;\n    }\n    if (swipeInstance.current.velocity < -minFlingVelocity || 1 - translateRatio > hysteresis) {\n      onOpen();\n    } else {\n      // Reset the position, the swipe was aborted.\n      setPosition(getMaxTranslate(horizontal, paperRef.current), {\n        mode: 'enter'\n      });\n    }\n  });\n  const startMaybeSwiping = function () {\n    let force = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (!maybeSwiping) {\n      // on Safari Mobile, if you want to be able to have the 'click' event fired on child elements, nothing in the DOM can be changed.\n      // this is because Safari Mobile will not fire any mouse events (still fires touch though) if the DOM changes during mousemove.\n      // so do this change on first touchmove instead of touchstart\n      if (force || !(disableDiscovery && allowSwipeInChildren)) {\n        ReactDOM.flushSync(() => {\n          setMaybeSwiping(true);\n        });\n      }\n      const horizontalSwipe = isHorizontal(anchor);\n      if (!open && paperRef.current) {\n        // The ref may be null when a parent component updates while swiping.\n        setPosition(getMaxTranslate(horizontalSwipe, paperRef.current) + (disableDiscovery ? 15 : -DRAG_STARTED_SIGNAL), {\n          changeTransition: false\n        });\n      }\n      swipeInstance.current.velocity = 0;\n      swipeInstance.current.lastTime = null;\n      swipeInstance.current.lastTranslate = null;\n      swipeInstance.current.paperHit = false;\n      touchDetected.current = true;\n    }\n  };\n  const handleBodyTouchMove = useEventCallback(nativeEvent => {\n    // the ref may be null when a parent component updates while swiping\n    if (!paperRef.current || !touchDetected.current) {\n      return;\n    }\n\n    // We are not supposed to handle this touch move because the swipe was started in a scrollable container in the drawer\n    if (claimedSwipeInstance !== null && claimedSwipeInstance !== swipeInstance.current) {\n      return;\n    }\n    startMaybeSwiping(true);\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (open && paperRef.current.contains(nativeEvent.target) && claimedSwipeInstance === null) {\n      const domTreeShapes = getDomTreeShapes(nativeEvent.target, paperRef.current);\n      const hasNativeHandler = computeHasNativeHandler({\n        domTreeShapes,\n        start: horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY,\n        current: horizontalSwipe ? currentX : currentY,\n        anchor\n      });\n      if (hasNativeHandler) {\n        claimedSwipeInstance = true;\n        return;\n      }\n      claimedSwipeInstance = swipeInstance.current;\n    }\n\n    // We don't know yet.\n    if (swipeInstance.current.isSwiping == null) {\n      const dx = Math.abs(currentX - swipeInstance.current.startX);\n      const dy = Math.abs(currentY - swipeInstance.current.startY);\n      const definitelySwiping = horizontalSwipe ? dx > dy && dx > UNCERTAINTY_THRESHOLD : dy > dx && dy > UNCERTAINTY_THRESHOLD;\n      if (definitelySwiping && nativeEvent.cancelable) {\n        nativeEvent.preventDefault();\n      }\n      if (definitelySwiping === true || (horizontalSwipe ? dy > UNCERTAINTY_THRESHOLD : dx > UNCERTAINTY_THRESHOLD)) {\n        swipeInstance.current.isSwiping = definitelySwiping;\n        if (!definitelySwiping) {\n          handleBodyTouchEnd(nativeEvent);\n          return;\n        }\n\n        // Shift the starting point.\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n\n        // Compensate for the part of the drawer displayed on touch start.\n        if (!disableDiscovery && !open) {\n          if (horizontalSwipe) {\n            swipeInstance.current.startX -= DRAG_STARTED_SIGNAL;\n          } else {\n            swipeInstance.current.startY -= DRAG_STARTED_SIGNAL;\n          }\n        }\n      }\n    }\n    if (!swipeInstance.current.isSwiping) {\n      return;\n    }\n    const maxTranslate = getMaxTranslate(horizontalSwipe, paperRef.current);\n    let startLocation = horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY;\n    if (open && !swipeInstance.current.paperHit) {\n      startLocation = Math.min(startLocation, maxTranslate);\n    }\n    const translate = getTranslate(horizontalSwipe ? currentX : currentY, startLocation, open, maxTranslate);\n    if (open) {\n      if (!swipeInstance.current.paperHit) {\n        const paperHit = horizontalSwipe ? currentX < maxTranslate : currentY < maxTranslate;\n        if (paperHit) {\n          swipeInstance.current.paperHit = true;\n          swipeInstance.current.startX = currentX;\n          swipeInstance.current.startY = currentY;\n        } else {\n          return;\n        }\n      } else if (translate === 0) {\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n      }\n    }\n    if (swipeInstance.current.lastTranslate === null) {\n      swipeInstance.current.lastTranslate = translate;\n      swipeInstance.current.lastTime = performance.now() + 1;\n    }\n    const velocity = (translate - swipeInstance.current.lastTranslate) / (performance.now() - swipeInstance.current.lastTime) * 1e3;\n\n    // Low Pass filter.\n    swipeInstance.current.velocity = swipeInstance.current.velocity * 0.4 + velocity * 0.6;\n    swipeInstance.current.lastTranslate = translate;\n    swipeInstance.current.lastTime = performance.now();\n\n    // We are swiping, let's prevent the scroll event on iOS.\n    if (nativeEvent.cancelable) {\n      nativeEvent.preventDefault();\n    }\n    setPosition(translate);\n  });\n  const handleBodyTouchStart = useEventCallback(nativeEvent => {\n    // We are not supposed to handle this touch move.\n    // Example of use case: ignore the event if there is a Slider.\n    if (nativeEvent.defaultPrevented) {\n      return;\n    }\n\n    // We can only have one node at the time claiming ownership for handling the swipe.\n    if (nativeEvent.defaultMuiPrevented) {\n      return;\n    }\n\n    // At least one element clogs the drawer interaction zone.\n    if (open && (hideBackdrop || !backdropRef.current.contains(nativeEvent.target)) && !paperRef.current.contains(nativeEvent.target)) {\n      return;\n    }\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (!open) {\n      // logic for if swipe should be ignored:\n      // if disableSwipeToOpen\n      // if target != swipeArea, and target is not a child of paper ref\n      // if is a child of paper ref, and `allowSwipeInChildren` does not allow it\n      if (disableSwipeToOpen || !(nativeEvent.target === swipeAreaRef.current || paperRef.current?.contains(nativeEvent.target) && (typeof allowSwipeInChildren === 'function' ? allowSwipeInChildren(nativeEvent, swipeAreaRef.current, paperRef.current) : allowSwipeInChildren))) {\n        return;\n      }\n      if (horizontalSwipe) {\n        if (currentX > swipeAreaWidth) {\n          return;\n        }\n      } else if (currentY > swipeAreaWidth) {\n        return;\n      }\n    }\n    nativeEvent.defaultMuiPrevented = true;\n    claimedSwipeInstance = null;\n    swipeInstance.current.startX = currentX;\n    swipeInstance.current.startY = currentY;\n    startMaybeSwiping();\n  });\n  React.useEffect(() => {\n    if (variant === 'temporary') {\n      const doc = ownerDocument(paperRef.current);\n      doc.addEventListener('touchstart', handleBodyTouchStart);\n      // A blocking listener prevents Firefox's navbar to auto-hide on scroll.\n      // It only needs to prevent scrolling on the drawer's content when open.\n      // When closed, the overlay prevents scrolling.\n      doc.addEventListener('touchmove', handleBodyTouchMove, {\n        passive: !open\n      });\n      doc.addEventListener('touchend', handleBodyTouchEnd);\n      return () => {\n        doc.removeEventListener('touchstart', handleBodyTouchStart);\n        doc.removeEventListener('touchmove', handleBodyTouchMove, {\n          passive: !open\n        });\n        doc.removeEventListener('touchend', handleBodyTouchEnd);\n      };\n    }\n    return undefined;\n  }, [variant, open, handleBodyTouchStart, handleBodyTouchMove, handleBodyTouchEnd]);\n  React.useEffect(() => () => {\n    // We need to release the lock.\n    if (claimedSwipeInstance === swipeInstance.current) {\n      claimedSwipeInstance = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    if (!open) {\n      setMaybeSwiping(false);\n    }\n  }, [open]);\n  const [SwipeAreaSlot, swipeAreaSlotProps] = useSlot('swipeArea', {\n    ref: swipeAreaRef,\n    elementType: SwipeArea,\n    ownerState: props,\n    externalForwardedProps: {\n      slots,\n      slotProps: {\n        swipeArea: SwipeAreaProps,\n        ...slotProps\n      }\n    },\n    additionalProps: {\n      width: swipeAreaWidth,\n      anchor\n    }\n  });\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(Drawer, {\n      open: variant === 'temporary' && maybeSwiping ? true : open,\n      variant: variant,\n      ModalProps: {\n        BackdropProps: {\n          ...BackdropProps,\n          ref: backdropRef\n        },\n        // Ensures that paperRef.current will be defined inside the touch start event handler\n        // See https://github.com/mui/material-ui/issues/30414 for more information\n        ...(variant === 'temporary' && {\n          keepMounted: true\n        }),\n        ...ModalPropsProp\n      },\n      hideBackdrop: hideBackdrop,\n      anchor: anchor,\n      transitionDuration: calculatedDurationRef.current || transitionDuration,\n      onClose: onClose,\n      ref: ref,\n      slots: slots,\n      slotProps: {\n        ...slotProps,\n        backdrop: mergeSlotProps(slotProps.backdrop ?? BackdropProps, {\n          ref: backdropRef\n        }),\n        paper: mergeSlotProps(slotProps.paper ?? PaperProps, {\n          style: {\n            pointerEvents: variant === 'temporary' && !open && !allowSwipeInChildren ? 'none' : ''\n          },\n          ref: handleRef\n        })\n      },\n      ...other\n    }), !disableSwipeToOpen && variant === 'temporary' && /*#__PURE__*/_jsx(NoSsr, {\n      children: /*#__PURE__*/_jsx(SwipeAreaSlot, {\n        ...swipeAreaSlotProps\n      })\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeableDrawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If set to true, the swipe event will open the drawer even if the user begins the swipe on one of the drawer's children.\n   * This can be useful in scenarios where the drawer is partially visible.\n   * You can customize it further with a callback that determines which children the user can drag over to open the drawer\n   * (for example, to ignore other elements that handle touch move events, like sliders).\n   *\n   * @param {TouchEvent} event The 'touchstart' event\n   * @param {HTMLDivElement} swipeArea The swipe area element\n   * @param {HTMLDivElement} paper The drawer's paper element\n   *\n   * @default false\n   */\n  allowSwipeInChildren: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable the backdrop transition.\n   * This can improve the FPS on low-end devices.\n   * @default false\n   */\n  disableBackdropTransition: PropTypes.bool,\n  /**\n   * If `true`, touching the screen near the edge of the drawer will not slide in the drawer a bit\n   * to promote accidental discovery of the swipe gesture.\n   * @default false\n   */\n  disableDiscovery: PropTypes.bool,\n  /**\n   * If `true`, swipe to open is disabled. This is useful in browsers where swiping triggers\n   * navigation actions. Swipe to open is disabled on iOS browsers by default.\n   * @default typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent)\n   */\n  disableSwipeToOpen: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Affects how far the drawer must be opened/closed to change its state.\n   * Specified as percent (0-1) of the width of the drawer\n   * @default 0.52\n   */\n  hysteresis: PropTypes.number,\n  /**\n   * Defines, from which (average) velocity on, the swipe is\n   * defined as complete although hysteresis isn't reached.\n   * Good threshold is between 250 - 1000 px/s\n   * @default 450\n   */\n  minFlingVelocity: PropTypes.number,\n  /**\n   * @ignore\n   */\n  ModalProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    BackdropProps: PropTypes.shape({\n      component: elementTypeAcceptingRef\n    })\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onClose: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the component requests to be opened.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onOpen: PropTypes.func.isRequired,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef,\n    style: PropTypes.object\n  }),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    docked: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    swipeArea: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    docked: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    swipeArea: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The element is used to intercept the touch events on the edge.\n   * @deprecated use the `slotProps.swipeArea` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  SwipeAreaProps: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` that\n   * the drawer can be swiped open from.\n   * @default 20\n   */\n  swipeAreaWidth: PropTypes.number,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * @ignore\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default SwipeableDrawer;", "map": {"version": 3, "names": ["React", "ReactDOM", "PropTypes", "elementTypeAcceptingRef", "NoSsr", "Drawer", "getAnchor", "isHorizontal", "useForkRef", "ownerDocument", "ownerWindow", "useEventCallback", "useEnhancedEffect", "useTheme", "useDefaultProps", "getTransitionProps", "mergeSlotProps", "useSlot", "SwipeArea", "jsx", "_jsx", "jsxs", "_jsxs", "UNCERTAINTY_THRESHOLD", "DRAG_STARTED_SIGNAL", "claimedSwipeInstance", "reset", "calculateCurrentX", "anchor", "touches", "doc", "body", "offsetWidth", "pageX", "calculateCurrentY", "containerWindow", "innerHeight", "clientY", "getMaxTranslate", "horizontalSwipe", "paperInstance", "clientWidth", "clientHeight", "getTranslate", "currentTranslate", "startLocation", "open", "maxTranslate", "Math", "min", "max", "getDomTreeShapes", "element", "rootNode", "domTreeShapes", "parentElement", "style", "getComputedStyle", "getPropertyValue", "scrollWidth", "scrollHeight", "push", "computeHasNativeHandler", "_ref", "start", "current", "axisProperties", "scrollPosition", "x", "y", "<PERSON><PERSON><PERSON><PERSON>", "clientLength", "some", "shape", "goingForward", "axis", "round", "areNotAtStart", "areNotAtEnd", "iOS", "navigator", "test", "userAgent", "SwipeableDrawer", "forwardRef", "inProps", "ref", "props", "name", "theme", "transitionDurationDefault", "enter", "transitions", "duration", "enteringScreen", "exit", "leavingScreen", "disableBackdropTransition", "disableDiscovery", "disableSwipeToOpen", "hideBackdrop", "hysteresis", "allowSwipeInChildren", "minFlingVelocity", "ModalProps", "BackdropProps", "ModalPropsProp", "onClose", "onOpen", "PaperProps", "SwipeAreaProps", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON>", "transitionDuration", "variant", "slots", "slotProps", "other", "maybeSwiping", "setMaybeSwiping", "useState", "swipeInstance", "useRef", "isSwiping", "swipeAreaRef", "backdropRef", "paperRef", "handleRef", "touchDetected", "calculatedDurationRef", "setPosition", "useCallback", "translate", "options", "arguments", "length", "undefined", "mode", "changeTransition", "anchorRtl", "rtlTranslateMultiplier", "includes", "transform", "drawerStyle", "webkitTransform", "transition", "create", "easing", "timeout", "webkitTransition", "backdropStyle", "opacity", "handleBodyTouchEnd", "nativeEvent", "flushSync", "horizontal", "changedTouches", "currentTarget", "startX", "startY", "translateRatio", "abs", "velocity", "startMaybeSwiping", "force", "lastTime", "lastTranslate", "paperHit", "handleBodyTouchMove", "currentX", "currentY", "contains", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dx", "dy", "definitelySwiping", "cancelable", "preventDefault", "performance", "now", "handleBodyTouchStart", "defaultPrevented", "defaultMuiPrevented", "useEffect", "addEventListener", "passive", "removeEventListener", "SwipeAreaSlot", "swipeAreaSlotProps", "elementType", "ownerState", "externalForwardedProps", "swipeArea", "additionalProps", "width", "Fragment", "children", "keepMounted", "backdrop", "paper", "pointerEvents", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "func", "bool", "oneOf", "node", "number", "component", "isRequired", "object", "docked", "root", "appear"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/SwipeableDrawer/SwipeableDrawer.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport NoSsr from \"../NoSsr/index.js\";\nimport Drawer, { getAnchor, isHorizontal } from \"../Drawer/Drawer.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport useEnhancedEffect from \"../utils/useEnhancedEffect.js\";\nimport { useTheme } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTransitionProps } from \"../transitions/utils.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport SwipeArea from \"./SwipeArea.js\";\n\n// This value is closed to what browsers are using internally to\n// trigger a native scroll.\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst UNCERTAINTY_THRESHOLD = 3; // px\n\n// This is the part of the drawer displayed on touch start.\nconst DRAG_STARTED_SIGNAL = 20; // px\n\n// We can only have one instance at the time claiming ownership for handling the swipe.\n// Otherwise, the UX would be confusing.\n// That's why we use a singleton here.\nlet claimedSwipeInstance = null;\n\n// Exported for test purposes.\nexport function reset() {\n  claimedSwipeInstance = null;\n}\nfunction calculateCurrentX(anchor, touches, doc) {\n  return anchor === 'right' ? doc.body.offsetWidth - touches[0].pageX : touches[0].pageX;\n}\nfunction calculateCurrentY(anchor, touches, containerWindow) {\n  return anchor === 'bottom' ? containerWindow.innerHeight - touches[0].clientY : touches[0].clientY;\n}\nfunction getMaxTranslate(horizontalSwipe, paperInstance) {\n  return horizontalSwipe ? paperInstance.clientWidth : paperInstance.clientHeight;\n}\nfunction getTranslate(currentTranslate, startLocation, open, maxTranslate) {\n  return Math.min(Math.max(open ? startLocation - currentTranslate : maxTranslate + startLocation - currentTranslate, 0), maxTranslate);\n}\n\n/**\n * @param {Element | null} element\n * @param {Element} rootNode\n */\nfunction getDomTreeShapes(element, rootNode) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L129\n  const domTreeShapes = [];\n  while (element && element !== rootNode.parentElement) {\n    const style = ownerWindow(rootNode).getComputedStyle(element);\n    if (\n    // Ignore the scroll children if the element is absolute positioned.\n    style.getPropertyValue('position') === 'absolute' ||\n    // Ignore the scroll children if the element has an overflowX hidden\n    style.getPropertyValue('overflow-x') === 'hidden') {\n      // noop\n    } else if (element.clientWidth > 0 && element.scrollWidth > element.clientWidth || element.clientHeight > 0 && element.scrollHeight > element.clientHeight) {\n      // Ignore the nodes that have no width.\n      // Keep elements with a scroll\n      domTreeShapes.push(element);\n    }\n    element = element.parentElement;\n  }\n  return domTreeShapes;\n}\n\n/**\n * @param {object} param0\n * @param {ReturnType<getDomTreeShapes>} param0.domTreeShapes\n */\nfunction computeHasNativeHandler({\n  domTreeShapes,\n  start,\n  current,\n  anchor\n}) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L175\n  const axisProperties = {\n    scrollPosition: {\n      x: 'scrollLeft',\n      y: 'scrollTop'\n    },\n    scrollLength: {\n      x: 'scrollWidth',\n      y: 'scrollHeight'\n    },\n    clientLength: {\n      x: 'clientWidth',\n      y: 'clientHeight'\n    }\n  };\n  return domTreeShapes.some(shape => {\n    // Determine if we are going backward or forward.\n    let goingForward = current >= start;\n    if (anchor === 'top' || anchor === 'left') {\n      goingForward = !goingForward;\n    }\n    const axis = anchor === 'left' || anchor === 'right' ? 'x' : 'y';\n    const scrollPosition = Math.round(shape[axisProperties.scrollPosition[axis]]);\n    const areNotAtStart = scrollPosition > 0;\n    const areNotAtEnd = scrollPosition + shape[axisProperties.clientLength[axis]] < shape[axisProperties.scrollLength[axis]];\n    if (goingForward && areNotAtEnd || !goingForward && areNotAtStart) {\n      return true;\n    }\n    return false;\n  });\n}\nconst iOS = typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);\nconst SwipeableDrawer = /*#__PURE__*/React.forwardRef(function SwipeableDrawer(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiSwipeableDrawer',\n    props: inProps\n  });\n  const theme = useTheme();\n  const transitionDurationDefault = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n    anchor = 'left',\n    disableBackdropTransition = false,\n    disableDiscovery = false,\n    disableSwipeToOpen = iOS,\n    hideBackdrop,\n    hysteresis = 0.52,\n    allowSwipeInChildren = false,\n    minFlingVelocity = 450,\n    ModalProps: {\n      BackdropProps,\n      ...ModalPropsProp\n    } = {},\n    onClose,\n    onOpen,\n    open = false,\n    PaperProps = {},\n    SwipeAreaProps,\n    swipeAreaWidth = 20,\n    transitionDuration = transitionDurationDefault,\n    variant = 'temporary',\n    // Mobile first.\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const [maybeSwiping, setMaybeSwiping] = React.useState(false);\n  const swipeInstance = React.useRef({\n    isSwiping: null\n  });\n  const swipeAreaRef = React.useRef();\n  const backdropRef = React.useRef();\n  const paperRef = React.useRef();\n  const handleRef = useForkRef(PaperProps.ref, paperRef);\n  const touchDetected = React.useRef(false);\n\n  // Ref for transition duration based on / to match swipe speed\n  const calculatedDurationRef = React.useRef();\n\n  // Use a ref so the open value used is always up to date inside useCallback.\n  useEnhancedEffect(() => {\n    calculatedDurationRef.current = null;\n  }, [open]);\n  const setPosition = React.useCallback((translate, options = {}) => {\n    const {\n      mode = null,\n      changeTransition = true\n    } = options;\n    const anchorRtl = getAnchor(theme, anchor);\n    const rtlTranslateMultiplier = ['right', 'bottom'].includes(anchorRtl) ? 1 : -1;\n    const horizontalSwipe = isHorizontal(anchor);\n    const transform = horizontalSwipe ? `translate(${rtlTranslateMultiplier * translate}px, 0)` : `translate(0, ${rtlTranslateMultiplier * translate}px)`;\n    const drawerStyle = paperRef.current.style;\n    drawerStyle.webkitTransform = transform;\n    drawerStyle.transform = transform;\n    let transition = '';\n    if (mode) {\n      transition = theme.transitions.create('all', getTransitionProps({\n        easing: undefined,\n        style: undefined,\n        timeout: transitionDuration\n      }, {\n        mode\n      }));\n    }\n    if (changeTransition) {\n      drawerStyle.webkitTransition = transition;\n      drawerStyle.transition = transition;\n    }\n    if (!disableBackdropTransition && !hideBackdrop) {\n      const backdropStyle = backdropRef.current.style;\n      backdropStyle.opacity = 1 - translate / getMaxTranslate(horizontalSwipe, paperRef.current);\n      if (changeTransition) {\n        backdropStyle.webkitTransition = transition;\n        backdropStyle.transition = transition;\n      }\n    }\n  }, [anchor, disableBackdropTransition, hideBackdrop, theme, transitionDuration]);\n  const handleBodyTouchEnd = useEventCallback(nativeEvent => {\n    if (!touchDetected.current) {\n      return;\n    }\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- claimedSwipeInstance is a singleton\n    claimedSwipeInstance = null;\n    touchDetected.current = false;\n    ReactDOM.flushSync(() => {\n      setMaybeSwiping(false);\n    });\n\n    // The swipe wasn't started.\n    if (!swipeInstance.current.isSwiping) {\n      swipeInstance.current.isSwiping = null;\n      return;\n    }\n    swipeInstance.current.isSwiping = null;\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontal = isHorizontal(anchor);\n    let current;\n    if (horizontal) {\n      current = calculateCurrentX(anchorRtl, nativeEvent.changedTouches, ownerDocument(nativeEvent.currentTarget));\n    } else {\n      current = calculateCurrentY(anchorRtl, nativeEvent.changedTouches, ownerWindow(nativeEvent.currentTarget));\n    }\n    const startLocation = horizontal ? swipeInstance.current.startX : swipeInstance.current.startY;\n    const maxTranslate = getMaxTranslate(horizontal, paperRef.current);\n    const currentTranslate = getTranslate(current, startLocation, open, maxTranslate);\n    const translateRatio = currentTranslate / maxTranslate;\n    if (Math.abs(swipeInstance.current.velocity) > minFlingVelocity) {\n      // Calculate transition duration to match swipe speed\n      calculatedDurationRef.current = Math.abs((maxTranslate - currentTranslate) / swipeInstance.current.velocity) * 1000;\n    }\n    if (open) {\n      if (swipeInstance.current.velocity > minFlingVelocity || translateRatio > hysteresis) {\n        onClose();\n      } else {\n        // Reset the position, the swipe was aborted.\n        setPosition(0, {\n          mode: 'exit'\n        });\n      }\n      return;\n    }\n    if (swipeInstance.current.velocity < -minFlingVelocity || 1 - translateRatio > hysteresis) {\n      onOpen();\n    } else {\n      // Reset the position, the swipe was aborted.\n      setPosition(getMaxTranslate(horizontal, paperRef.current), {\n        mode: 'enter'\n      });\n    }\n  });\n  const startMaybeSwiping = (force = false) => {\n    if (!maybeSwiping) {\n      // on Safari Mobile, if you want to be able to have the 'click' event fired on child elements, nothing in the DOM can be changed.\n      // this is because Safari Mobile will not fire any mouse events (still fires touch though) if the DOM changes during mousemove.\n      // so do this change on first touchmove instead of touchstart\n      if (force || !(disableDiscovery && allowSwipeInChildren)) {\n        ReactDOM.flushSync(() => {\n          setMaybeSwiping(true);\n        });\n      }\n      const horizontalSwipe = isHorizontal(anchor);\n      if (!open && paperRef.current) {\n        // The ref may be null when a parent component updates while swiping.\n        setPosition(getMaxTranslate(horizontalSwipe, paperRef.current) + (disableDiscovery ? 15 : -DRAG_STARTED_SIGNAL), {\n          changeTransition: false\n        });\n      }\n      swipeInstance.current.velocity = 0;\n      swipeInstance.current.lastTime = null;\n      swipeInstance.current.lastTranslate = null;\n      swipeInstance.current.paperHit = false;\n      touchDetected.current = true;\n    }\n  };\n  const handleBodyTouchMove = useEventCallback(nativeEvent => {\n    // the ref may be null when a parent component updates while swiping\n    if (!paperRef.current || !touchDetected.current) {\n      return;\n    }\n\n    // We are not supposed to handle this touch move because the swipe was started in a scrollable container in the drawer\n    if (claimedSwipeInstance !== null && claimedSwipeInstance !== swipeInstance.current) {\n      return;\n    }\n    startMaybeSwiping(true);\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (open && paperRef.current.contains(nativeEvent.target) && claimedSwipeInstance === null) {\n      const domTreeShapes = getDomTreeShapes(nativeEvent.target, paperRef.current);\n      const hasNativeHandler = computeHasNativeHandler({\n        domTreeShapes,\n        start: horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY,\n        current: horizontalSwipe ? currentX : currentY,\n        anchor\n      });\n      if (hasNativeHandler) {\n        claimedSwipeInstance = true;\n        return;\n      }\n      claimedSwipeInstance = swipeInstance.current;\n    }\n\n    // We don't know yet.\n    if (swipeInstance.current.isSwiping == null) {\n      const dx = Math.abs(currentX - swipeInstance.current.startX);\n      const dy = Math.abs(currentY - swipeInstance.current.startY);\n      const definitelySwiping = horizontalSwipe ? dx > dy && dx > UNCERTAINTY_THRESHOLD : dy > dx && dy > UNCERTAINTY_THRESHOLD;\n      if (definitelySwiping && nativeEvent.cancelable) {\n        nativeEvent.preventDefault();\n      }\n      if (definitelySwiping === true || (horizontalSwipe ? dy > UNCERTAINTY_THRESHOLD : dx > UNCERTAINTY_THRESHOLD)) {\n        swipeInstance.current.isSwiping = definitelySwiping;\n        if (!definitelySwiping) {\n          handleBodyTouchEnd(nativeEvent);\n          return;\n        }\n\n        // Shift the starting point.\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n\n        // Compensate for the part of the drawer displayed on touch start.\n        if (!disableDiscovery && !open) {\n          if (horizontalSwipe) {\n            swipeInstance.current.startX -= DRAG_STARTED_SIGNAL;\n          } else {\n            swipeInstance.current.startY -= DRAG_STARTED_SIGNAL;\n          }\n        }\n      }\n    }\n    if (!swipeInstance.current.isSwiping) {\n      return;\n    }\n    const maxTranslate = getMaxTranslate(horizontalSwipe, paperRef.current);\n    let startLocation = horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY;\n    if (open && !swipeInstance.current.paperHit) {\n      startLocation = Math.min(startLocation, maxTranslate);\n    }\n    const translate = getTranslate(horizontalSwipe ? currentX : currentY, startLocation, open, maxTranslate);\n    if (open) {\n      if (!swipeInstance.current.paperHit) {\n        const paperHit = horizontalSwipe ? currentX < maxTranslate : currentY < maxTranslate;\n        if (paperHit) {\n          swipeInstance.current.paperHit = true;\n          swipeInstance.current.startX = currentX;\n          swipeInstance.current.startY = currentY;\n        } else {\n          return;\n        }\n      } else if (translate === 0) {\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n      }\n    }\n    if (swipeInstance.current.lastTranslate === null) {\n      swipeInstance.current.lastTranslate = translate;\n      swipeInstance.current.lastTime = performance.now() + 1;\n    }\n    const velocity = (translate - swipeInstance.current.lastTranslate) / (performance.now() - swipeInstance.current.lastTime) * 1e3;\n\n    // Low Pass filter.\n    swipeInstance.current.velocity = swipeInstance.current.velocity * 0.4 + velocity * 0.6;\n    swipeInstance.current.lastTranslate = translate;\n    swipeInstance.current.lastTime = performance.now();\n\n    // We are swiping, let's prevent the scroll event on iOS.\n    if (nativeEvent.cancelable) {\n      nativeEvent.preventDefault();\n    }\n    setPosition(translate);\n  });\n  const handleBodyTouchStart = useEventCallback(nativeEvent => {\n    // We are not supposed to handle this touch move.\n    // Example of use case: ignore the event if there is a Slider.\n    if (nativeEvent.defaultPrevented) {\n      return;\n    }\n\n    // We can only have one node at the time claiming ownership for handling the swipe.\n    if (nativeEvent.defaultMuiPrevented) {\n      return;\n    }\n\n    // At least one element clogs the drawer interaction zone.\n    if (open && (hideBackdrop || !backdropRef.current.contains(nativeEvent.target)) && !paperRef.current.contains(nativeEvent.target)) {\n      return;\n    }\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (!open) {\n      // logic for if swipe should be ignored:\n      // if disableSwipeToOpen\n      // if target != swipeArea, and target is not a child of paper ref\n      // if is a child of paper ref, and `allowSwipeInChildren` does not allow it\n      if (disableSwipeToOpen || !(nativeEvent.target === swipeAreaRef.current || paperRef.current?.contains(nativeEvent.target) && (typeof allowSwipeInChildren === 'function' ? allowSwipeInChildren(nativeEvent, swipeAreaRef.current, paperRef.current) : allowSwipeInChildren))) {\n        return;\n      }\n      if (horizontalSwipe) {\n        if (currentX > swipeAreaWidth) {\n          return;\n        }\n      } else if (currentY > swipeAreaWidth) {\n        return;\n      }\n    }\n    nativeEvent.defaultMuiPrevented = true;\n    claimedSwipeInstance = null;\n    swipeInstance.current.startX = currentX;\n    swipeInstance.current.startY = currentY;\n    startMaybeSwiping();\n  });\n  React.useEffect(() => {\n    if (variant === 'temporary') {\n      const doc = ownerDocument(paperRef.current);\n      doc.addEventListener('touchstart', handleBodyTouchStart);\n      // A blocking listener prevents Firefox's navbar to auto-hide on scroll.\n      // It only needs to prevent scrolling on the drawer's content when open.\n      // When closed, the overlay prevents scrolling.\n      doc.addEventListener('touchmove', handleBodyTouchMove, {\n        passive: !open\n      });\n      doc.addEventListener('touchend', handleBodyTouchEnd);\n      return () => {\n        doc.removeEventListener('touchstart', handleBodyTouchStart);\n        doc.removeEventListener('touchmove', handleBodyTouchMove, {\n          passive: !open\n        });\n        doc.removeEventListener('touchend', handleBodyTouchEnd);\n      };\n    }\n    return undefined;\n  }, [variant, open, handleBodyTouchStart, handleBodyTouchMove, handleBodyTouchEnd]);\n  React.useEffect(() => () => {\n    // We need to release the lock.\n    if (claimedSwipeInstance === swipeInstance.current) {\n      claimedSwipeInstance = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    if (!open) {\n      setMaybeSwiping(false);\n    }\n  }, [open]);\n  const [SwipeAreaSlot, swipeAreaSlotProps] = useSlot('swipeArea', {\n    ref: swipeAreaRef,\n    elementType: SwipeArea,\n    ownerState: props,\n    externalForwardedProps: {\n      slots,\n      slotProps: {\n        swipeArea: SwipeAreaProps,\n        ...slotProps\n      }\n    },\n    additionalProps: {\n      width: swipeAreaWidth,\n      anchor\n    }\n  });\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(Drawer, {\n      open: variant === 'temporary' && maybeSwiping ? true : open,\n      variant: variant,\n      ModalProps: {\n        BackdropProps: {\n          ...BackdropProps,\n          ref: backdropRef\n        },\n        // Ensures that paperRef.current will be defined inside the touch start event handler\n        // See https://github.com/mui/material-ui/issues/30414 for more information\n        ...(variant === 'temporary' && {\n          keepMounted: true\n        }),\n        ...ModalPropsProp\n      },\n      hideBackdrop: hideBackdrop,\n      anchor: anchor,\n      transitionDuration: calculatedDurationRef.current || transitionDuration,\n      onClose: onClose,\n      ref: ref,\n      slots: slots,\n      slotProps: {\n        ...slotProps,\n        backdrop: mergeSlotProps(slotProps.backdrop ?? BackdropProps, {\n          ref: backdropRef\n        }),\n        paper: mergeSlotProps(slotProps.paper ?? PaperProps, {\n          style: {\n            pointerEvents: variant === 'temporary' && !open && !allowSwipeInChildren ? 'none' : ''\n          },\n          ref: handleRef\n        })\n      },\n      ...other\n    }), !disableSwipeToOpen && variant === 'temporary' && /*#__PURE__*/_jsx(NoSsr, {\n      children: /*#__PURE__*/_jsx(SwipeAreaSlot, {\n        ...swipeAreaSlotProps\n      })\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeableDrawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If set to true, the swipe event will open the drawer even if the user begins the swipe on one of the drawer's children.\n   * This can be useful in scenarios where the drawer is partially visible.\n   * You can customize it further with a callback that determines which children the user can drag over to open the drawer\n   * (for example, to ignore other elements that handle touch move events, like sliders).\n   *\n   * @param {TouchEvent} event The 'touchstart' event\n   * @param {HTMLDivElement} swipeArea The swipe area element\n   * @param {HTMLDivElement} paper The drawer's paper element\n   *\n   * @default false\n   */\n  allowSwipeInChildren: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable the backdrop transition.\n   * This can improve the FPS on low-end devices.\n   * @default false\n   */\n  disableBackdropTransition: PropTypes.bool,\n  /**\n   * If `true`, touching the screen near the edge of the drawer will not slide in the drawer a bit\n   * to promote accidental discovery of the swipe gesture.\n   * @default false\n   */\n  disableDiscovery: PropTypes.bool,\n  /**\n   * If `true`, swipe to open is disabled. This is useful in browsers where swiping triggers\n   * navigation actions. Swipe to open is disabled on iOS browsers by default.\n   * @default typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent)\n   */\n  disableSwipeToOpen: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Affects how far the drawer must be opened/closed to change its state.\n   * Specified as percent (0-1) of the width of the drawer\n   * @default 0.52\n   */\n  hysteresis: PropTypes.number,\n  /**\n   * Defines, from which (average) velocity on, the swipe is\n   * defined as complete although hysteresis isn't reached.\n   * Good threshold is between 250 - 1000 px/s\n   * @default 450\n   */\n  minFlingVelocity: PropTypes.number,\n  /**\n   * @ignore\n   */\n  ModalProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    BackdropProps: PropTypes.shape({\n      component: elementTypeAcceptingRef\n    })\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onClose: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the component requests to be opened.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onOpen: PropTypes.func.isRequired,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef,\n    style: PropTypes.object\n  }),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    docked: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    swipeArea: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    docked: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    swipeArea: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The element is used to intercept the touch events on the edge.\n   * @deprecated use the `slotProps.swipeArea` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  SwipeAreaProps: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` that\n   * the drawer can be swiped open from.\n   * @default 20\n   */\n  swipeAreaWidth: PropTypes.number,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * @ignore\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default SwipeableDrawer;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,uBAAuB,MAAM,oCAAoC;AACxE,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,MAAM,IAAIC,SAAS,EAAEC,YAAY,QAAQ,qBAAqB;AACrE,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,qBAAqB,GAAG,CAAC,CAAC,CAAC;;AAEjC;AACA,MAAMC,mBAAmB,GAAG,EAAE,CAAC,CAAC;;AAEhC;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,IAAI;;AAE/B;AACA,OAAO,SAASC,KAAKA,CAAA,EAAG;EACtBD,oBAAoB,GAAG,IAAI;AAC7B;AACA,SAASE,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAE;EAC/C,OAAOF,MAAM,KAAK,OAAO,GAAGE,GAAG,CAACC,IAAI,CAACC,WAAW,GAAGH,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK,GAAGJ,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK;AACxF;AACA,SAASC,iBAAiBA,CAACN,MAAM,EAAEC,OAAO,EAAEM,eAAe,EAAE;EAC3D,OAAOP,MAAM,KAAK,QAAQ,GAAGO,eAAe,CAACC,WAAW,GAAGP,OAAO,CAAC,CAAC,CAAC,CAACQ,OAAO,GAAGR,OAAO,CAAC,CAAC,CAAC,CAACQ,OAAO;AACpG;AACA,SAASC,eAAeA,CAACC,eAAe,EAAEC,aAAa,EAAE;EACvD,OAAOD,eAAe,GAAGC,aAAa,CAACC,WAAW,GAAGD,aAAa,CAACE,YAAY;AACjF;AACA,SAASC,YAAYA,CAACC,gBAAgB,EAAEC,aAAa,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACzE,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACJ,IAAI,GAAGD,aAAa,GAAGD,gBAAgB,GAAGG,YAAY,GAAGF,aAAa,GAAGD,gBAAgB,EAAE,CAAC,CAAC,EAAEG,YAAY,CAAC;AACvI;;AAEA;AACA;AACA;AACA;AACA,SAASI,gBAAgBA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAC3C;EACA,MAAMC,aAAa,GAAG,EAAE;EACxB,OAAOF,OAAO,IAAIA,OAAO,KAAKC,QAAQ,CAACE,aAAa,EAAE;IACpD,MAAMC,KAAK,GAAG9C,WAAW,CAAC2C,QAAQ,CAAC,CAACI,gBAAgB,CAACL,OAAO,CAAC;IAC7D;IACA;IACAI,KAAK,CAACE,gBAAgB,CAAC,UAAU,CAAC,KAAK,UAAU;IACjD;IACAF,KAAK,CAACE,gBAAgB,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE;MACjD;IAAA,CACD,MAAM,IAAIN,OAAO,CAACX,WAAW,GAAG,CAAC,IAAIW,OAAO,CAACO,WAAW,GAAGP,OAAO,CAACX,WAAW,IAAIW,OAAO,CAACV,YAAY,GAAG,CAAC,IAAIU,OAAO,CAACQ,YAAY,GAAGR,OAAO,CAACV,YAAY,EAAE;MAC1J;MACA;MACAY,aAAa,CAACO,IAAI,CAACT,OAAO,CAAC;IAC7B;IACAA,OAAO,GAAGA,OAAO,CAACG,aAAa;EACjC;EACA,OAAOD,aAAa;AACtB;;AAEA;AACA;AACA;AACA;AACA,SAASQ,uBAAuBA,CAAAC,IAAA,EAK7B;EAAA,IAL8B;IAC/BT,aAAa;IACbU,KAAK;IACLC,OAAO;IACPrC;EACF,CAAC,GAAAmC,IAAA;EACC;EACA,MAAMG,cAAc,GAAG;IACrBC,cAAc,EAAE;MACdC,CAAC,EAAE,YAAY;MACfC,CAAC,EAAE;IACL,CAAC;IACDC,YAAY,EAAE;MACZF,CAAC,EAAE,aAAa;MAChBC,CAAC,EAAE;IACL,CAAC;IACDE,YAAY,EAAE;MACZH,CAAC,EAAE,aAAa;MAChBC,CAAC,EAAE;IACL;EACF,CAAC;EACD,OAAOf,aAAa,CAACkB,IAAI,CAACC,KAAK,IAAI;IACjC;IACA,IAAIC,YAAY,GAAGT,OAAO,IAAID,KAAK;IACnC,IAAIpC,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,EAAE;MACzC8C,YAAY,GAAG,CAACA,YAAY;IAC9B;IACA,MAAMC,IAAI,GAAG/C,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;IAChE,MAAMuC,cAAc,GAAGnB,IAAI,CAAC4B,KAAK,CAACH,KAAK,CAACP,cAAc,CAACC,cAAc,CAACQ,IAAI,CAAC,CAAC,CAAC;IAC7E,MAAME,aAAa,GAAGV,cAAc,GAAG,CAAC;IACxC,MAAMW,WAAW,GAAGX,cAAc,GAAGM,KAAK,CAACP,cAAc,CAACK,YAAY,CAACI,IAAI,CAAC,CAAC,GAAGF,KAAK,CAACP,cAAc,CAACI,YAAY,CAACK,IAAI,CAAC,CAAC;IACxH,IAAID,YAAY,IAAII,WAAW,IAAI,CAACJ,YAAY,IAAIG,aAAa,EAAE;MACjE,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC;AACJ;AACA,MAAME,GAAG,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAI,kBAAkB,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;AAC5F,MAAMC,eAAe,GAAG,aAAanF,KAAK,CAACoF,UAAU,CAAC,SAASD,eAAeA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3F,MAAMC,KAAK,GAAGzE,eAAe,CAAC;IAC5B0E,IAAI,EAAE,oBAAoB;IAC1BD,KAAK,EAAEF;EACT,CAAC,CAAC;EACF,MAAMI,KAAK,GAAG5E,QAAQ,CAAC,CAAC;EACxB,MAAM6E,yBAAyB,GAAG;IAChCC,KAAK,EAAEF,KAAK,CAACG,WAAW,CAACC,QAAQ,CAACC,cAAc;IAChDC,IAAI,EAAEN,KAAK,CAACG,WAAW,CAACC,QAAQ,CAACG;EACnC,CAAC;EACD,MAAM;IACJpE,MAAM,GAAG,MAAM;IACfqE,yBAAyB,GAAG,KAAK;IACjCC,gBAAgB,GAAG,KAAK;IACxBC,kBAAkB,GAAGpB,GAAG;IACxBqB,YAAY;IACZC,UAAU,GAAG,IAAI;IACjBC,oBAAoB,GAAG,KAAK;IAC5BC,gBAAgB,GAAG,GAAG;IACtBC,UAAU,EAAE;MACVC,aAAa;MACb,GAAGC;IACL,CAAC,GAAG,CAAC,CAAC;IACNC,OAAO;IACPC,MAAM;IACN9D,IAAI,GAAG,KAAK;IACZ+D,UAAU,GAAG,CAAC,CAAC;IACfC,cAAc;IACdC,cAAc,GAAG,EAAE;IACnBC,kBAAkB,GAAGtB,yBAAyB;IAC9CuB,OAAO,GAAG,WAAW;IACrB;IACAC,KAAK,GAAG,CAAC,CAAC;IACVC,SAAS,GAAG,CAAC,CAAC;IACd,GAAGC;EACL,CAAC,GAAG7B,KAAK;EACT,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAGtH,KAAK,CAACuH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMC,aAAa,GAAGxH,KAAK,CAACyH,MAAM,CAAC;IACjCC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMC,YAAY,GAAG3H,KAAK,CAACyH,MAAM,CAAC,CAAC;EACnC,MAAMG,WAAW,GAAG5H,KAAK,CAACyH,MAAM,CAAC,CAAC;EAClC,MAAMI,QAAQ,GAAG7H,KAAK,CAACyH,MAAM,CAAC,CAAC;EAC/B,MAAMK,SAAS,GAAGtH,UAAU,CAACqG,UAAU,CAACvB,GAAG,EAAEuC,QAAQ,CAAC;EACtD,MAAME,aAAa,GAAG/H,KAAK,CAACyH,MAAM,CAAC,KAAK,CAAC;;EAEzC;EACA,MAAMO,qBAAqB,GAAGhI,KAAK,CAACyH,MAAM,CAAC,CAAC;;EAE5C;EACA7G,iBAAiB,CAAC,MAAM;IACtBoH,qBAAqB,CAAC/D,OAAO,GAAG,IAAI;EACtC,CAAC,EAAE,CAACnB,IAAI,CAAC,CAAC;EACV,MAAMmF,WAAW,GAAGjI,KAAK,CAACkI,WAAW,CAAC,UAACC,SAAS,EAAmB;IAAA,IAAjBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC5D,MAAM;MACJG,IAAI,GAAG,IAAI;MACXC,gBAAgB,GAAG;IACrB,CAAC,GAAGL,OAAO;IACX,MAAMM,SAAS,GAAGpI,SAAS,CAACmF,KAAK,EAAE7D,MAAM,CAAC;IAC1C,MAAM+G,sBAAsB,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACF,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/E,MAAMnG,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;IAC5C,MAAMiH,SAAS,GAAGtG,eAAe,GAAG,aAAaoG,sBAAsB,GAAGR,SAAS,QAAQ,GAAG,gBAAgBQ,sBAAsB,GAAGR,SAAS,KAAK;IACrJ,MAAMW,WAAW,GAAGjB,QAAQ,CAAC5D,OAAO,CAACT,KAAK;IAC1CsF,WAAW,CAACC,eAAe,GAAGF,SAAS;IACvCC,WAAW,CAACD,SAAS,GAAGA,SAAS;IACjC,IAAIG,UAAU,GAAG,EAAE;IACnB,IAAIR,IAAI,EAAE;MACRQ,UAAU,GAAGvD,KAAK,CAACG,WAAW,CAACqD,MAAM,CAAC,KAAK,EAAElI,kBAAkB,CAAC;QAC9DmI,MAAM,EAAEX,SAAS;QACjB/E,KAAK,EAAE+E,SAAS;QAChBY,OAAO,EAAEnC;MACX,CAAC,EAAE;QACDwB;MACF,CAAC,CAAC,CAAC;IACL;IACA,IAAIC,gBAAgB,EAAE;MACpBK,WAAW,CAACM,gBAAgB,GAAGJ,UAAU;MACzCF,WAAW,CAACE,UAAU,GAAGA,UAAU;IACrC;IACA,IAAI,CAAC/C,yBAAyB,IAAI,CAACG,YAAY,EAAE;MAC/C,MAAMiD,aAAa,GAAGzB,WAAW,CAAC3D,OAAO,CAACT,KAAK;MAC/C6F,aAAa,CAACC,OAAO,GAAG,CAAC,GAAGnB,SAAS,GAAG7F,eAAe,CAACC,eAAe,EAAEsF,QAAQ,CAAC5D,OAAO,CAAC;MAC1F,IAAIwE,gBAAgB,EAAE;QACpBY,aAAa,CAACD,gBAAgB,GAAGJ,UAAU;QAC3CK,aAAa,CAACL,UAAU,GAAGA,UAAU;MACvC;IACF;EACF,CAAC,EAAE,CAACpH,MAAM,EAAEqE,yBAAyB,EAAEG,YAAY,EAAEX,KAAK,EAAEuB,kBAAkB,CAAC,CAAC;EAChF,MAAMuC,kBAAkB,GAAG5I,gBAAgB,CAAC6I,WAAW,IAAI;IACzD,IAAI,CAACzB,aAAa,CAAC9D,OAAO,EAAE;MAC1B;IACF;IACA;IACAxC,oBAAoB,GAAG,IAAI;IAC3BsG,aAAa,CAAC9D,OAAO,GAAG,KAAK;IAC7BhE,QAAQ,CAACwJ,SAAS,CAAC,MAAM;MACvBnC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACE,aAAa,CAACvD,OAAO,CAACyD,SAAS,EAAE;MACpCF,aAAa,CAACvD,OAAO,CAACyD,SAAS,GAAG,IAAI;MACtC;IACF;IACAF,aAAa,CAACvD,OAAO,CAACyD,SAAS,GAAG,IAAI;IACtC,MAAMgB,SAAS,GAAGpI,SAAS,CAACmF,KAAK,EAAE7D,MAAM,CAAC;IAC1C,MAAM8H,UAAU,GAAGnJ,YAAY,CAACqB,MAAM,CAAC;IACvC,IAAIqC,OAAO;IACX,IAAIyF,UAAU,EAAE;MACdzF,OAAO,GAAGtC,iBAAiB,CAAC+G,SAAS,EAAEc,WAAW,CAACG,cAAc,EAAElJ,aAAa,CAAC+I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC9G,CAAC,MAAM;MACL3F,OAAO,GAAG/B,iBAAiB,CAACwG,SAAS,EAAEc,WAAW,CAACG,cAAc,EAAEjJ,WAAW,CAAC8I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G;IACA,MAAM/G,aAAa,GAAG6G,UAAU,GAAGlC,aAAa,CAACvD,OAAO,CAAC4F,MAAM,GAAGrC,aAAa,CAACvD,OAAO,CAAC6F,MAAM;IAC9F,MAAM/G,YAAY,GAAGT,eAAe,CAACoH,UAAU,EAAE7B,QAAQ,CAAC5D,OAAO,CAAC;IAClE,MAAMrB,gBAAgB,GAAGD,YAAY,CAACsB,OAAO,EAAEpB,aAAa,EAAEC,IAAI,EAAEC,YAAY,CAAC;IACjF,MAAMgH,cAAc,GAAGnH,gBAAgB,GAAGG,YAAY;IACtD,IAAIC,IAAI,CAACgH,GAAG,CAACxC,aAAa,CAACvD,OAAO,CAACgG,QAAQ,CAAC,GAAG1D,gBAAgB,EAAE;MAC/D;MACAyB,qBAAqB,CAAC/D,OAAO,GAAGjB,IAAI,CAACgH,GAAG,CAAC,CAACjH,YAAY,GAAGH,gBAAgB,IAAI4E,aAAa,CAACvD,OAAO,CAACgG,QAAQ,CAAC,GAAG,IAAI;IACrH;IACA,IAAInH,IAAI,EAAE;MACR,IAAI0E,aAAa,CAACvD,OAAO,CAACgG,QAAQ,GAAG1D,gBAAgB,IAAIwD,cAAc,GAAG1D,UAAU,EAAE;QACpFM,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACL;QACAsB,WAAW,CAAC,CAAC,EAAE;UACbO,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA;IACF;IACA,IAAIhB,aAAa,CAACvD,OAAO,CAACgG,QAAQ,GAAG,CAAC1D,gBAAgB,IAAI,CAAC,GAAGwD,cAAc,GAAG1D,UAAU,EAAE;MACzFO,MAAM,CAAC,CAAC;IACV,CAAC,MAAM;MACL;MACAqB,WAAW,CAAC3F,eAAe,CAACoH,UAAU,EAAE7B,QAAQ,CAAC5D,OAAO,CAAC,EAAE;QACzDuE,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAM0B,iBAAiB,GAAG,SAAAA,CAAA,EAAmB;IAAA,IAAlBC,KAAK,GAAA9B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACtC,IAAI,CAAChB,YAAY,EAAE;MACjB;MACA;MACA;MACA,IAAI8C,KAAK,IAAI,EAAEjE,gBAAgB,IAAII,oBAAoB,CAAC,EAAE;QACxDrG,QAAQ,CAACwJ,SAAS,CAAC,MAAM;UACvBnC,eAAe,CAAC,IAAI,CAAC;QACvB,CAAC,CAAC;MACJ;MACA,MAAM/E,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;MAC5C,IAAI,CAACkB,IAAI,IAAI+E,QAAQ,CAAC5D,OAAO,EAAE;QAC7B;QACAgE,WAAW,CAAC3F,eAAe,CAACC,eAAe,EAAEsF,QAAQ,CAAC5D,OAAO,CAAC,IAAIiC,gBAAgB,GAAG,EAAE,GAAG,CAAC1E,mBAAmB,CAAC,EAAE;UAC/GiH,gBAAgB,EAAE;QACpB,CAAC,CAAC;MACJ;MACAjB,aAAa,CAACvD,OAAO,CAACgG,QAAQ,GAAG,CAAC;MAClCzC,aAAa,CAACvD,OAAO,CAACmG,QAAQ,GAAG,IAAI;MACrC5C,aAAa,CAACvD,OAAO,CAACoG,aAAa,GAAG,IAAI;MAC1C7C,aAAa,CAACvD,OAAO,CAACqG,QAAQ,GAAG,KAAK;MACtCvC,aAAa,CAAC9D,OAAO,GAAG,IAAI;IAC9B;EACF,CAAC;EACD,MAAMsG,mBAAmB,GAAG5J,gBAAgB,CAAC6I,WAAW,IAAI;IAC1D;IACA,IAAI,CAAC3B,QAAQ,CAAC5D,OAAO,IAAI,CAAC8D,aAAa,CAAC9D,OAAO,EAAE;MAC/C;IACF;;IAEA;IACA,IAAIxC,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK+F,aAAa,CAACvD,OAAO,EAAE;MACnF;IACF;IACAiG,iBAAiB,CAAC,IAAI,CAAC;IACvB,MAAMxB,SAAS,GAAGpI,SAAS,CAACmF,KAAK,EAAE7D,MAAM,CAAC;IAC1C,MAAMW,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;IAC5C,MAAM4I,QAAQ,GAAG7I,iBAAiB,CAAC+G,SAAS,EAAEc,WAAW,CAAC3H,OAAO,EAAEpB,aAAa,CAAC+I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G,MAAMa,QAAQ,GAAGvI,iBAAiB,CAACwG,SAAS,EAAEc,WAAW,CAAC3H,OAAO,EAAEnB,WAAW,CAAC8I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC1G,IAAI9G,IAAI,IAAI+E,QAAQ,CAAC5D,OAAO,CAACyG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,IAAIlJ,oBAAoB,KAAK,IAAI,EAAE;MAC1F,MAAM6B,aAAa,GAAGH,gBAAgB,CAACqG,WAAW,CAACmB,MAAM,EAAE9C,QAAQ,CAAC5D,OAAO,CAAC;MAC5E,MAAM2G,gBAAgB,GAAG9G,uBAAuB,CAAC;QAC/CR,aAAa;QACbU,KAAK,EAAEzB,eAAe,GAAGiF,aAAa,CAACvD,OAAO,CAAC4F,MAAM,GAAGrC,aAAa,CAACvD,OAAO,CAAC6F,MAAM;QACpF7F,OAAO,EAAE1B,eAAe,GAAGiI,QAAQ,GAAGC,QAAQ;QAC9C7I;MACF,CAAC,CAAC;MACF,IAAIgJ,gBAAgB,EAAE;QACpBnJ,oBAAoB,GAAG,IAAI;QAC3B;MACF;MACAA,oBAAoB,GAAG+F,aAAa,CAACvD,OAAO;IAC9C;;IAEA;IACA,IAAIuD,aAAa,CAACvD,OAAO,CAACyD,SAAS,IAAI,IAAI,EAAE;MAC3C,MAAMmD,EAAE,GAAG7H,IAAI,CAACgH,GAAG,CAACQ,QAAQ,GAAGhD,aAAa,CAACvD,OAAO,CAAC4F,MAAM,CAAC;MAC5D,MAAMiB,EAAE,GAAG9H,IAAI,CAACgH,GAAG,CAACS,QAAQ,GAAGjD,aAAa,CAACvD,OAAO,CAAC6F,MAAM,CAAC;MAC5D,MAAMiB,iBAAiB,GAAGxI,eAAe,GAAGsI,EAAE,GAAGC,EAAE,IAAID,EAAE,GAAGtJ,qBAAqB,GAAGuJ,EAAE,GAAGD,EAAE,IAAIC,EAAE,GAAGvJ,qBAAqB;MACzH,IAAIwJ,iBAAiB,IAAIvB,WAAW,CAACwB,UAAU,EAAE;QAC/CxB,WAAW,CAACyB,cAAc,CAAC,CAAC;MAC9B;MACA,IAAIF,iBAAiB,KAAK,IAAI,KAAKxI,eAAe,GAAGuI,EAAE,GAAGvJ,qBAAqB,GAAGsJ,EAAE,GAAGtJ,qBAAqB,CAAC,EAAE;QAC7GiG,aAAa,CAACvD,OAAO,CAACyD,SAAS,GAAGqD,iBAAiB;QACnD,IAAI,CAACA,iBAAiB,EAAE;UACtBxB,kBAAkB,CAACC,WAAW,CAAC;UAC/B;QACF;;QAEA;QACAhC,aAAa,CAACvD,OAAO,CAAC4F,MAAM,GAAGW,QAAQ;QACvChD,aAAa,CAACvD,OAAO,CAAC6F,MAAM,GAAGW,QAAQ;;QAEvC;QACA,IAAI,CAACvE,gBAAgB,IAAI,CAACpD,IAAI,EAAE;UAC9B,IAAIP,eAAe,EAAE;YACnBiF,aAAa,CAACvD,OAAO,CAAC4F,MAAM,IAAIrI,mBAAmB;UACrD,CAAC,MAAM;YACLgG,aAAa,CAACvD,OAAO,CAAC6F,MAAM,IAAItI,mBAAmB;UACrD;QACF;MACF;IACF;IACA,IAAI,CAACgG,aAAa,CAACvD,OAAO,CAACyD,SAAS,EAAE;MACpC;IACF;IACA,MAAM3E,YAAY,GAAGT,eAAe,CAACC,eAAe,EAAEsF,QAAQ,CAAC5D,OAAO,CAAC;IACvE,IAAIpB,aAAa,GAAGN,eAAe,GAAGiF,aAAa,CAACvD,OAAO,CAAC4F,MAAM,GAAGrC,aAAa,CAACvD,OAAO,CAAC6F,MAAM;IACjG,IAAIhH,IAAI,IAAI,CAAC0E,aAAa,CAACvD,OAAO,CAACqG,QAAQ,EAAE;MAC3CzH,aAAa,GAAGG,IAAI,CAACC,GAAG,CAACJ,aAAa,EAAEE,YAAY,CAAC;IACvD;IACA,MAAMoF,SAAS,GAAGxF,YAAY,CAACJ,eAAe,GAAGiI,QAAQ,GAAGC,QAAQ,EAAE5H,aAAa,EAAEC,IAAI,EAAEC,YAAY,CAAC;IACxG,IAAID,IAAI,EAAE;MACR,IAAI,CAAC0E,aAAa,CAACvD,OAAO,CAACqG,QAAQ,EAAE;QACnC,MAAMA,QAAQ,GAAG/H,eAAe,GAAGiI,QAAQ,GAAGzH,YAAY,GAAG0H,QAAQ,GAAG1H,YAAY;QACpF,IAAIuH,QAAQ,EAAE;UACZ9C,aAAa,CAACvD,OAAO,CAACqG,QAAQ,GAAG,IAAI;UACrC9C,aAAa,CAACvD,OAAO,CAAC4F,MAAM,GAAGW,QAAQ;UACvChD,aAAa,CAACvD,OAAO,CAAC6F,MAAM,GAAGW,QAAQ;QACzC,CAAC,MAAM;UACL;QACF;MACF,CAAC,MAAM,IAAItC,SAAS,KAAK,CAAC,EAAE;QAC1BX,aAAa,CAACvD,OAAO,CAAC4F,MAAM,GAAGW,QAAQ;QACvChD,aAAa,CAACvD,OAAO,CAAC6F,MAAM,GAAGW,QAAQ;MACzC;IACF;IACA,IAAIjD,aAAa,CAACvD,OAAO,CAACoG,aAAa,KAAK,IAAI,EAAE;MAChD7C,aAAa,CAACvD,OAAO,CAACoG,aAAa,GAAGlC,SAAS;MAC/CX,aAAa,CAACvD,OAAO,CAACmG,QAAQ,GAAGc,WAAW,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;IACxD;IACA,MAAMlB,QAAQ,GAAG,CAAC9B,SAAS,GAAGX,aAAa,CAACvD,OAAO,CAACoG,aAAa,KAAKa,WAAW,CAACC,GAAG,CAAC,CAAC,GAAG3D,aAAa,CAACvD,OAAO,CAACmG,QAAQ,CAAC,GAAG,GAAG;;IAE/H;IACA5C,aAAa,CAACvD,OAAO,CAACgG,QAAQ,GAAGzC,aAAa,CAACvD,OAAO,CAACgG,QAAQ,GAAG,GAAG,GAAGA,QAAQ,GAAG,GAAG;IACtFzC,aAAa,CAACvD,OAAO,CAACoG,aAAa,GAAGlC,SAAS;IAC/CX,aAAa,CAACvD,OAAO,CAACmG,QAAQ,GAAGc,WAAW,CAACC,GAAG,CAAC,CAAC;;IAElD;IACA,IAAI3B,WAAW,CAACwB,UAAU,EAAE;MAC1BxB,WAAW,CAACyB,cAAc,CAAC,CAAC;IAC9B;IACAhD,WAAW,CAACE,SAAS,CAAC;EACxB,CAAC,CAAC;EACF,MAAMiD,oBAAoB,GAAGzK,gBAAgB,CAAC6I,WAAW,IAAI;IAC3D;IACA;IACA,IAAIA,WAAW,CAAC6B,gBAAgB,EAAE;MAChC;IACF;;IAEA;IACA,IAAI7B,WAAW,CAAC8B,mBAAmB,EAAE;MACnC;IACF;;IAEA;IACA,IAAIxI,IAAI,KAAKsD,YAAY,IAAI,CAACwB,WAAW,CAAC3D,OAAO,CAACyG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,CAAC,IAAI,CAAC9C,QAAQ,CAAC5D,OAAO,CAACyG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,EAAE;MACjI;IACF;IACA,MAAMjC,SAAS,GAAGpI,SAAS,CAACmF,KAAK,EAAE7D,MAAM,CAAC;IAC1C,MAAMW,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;IAC5C,MAAM4I,QAAQ,GAAG7I,iBAAiB,CAAC+G,SAAS,EAAEc,WAAW,CAAC3H,OAAO,EAAEpB,aAAa,CAAC+I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G,MAAMa,QAAQ,GAAGvI,iBAAiB,CAACwG,SAAS,EAAEc,WAAW,CAAC3H,OAAO,EAAEnB,WAAW,CAAC8I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC1G,IAAI,CAAC9G,IAAI,EAAE;MACT;MACA;MACA;MACA;MACA,IAAIqD,kBAAkB,IAAI,EAAEqD,WAAW,CAACmB,MAAM,KAAKhD,YAAY,CAAC1D,OAAO,IAAI4D,QAAQ,CAAC5D,OAAO,EAAEyG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,KAAK,OAAOrE,oBAAoB,KAAK,UAAU,GAAGA,oBAAoB,CAACkD,WAAW,EAAE7B,YAAY,CAAC1D,OAAO,EAAE4D,QAAQ,CAAC5D,OAAO,CAAC,GAAGqC,oBAAoB,CAAC,CAAC,EAAE;QAC7Q;MACF;MACA,IAAI/D,eAAe,EAAE;QACnB,IAAIiI,QAAQ,GAAGzD,cAAc,EAAE;UAC7B;QACF;MACF,CAAC,MAAM,IAAI0D,QAAQ,GAAG1D,cAAc,EAAE;QACpC;MACF;IACF;IACAyC,WAAW,CAAC8B,mBAAmB,GAAG,IAAI;IACtC7J,oBAAoB,GAAG,IAAI;IAC3B+F,aAAa,CAACvD,OAAO,CAAC4F,MAAM,GAAGW,QAAQ;IACvChD,aAAa,CAACvD,OAAO,CAAC6F,MAAM,GAAGW,QAAQ;IACvCP,iBAAiB,CAAC,CAAC;EACrB,CAAC,CAAC;EACFlK,KAAK,CAACuL,SAAS,CAAC,MAAM;IACpB,IAAItE,OAAO,KAAK,WAAW,EAAE;MAC3B,MAAMnF,GAAG,GAAGrB,aAAa,CAACoH,QAAQ,CAAC5D,OAAO,CAAC;MAC3CnC,GAAG,CAAC0J,gBAAgB,CAAC,YAAY,EAAEJ,oBAAoB,CAAC;MACxD;MACA;MACA;MACAtJ,GAAG,CAAC0J,gBAAgB,CAAC,WAAW,EAAEjB,mBAAmB,EAAE;QACrDkB,OAAO,EAAE,CAAC3I;MACZ,CAAC,CAAC;MACFhB,GAAG,CAAC0J,gBAAgB,CAAC,UAAU,EAAEjC,kBAAkB,CAAC;MACpD,OAAO,MAAM;QACXzH,GAAG,CAAC4J,mBAAmB,CAAC,YAAY,EAAEN,oBAAoB,CAAC;QAC3DtJ,GAAG,CAAC4J,mBAAmB,CAAC,WAAW,EAAEnB,mBAAmB,EAAE;UACxDkB,OAAO,EAAE,CAAC3I;QACZ,CAAC,CAAC;QACFhB,GAAG,CAAC4J,mBAAmB,CAAC,UAAU,EAAEnC,kBAAkB,CAAC;MACzD,CAAC;IACH;IACA,OAAOhB,SAAS;EAClB,CAAC,EAAE,CAACtB,OAAO,EAAEnE,IAAI,EAAEsI,oBAAoB,EAAEb,mBAAmB,EAAEhB,kBAAkB,CAAC,CAAC;EAClFvJ,KAAK,CAACuL,SAAS,CAAC,MAAM,MAAM;IAC1B;IACA,IAAI9J,oBAAoB,KAAK+F,aAAa,CAACvD,OAAO,EAAE;MAClDxC,oBAAoB,GAAG,IAAI;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EACNzB,KAAK,CAACuL,SAAS,CAAC,MAAM;IACpB,IAAI,CAACzI,IAAI,EAAE;MACTwE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACxE,IAAI,CAAC,CAAC;EACV,MAAM,CAAC6I,aAAa,EAAEC,kBAAkB,CAAC,GAAG3K,OAAO,CAAC,WAAW,EAAE;IAC/DqE,GAAG,EAAEqC,YAAY;IACjBkE,WAAW,EAAE3K,SAAS;IACtB4K,UAAU,EAAEvG,KAAK;IACjBwG,sBAAsB,EAAE;MACtB7E,KAAK;MACLC,SAAS,EAAE;QACT6E,SAAS,EAAElF,cAAc;QACzB,GAAGK;MACL;IACF,CAAC;IACD8E,eAAe,EAAE;MACfC,KAAK,EAAEnF,cAAc;MACrBnF;IACF;EACF,CAAC,CAAC;EACF,OAAO,aAAaN,KAAK,CAACtB,KAAK,CAACmM,QAAQ,EAAE;IACxCC,QAAQ,EAAE,CAAC,aAAahL,IAAI,CAACf,MAAM,EAAE;MACnCyC,IAAI,EAAEmE,OAAO,KAAK,WAAW,IAAII,YAAY,GAAG,IAAI,GAAGvE,IAAI;MAC3DmE,OAAO,EAAEA,OAAO;MAChBT,UAAU,EAAE;QACVC,aAAa,EAAE;UACb,GAAGA,aAAa;UAChBnB,GAAG,EAAEsC;QACP,CAAC;QACD;QACA;QACA,IAAIX,OAAO,KAAK,WAAW,IAAI;UAC7BoF,WAAW,EAAE;QACf,CAAC,CAAC;QACF,GAAG3F;MACL,CAAC;MACDN,YAAY,EAAEA,YAAY;MAC1BxE,MAAM,EAAEA,MAAM;MACdoF,kBAAkB,EAAEgB,qBAAqB,CAAC/D,OAAO,IAAI+C,kBAAkB;MACvEL,OAAO,EAAEA,OAAO;MAChBrB,GAAG,EAAEA,GAAG;MACR4B,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAE;QACT,GAAGA,SAAS;QACZmF,QAAQ,EAAEtL,cAAc,CAACmG,SAAS,CAACmF,QAAQ,IAAI7F,aAAa,EAAE;UAC5DnB,GAAG,EAAEsC;QACP,CAAC,CAAC;QACF2E,KAAK,EAAEvL,cAAc,CAACmG,SAAS,CAACoF,KAAK,IAAI1F,UAAU,EAAE;UACnDrD,KAAK,EAAE;YACLgJ,aAAa,EAAEvF,OAAO,KAAK,WAAW,IAAI,CAACnE,IAAI,IAAI,CAACwD,oBAAoB,GAAG,MAAM,GAAG;UACtF,CAAC;UACDhB,GAAG,EAAEwC;QACP,CAAC;MACH,CAAC;MACD,GAAGV;IACL,CAAC,CAAC,EAAE,CAACjB,kBAAkB,IAAIc,OAAO,KAAK,WAAW,IAAI,aAAa7F,IAAI,CAAChB,KAAK,EAAE;MAC7EgM,QAAQ,EAAE,aAAahL,IAAI,CAACuK,aAAa,EAAE;QACzC,GAAGC;MACL,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxH,eAAe,CAACyH,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtG,oBAAoB,EAAEpG,SAAS,CAAC2M,SAAS,CAAC,CAAC3M,SAAS,CAAC4M,IAAI,EAAE5M,SAAS,CAAC6M,IAAI,CAAC,CAAC;EAC3E;AACF;AACA;EACEnL,MAAM,EAAE1B,SAAS,CAAC8M,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC3D;AACF;AACA;EACEZ,QAAQ,EAAElM,SAAS,CAAC+M,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEhH,yBAAyB,EAAE/F,SAAS,CAAC6M,IAAI;EACzC;AACF;AACA;AACA;AACA;EACE7G,gBAAgB,EAAEhG,SAAS,CAAC6M,IAAI;EAChC;AACF;AACA;AACA;AACA;EACE5G,kBAAkB,EAAEjG,SAAS,CAAC6M,IAAI;EAClC;AACF;AACA;EACE3G,YAAY,EAAElG,SAAS,CAAC6M,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACE1G,UAAU,EAAEnG,SAAS,CAACgN,MAAM;EAC5B;AACF;AACA;AACA;AACA;AACA;EACE3G,gBAAgB,EAAErG,SAAS,CAACgN,MAAM;EAClC;AACF;AACA;EACE1G,UAAU,EAAEtG,SAAS,CAAC,sCAAsCuE,KAAK,CAAC;IAChEgC,aAAa,EAAEvG,SAAS,CAACuE,KAAK,CAAC;MAC7B0I,SAAS,EAAEhN;IACb,CAAC;EACH,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEwG,OAAO,EAAEzG,SAAS,CAAC4M,IAAI,CAACM,UAAU;EAClC;AACF;AACA;AACA;AACA;EACExG,MAAM,EAAE1G,SAAS,CAAC4M,IAAI,CAACM,UAAU;EACjC;AACF;AACA;AACA;EACEtK,IAAI,EAAE5C,SAAS,CAAC6M,IAAI;EACpB;AACF;AACA;EACElG,UAAU,EAAE3G,SAAS,CAAC,sCAAsCuE,KAAK,CAAC;IAChE0I,SAAS,EAAEhN,uBAAuB;IAClCqD,KAAK,EAAEtD,SAAS,CAACmN;EACnB,CAAC,CAAC;EACF;AACF;AACA;AACA;EACElG,SAAS,EAAEjH,SAAS,CAACuE,KAAK,CAAC;IACzB6H,QAAQ,EAAEpM,SAAS,CAAC2M,SAAS,CAAC,CAAC3M,SAAS,CAAC4M,IAAI,EAAE5M,SAAS,CAACmN,MAAM,CAAC,CAAC;IACjEC,MAAM,EAAEpN,SAAS,CAAC2M,SAAS,CAAC,CAAC3M,SAAS,CAAC4M,IAAI,EAAE5M,SAAS,CAACmN,MAAM,CAAC,CAAC;IAC/Dd,KAAK,EAAErM,SAAS,CAAC2M,SAAS,CAAC,CAAC3M,SAAS,CAAC4M,IAAI,EAAE5M,SAAS,CAACmN,MAAM,CAAC,CAAC;IAC9DE,IAAI,EAAErN,SAAS,CAAC2M,SAAS,CAAC,CAAC3M,SAAS,CAAC4M,IAAI,EAAE5M,SAAS,CAACmN,MAAM,CAAC,CAAC;IAC7DrB,SAAS,EAAE9L,SAAS,CAAC2M,SAAS,CAAC,CAAC3M,SAAS,CAAC4M,IAAI,EAAE5M,SAAS,CAACmN,MAAM,CAAC,CAAC;IAClErE,UAAU,EAAE9I,SAAS,CAAC2M,SAAS,CAAC,CAAC3M,SAAS,CAAC4M,IAAI,EAAE5M,SAAS,CAACmN,MAAM,CAAC;EACpE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEnG,KAAK,EAAEhH,SAAS,CAACuE,KAAK,CAAC;IACrB6H,QAAQ,EAAEpM,SAAS,CAAC2L,WAAW;IAC/ByB,MAAM,EAAEpN,SAAS,CAAC2L,WAAW;IAC7BU,KAAK,EAAErM,SAAS,CAAC2L,WAAW;IAC5B0B,IAAI,EAAErN,SAAS,CAAC2L,WAAW;IAC3BG,SAAS,EAAE9L,SAAS,CAAC2L,WAAW;IAChC7C,UAAU,EAAE9I,SAAS,CAAC2L;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE/E,cAAc,EAAE5G,SAAS,CAACmN,MAAM;EAChC;AACF;AACA;AACA;AACA;EACEtG,cAAc,EAAE7G,SAAS,CAACgN,MAAM;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACElG,kBAAkB,EAAE9G,SAAS,CAAC2M,SAAS,CAAC,CAAC3M,SAAS,CAACgN,MAAM,EAAEhN,SAAS,CAACuE,KAAK,CAAC;IACzE+I,MAAM,EAAEtN,SAAS,CAACgN,MAAM;IACxBvH,KAAK,EAAEzF,SAAS,CAACgN,MAAM;IACvBnH,IAAI,EAAE7F,SAAS,CAACgN;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;EACEjG,OAAO,EAAE/G,SAAS,CAAC8M,KAAK,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;AACnE,CAAC,GAAG,KAAK,CAAC;AACV,eAAe7H,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}