{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Tabs,Tab,Paper,Alert,Snackbar}from'@mui/material';import{ShoppingCart,History}from'@mui/icons-material';import CreditBalance from'../../components/credit/CreditBalance';import CreditPackages from'../../components/credit/CreditPackages';import TransactionHistory from'../../components/credit/TransactionHistory';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function TabPanel(props){const{children,value,index,...other}=props;return/*#__PURE__*/_jsx(\"div\",{role:\"tabpanel\",hidden:value!==index,id:`credit-tabpanel-${index}`,\"aria-labelledby\":`credit-tab-${index}`,...other,children:value===index&&/*#__PURE__*/_jsx(Box,{sx:{py:3},children:children})});}function a11yProps(index){return{id:`credit-tab-${index}`,'aria-controls':`credit-tabpanel-${index}`};}const Credit=()=>{const[tabValue,setTabValue]=useState(0);const[refreshTrigger,setRefreshTrigger]=useState(0);const[notification,setNotification]=useState({open:false,message:'',severity:'info'});// Check for payment status in URL parameters\nuseEffect(()=>{const urlParams=new URLSearchParams(window.location.search);const billplzId=urlParams.get('billplz[id]');const billplzPaid=urlParams.get('billplz[paid]');const billplzState=urlParams.get('billplz[state]');if(billplzId){if(billplzState==='paid'||billplzPaid==='true'){setNotification({open:true,message:'Payment successful! Your credits have been added to your account.',severity:'success'});setRefreshTrigger(prev=>prev+1);}else{setNotification({open:true,message:'Payment was not completed. Please try again or contact support.',severity:'warning'});}// Clean up URL parameters\nconst newUrl=window.location.pathname;window.history.replaceState({},document.title,newUrl);}},[]);const handleTabChange=(event,newValue)=>{setTabValue(newValue);};const handlePurchaseSuccess=()=>{setRefreshTrigger(prev=>prev+1);setNotification({open:true,message:'Purchase initiated! You will be redirected to complete payment.',severity:'info'});};const handleCloseNotification=()=>{setNotification(prev=>({...prev,open:false}));};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:\"Credit Management\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",paragraph:true,children:\"Manage your credit balance, purchase credit packages, and view your transaction history.\"}),/*#__PURE__*/_jsx(Box,{mb:4,children:/*#__PURE__*/_jsx(CreditBalance,{refreshTrigger:refreshTrigger})}),/*#__PURE__*/_jsxs(Paper,{sx:{width:'100%'},children:[/*#__PURE__*/_jsx(Box,{sx:{borderBottom:1,borderColor:'divider'},children:/*#__PURE__*/_jsxs(Tabs,{value:tabValue,onChange:handleTabChange,\"aria-label\":\"credit management tabs\",variant:\"fullWidth\",children:[/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(ShoppingCart,{}),label:\"Purchase Credits\",...a11yProps(0)}),/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(History,{}),label:\"Transaction History\",...a11yProps(1)})]})}),/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:0,children:/*#__PURE__*/_jsx(CreditPackages,{onPurchaseSuccess:handlePurchaseSuccess})}),/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:1,children:/*#__PURE__*/_jsx(TransactionHistory,{refreshTrigger:refreshTrigger})})]}),/*#__PURE__*/_jsx(Snackbar,{open:notification.open,autoHideDuration:6000,onClose:handleCloseNotification,anchorOrigin:{vertical:'bottom',horizontal:'right'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleCloseNotification,severity:notification.severity,sx:{width:'100%'},children:notification.message})})]});};export default Credit;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Tabs", "Tab", "Paper", "<PERSON><PERSON>", "Snackbar", "ShoppingCart", "History", "CreditBalance", "CreditPackages", "TransactionHistory", "jsx", "_jsx", "jsxs", "_jsxs", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "py", "a11yProps", "Credit", "tabValue", "setTabValue", "refreshTrigger", "setRefreshTrigger", "notification", "setNotification", "open", "message", "severity", "urlParams", "URLSearchParams", "window", "location", "search", "billplzId", "get", "billplzPaid", "billplzState", "prev", "newUrl", "pathname", "history", "replaceState", "document", "title", "handleTabChange", "event", "newValue", "handlePurchaseSuccess", "handleCloseNotification", "variant", "component", "gutterBottom", "color", "paragraph", "mb", "width", "borderBottom", "borderColor", "onChange", "icon", "label", "onPurchaseSuccess", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal"], "sources": ["C:/laragon/www/frontend/src/pages/dashboard/Credit.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Tabs,\n  Tab,\n  Paper,\n  Alert,\n  Snackbar,\n} from '@mui/material';\nimport {\n  AccountBalanceWallet,\n  ShoppingCart,\n  History,\n} from '@mui/icons-material';\nimport CreditBalance from '../../components/credit/CreditBalance';\nimport CreditPackages from '../../components/credit/CreditPackages';\nimport TransactionHistory from '../../components/credit/TransactionHistory';\n\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nfunction TabPanel(props: TabPanelProps) {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`credit-tabpanel-${index}`}\n      aria-labelledby={`credit-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nfunction a11yProps(index: number) {\n  return {\n    id: `credit-tab-${index}`,\n    'aria-controls': `credit-tabpanel-${index}`,\n  };\n}\n\nconst Credit: React.FC = () => {\n  const [tabValue, setTabValue] = useState(0);\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n  const [notification, setNotification] = useState<{\n    open: boolean;\n    message: string;\n    severity: 'success' | 'error' | 'warning' | 'info';\n  }>({\n    open: false,\n    message: '',\n    severity: 'info',\n  });\n\n  // Check for payment status in URL parameters\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const billplzId = urlParams.get('billplz[id]');\n    const billplzPaid = urlParams.get('billplz[paid]');\n    const billplzState = urlParams.get('billplz[state]');\n\n    if (billplzId) {\n      if (billplzState === 'paid' || billplzPaid === 'true') {\n        setNotification({\n          open: true,\n          message: 'Payment successful! Your credits have been added to your account.',\n          severity: 'success',\n        });\n        setRefreshTrigger(prev => prev + 1);\n      } else {\n        setNotification({\n          open: true,\n          message: 'Payment was not completed. Please try again or contact support.',\n          severity: 'warning',\n        });\n      }\n\n      // Clean up URL parameters\n      const newUrl = window.location.pathname;\n      window.history.replaceState({}, document.title, newUrl);\n    }\n  }, []);\n\n  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\n    setTabValue(newValue);\n  };\n\n  const handlePurchaseSuccess = () => {\n    setRefreshTrigger(prev => prev + 1);\n    setNotification({\n      open: true,\n      message: 'Purchase initiated! You will be redirected to complete payment.',\n      severity: 'info',\n    });\n  };\n\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n        Credit Management\n      </Typography>\n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\n        Manage your credit balance, purchase credit packages, and view your transaction history.\n      </Typography>\n\n      {/* Credit Balance Overview */}\n      <Box mb={4}>\n        <CreditBalance refreshTrigger={refreshTrigger} />\n      </Box>\n\n      {/* Tabs */}\n      <Paper sx={{ width: '100%' }}>\n        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n          <Tabs\n            value={tabValue}\n            onChange={handleTabChange}\n            aria-label=\"credit management tabs\"\n            variant=\"fullWidth\"\n          >\n            <Tab\n              icon={<ShoppingCart />}\n              label=\"Purchase Credits\"\n              {...a11yProps(0)}\n            />\n            <Tab\n              icon={<History />}\n              label=\"Transaction History\"\n              {...a11yProps(1)}\n            />\n          </Tabs>\n        </Box>\n\n        <TabPanel value={tabValue} index={0}>\n          <CreditPackages onPurchaseSuccess={handlePurchaseSuccess} />\n        </TabPanel>\n\n        <TabPanel value={tabValue} index={1}>\n          <TransactionHistory refreshTrigger={refreshTrigger} />\n        </TabPanel>\n      </Paper>\n\n      {/* Notification Snackbar */}\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert\n          onClose={handleCloseNotification}\n          severity={notification.severity}\n          sx={{ width: '100%' }}\n        >\n          {notification.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default Credit;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,IAAI,CACJC,GAAG,CACHC,KAAK,CACLC,KAAK,CACLC,QAAQ,KACH,eAAe,CACtB,OAEEC,YAAY,CACZC,OAAO,KACF,qBAAqB,CAC5B,MAAO,CAAAC,aAAa,KAAM,uCAAuC,CACjE,MAAO,CAAAC,cAAc,KAAM,wCAAwC,CACnE,MAAO,CAAAC,kBAAkB,KAAM,4CAA4C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQ5E,QAAS,CAAAC,QAAQA,CAACC,KAAoB,CAAE,CACtC,KAAM,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAE,GAAGC,KAAM,CAAC,CAAGJ,KAAK,CAElD,mBACEJ,IAAA,QACES,IAAI,CAAC,UAAU,CACfC,MAAM,CAAEJ,KAAK,GAAKC,KAAM,CACxBI,EAAE,CAAE,mBAAmBJ,KAAK,EAAG,CAC/B,kBAAiB,cAAcA,KAAK,EAAG,IACnCC,KAAK,CAAAH,QAAA,CAERC,KAAK,GAAKC,KAAK,eAAIP,IAAA,CAACb,GAAG,EAACyB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,CAAEA,QAAQ,CAAM,CAAC,CACrD,CAAC,CAEV,CAEA,QAAS,CAAAS,SAASA,CAACP,KAAa,CAAE,CAChC,MAAO,CACLI,EAAE,CAAE,cAAcJ,KAAK,EAAE,CACzB,eAAe,CAAE,mBAAmBA,KAAK,EAC3C,CAAC,CACH,CAEA,KAAM,CAAAQ,MAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGhC,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACiC,cAAc,CAAEC,iBAAiB,CAAC,CAAGlC,QAAQ,CAAC,CAAC,CAAC,CACvD,KAAM,CAACmC,YAAY,CAAEC,eAAe,CAAC,CAAGpC,QAAQ,CAI7C,CACDqC,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,MACZ,CAAC,CAAC,CAEF;AACAtC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuC,SAAS,CAAG,GAAI,CAAAC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAC7D,KAAM,CAAAC,SAAS,CAAGL,SAAS,CAACM,GAAG,CAAC,aAAa,CAAC,CAC9C,KAAM,CAAAC,WAAW,CAAGP,SAAS,CAACM,GAAG,CAAC,eAAe,CAAC,CAClD,KAAM,CAAAE,YAAY,CAAGR,SAAS,CAACM,GAAG,CAAC,gBAAgB,CAAC,CAEpD,GAAID,SAAS,CAAE,CACb,GAAIG,YAAY,GAAK,MAAM,EAAID,WAAW,GAAK,MAAM,CAAE,CACrDX,eAAe,CAAC,CACdC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,mEAAmE,CAC5EC,QAAQ,CAAE,SACZ,CAAC,CAAC,CACFL,iBAAiB,CAACe,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CACrC,CAAC,IAAM,CACLb,eAAe,CAAC,CACdC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,iEAAiE,CAC1EC,QAAQ,CAAE,SACZ,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAW,MAAM,CAAGR,MAAM,CAACC,QAAQ,CAACQ,QAAQ,CACvCT,MAAM,CAACU,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,CAAEC,QAAQ,CAACC,KAAK,CAAEL,MAAM,CAAC,CACzD,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAM,eAAe,CAAGA,CAACC,KAA2B,CAAEC,QAAgB,GAAK,CACzE1B,WAAW,CAAC0B,QAAQ,CAAC,CACvB,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,CAClCzB,iBAAiB,CAACe,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CACnCb,eAAe,CAAC,CACdC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,iEAAiE,CAC1EC,QAAQ,CAAE,MACZ,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAqB,uBAAuB,CAAGA,CAAA,GAAM,CACpCxB,eAAe,CAACa,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEZ,IAAI,CAAE,KAAM,CAAC,CAAC,CAAC,CACrD,CAAC,CAED,mBACEpB,KAAA,CAACf,GAAG,EAAAkB,QAAA,eACFL,IAAA,CAACZ,UAAU,EAAC0D,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAA3C,QAAA,CAAC,mBAErD,CAAY,CAAC,cACbL,IAAA,CAACZ,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAACG,KAAK,CAAC,gBAAgB,CAACC,SAAS,MAAA7C,QAAA,CAAC,0FAE7D,CAAY,CAAC,cAGbL,IAAA,CAACb,GAAG,EAACgE,EAAE,CAAE,CAAE,CAAA9C,QAAA,cACTL,IAAA,CAACJ,aAAa,EAACsB,cAAc,CAAEA,cAAe,CAAE,CAAC,CAC9C,CAAC,cAGNhB,KAAA,CAACX,KAAK,EAACqB,EAAE,CAAE,CAAEwC,KAAK,CAAE,MAAO,CAAE,CAAA/C,QAAA,eAC3BL,IAAA,CAACb,GAAG,EAACyB,EAAE,CAAE,CAAEyC,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAU,CAAE,CAAAjD,QAAA,cACnDH,KAAA,CAACb,IAAI,EACHiB,KAAK,CAAEU,QAAS,CAChBuC,QAAQ,CAAEd,eAAgB,CAC1B,aAAW,wBAAwB,CACnCK,OAAO,CAAC,WAAW,CAAAzC,QAAA,eAEnBL,IAAA,CAACV,GAAG,EACFkE,IAAI,cAAExD,IAAA,CAACN,YAAY,GAAE,CAAE,CACvB+D,KAAK,CAAC,kBAAkB,IACpB3C,SAAS,CAAC,CAAC,CAAC,CACjB,CAAC,cACFd,IAAA,CAACV,GAAG,EACFkE,IAAI,cAAExD,IAAA,CAACL,OAAO,GAAE,CAAE,CAClB8D,KAAK,CAAC,qBAAqB,IACvB3C,SAAS,CAAC,CAAC,CAAC,CACjB,CAAC,EACE,CAAC,CACJ,CAAC,cAENd,IAAA,CAACG,QAAQ,EAACG,KAAK,CAAEU,QAAS,CAACT,KAAK,CAAE,CAAE,CAAAF,QAAA,cAClCL,IAAA,CAACH,cAAc,EAAC6D,iBAAiB,CAAEd,qBAAsB,CAAE,CAAC,CACpD,CAAC,cAEX5C,IAAA,CAACG,QAAQ,EAACG,KAAK,CAAEU,QAAS,CAACT,KAAK,CAAE,CAAE,CAAAF,QAAA,cAClCL,IAAA,CAACF,kBAAkB,EAACoB,cAAc,CAAEA,cAAe,CAAE,CAAC,CAC9C,CAAC,EACN,CAAC,cAGRlB,IAAA,CAACP,QAAQ,EACP6B,IAAI,CAAEF,YAAY,CAACE,IAAK,CACxBqC,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAEf,uBAAwB,CACjCgB,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAE,CAAA1D,QAAA,cAE1DL,IAAA,CAACR,KAAK,EACJoE,OAAO,CAAEf,uBAAwB,CACjCrB,QAAQ,CAAEJ,YAAY,CAACI,QAAS,CAChCZ,EAAE,CAAE,CAAEwC,KAAK,CAAE,MAAO,CAAE,CAAA/C,QAAA,CAErBe,YAAY,CAACG,OAAO,CAChB,CAAC,CACA,CAAC,EACR,CAAC,CAEV,CAAC,CAED,cAAe,CAAAR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}