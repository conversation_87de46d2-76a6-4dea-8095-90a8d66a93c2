{"ast": null, "code": "import React from'react';import{Card,CardContent,Typography,Box,Paper,Avatar}from'@mui/material';import{TrendingUp,People,ShoppingCart,AttachMoney}from'@mui/icons-material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StatCard=_ref=>{let{title,value,icon,color,change}=_ref;return/*#__PURE__*/_jsx(Card,{sx:{height:'100%'},children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{color:\"textSecondary\",gutterBottom:true,variant:\"overline\",children:title}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"div\",children:value}),change&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:'success.main',mt:1},children:change})]}),/*#__PURE__*/_jsx(Avatar,{sx:{bgcolor:color,width:56,height:56},children:icon})]})})});};const Dashboard=()=>{const stats=[{title:'Total Users',value:'1,234',icon:/*#__PURE__*/_jsx(People,{}),color:'#1976d2',change:'+12% from last month'},{title:'Revenue',value:'$45,678',icon:/*#__PURE__*/_jsx(AttachMoney,{}),color:'#4caf50',change:'+8% from last month'},{title:'Orders',value:'567',icon:/*#__PURE__*/_jsx(ShoppingCart,{}),color:'#ff9800',change:'+15% from last month'},{title:'Growth',value:'23.5%',icon:/*#__PURE__*/_jsx(TrendingUp,{}),color:'#9c27b0',change:'+3% from last month'}];return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:\"Dashboard Overview\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"textSecondary\",sx:{mb:2},children:\"Welcome to your Material Dashboard! Here's an overview of your key metrics.\"}),/*#__PURE__*/_jsx(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'1fr',sm:'1fr 1fr',md:'1fr 1fr 1fr 1fr'},gap:3},children:stats.map((stat,index)=>/*#__PURE__*/_jsx(StatCard,{...stat},index))}),/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'1fr',md:'2fr 1fr'},gap:3,mt:3},children:[/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Recent Activity\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"This section would typically contain charts, graphs, or recent activity feeds. You can integrate libraries like Chart.js, Recharts, or MUI X Charts here.\"})]}),/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Quick Actions\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"Add quick action buttons or widgets here for common tasks.\"})]})]})]});};export default Dashboard;", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Box", "Paper", "Avatar", "TrendingUp", "People", "ShoppingCart", "AttachMoney", "jsx", "_jsx", "jsxs", "_jsxs", "StatCard", "_ref", "title", "value", "icon", "color", "change", "sx", "height", "children", "display", "alignItems", "justifyContent", "gutterBottom", "variant", "component", "mt", "bgcolor", "width", "Dashboard", "stats", "mb", "gridTemplateColumns", "xs", "sm", "md", "gap", "map", "stat", "index", "p"], "sources": ["C:/laragon/www/frontend/src/pages/dashboard/Dashboard.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Card,\n  CardContent,\n  Typography,\n  Box,\n  Paper,\n  Avatar,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  People,\n  ShoppingCart,\n  AttachMoney,\n} from '@mui/icons-material';\n\ninterface StatCardProps {\n  title: string;\n  value: string;\n  icon: React.ReactNode;\n  color: string;\n  change?: string;\n}\n\nconst StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, change }) => (\n  <Card sx={{ height: '100%' }}>\n    <CardContent>\n      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box>\n          <Typography color=\"textSecondary\" gutterBottom variant=\"overline\">\n            {title}\n          </Typography>\n          <Typography variant=\"h4\" component=\"div\">\n            {value}\n          </Typography>\n          {change && (\n            <Typography variant=\"body2\" sx={{ color: 'success.main', mt: 1 }}>\n              {change}\n            </Typography>\n          )}\n        </Box>\n        <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>\n          {icon}\n        </Avatar>\n      </Box>\n    </CardContent>\n  </Card>\n);\n\nconst Dashboard: React.FC = () => {\n  const stats = [\n    {\n      title: 'Total Users',\n      value: '1,234',\n      icon: <People />,\n      color: '#1976d2',\n      change: '+12% from last month',\n    },\n    {\n      title: 'Revenue',\n      value: '$45,678',\n      icon: <AttachMoney />,\n      color: '#4caf50',\n      change: '+8% from last month',\n    },\n    {\n      title: 'Orders',\n      value: '567',\n      icon: <ShoppingCart />,\n      color: '#ff9800',\n      change: '+15% from last month',\n    },\n    {\n      title: 'Growth',\n      value: '23.5%',\n      icon: <TrendingUp />,\n      color: '#9c27b0',\n      change: '+3% from last month',\n    },\n  ];\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n        Dashboard Overview\n      </Typography>\n      <Typography variant=\"body1\" color=\"textSecondary\" sx={{ mb: 2 }}>\n        Welcome to your Material Dashboard! Here's an overview of your key metrics.\n      </Typography>\n\n      <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' }, gap: 3 }}>\n        {stats.map((stat, index) => (\n          <StatCard key={index} {...stat} />\n        ))}\n      </Box>\n\n      <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '2fr 1fr' }, gap: 3, mt: 3 }}>\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Recent Activity\n          </Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">\n            This section would typically contain charts, graphs, or recent activity feeds.\n            You can integrate libraries like Chart.js, Recharts, or MUI X Charts here.\n          </Typography>\n        </Paper>\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Quick Actions\n          </Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">\n            Add quick action buttons or widgets here for common tasks.\n          </Typography>\n        </Paper>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,IAAI,CACJC,WAAW,CACXC,UAAU,CACVC,GAAG,CACHC,KAAK,CACLC,MAAM,KACD,eAAe,CACtB,OACEC,UAAU,CACVC,MAAM,CACNC,YAAY,CACZC,WAAW,KACN,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU7B,KAAM,CAAAC,QAAiC,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,KAAK,CAAEC,IAAI,CAAEC,KAAK,CAAEC,MAAO,CAAC,CAAAL,IAAA,oBAC9EJ,IAAA,CAACX,IAAI,EAACqB,EAAE,CAAE,CAAEC,MAAM,CAAE,MAAO,CAAE,CAAAC,QAAA,cAC3BZ,IAAA,CAACV,WAAW,EAAAsB,QAAA,cACVV,KAAA,CAACV,GAAG,EAACkB,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,eAAgB,CAAE,CAAAH,QAAA,eAClFV,KAAA,CAACV,GAAG,EAAAoB,QAAA,eACFZ,IAAA,CAACT,UAAU,EAACiB,KAAK,CAAC,eAAe,CAACQ,YAAY,MAACC,OAAO,CAAC,UAAU,CAAAL,QAAA,CAC9DP,KAAK,CACI,CAAC,cACbL,IAAA,CAACT,UAAU,EAAC0B,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAAAN,QAAA,CACrCN,KAAK,CACI,CAAC,CACZG,MAAM,eACLT,IAAA,CAACT,UAAU,EAAC0B,OAAO,CAAC,OAAO,CAACP,EAAE,CAAE,CAAEF,KAAK,CAAE,cAAc,CAAEW,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,CAC9DH,MAAM,CACG,CACb,EACE,CAAC,cACNT,IAAA,CAACN,MAAM,EAACgB,EAAE,CAAE,CAAEU,OAAO,CAAEZ,KAAK,CAAEa,KAAK,CAAE,EAAE,CAAEV,MAAM,CAAE,EAAG,CAAE,CAAAC,QAAA,CACnDL,IAAI,CACC,CAAC,EACN,CAAC,CACK,CAAC,CACV,CAAC,EACR,CAED,KAAM,CAAAe,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,KAAK,CAAG,CACZ,CACElB,KAAK,CAAE,aAAa,CACpBC,KAAK,CAAE,OAAO,CACdC,IAAI,cAAEP,IAAA,CAACJ,MAAM,GAAE,CAAC,CAChBY,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAE,sBACV,CAAC,CACD,CACEJ,KAAK,CAAE,SAAS,CAChBC,KAAK,CAAE,SAAS,CAChBC,IAAI,cAAEP,IAAA,CAACF,WAAW,GAAE,CAAC,CACrBU,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAE,qBACV,CAAC,CACD,CACEJ,KAAK,CAAE,QAAQ,CACfC,KAAK,CAAE,KAAK,CACZC,IAAI,cAAEP,IAAA,CAACH,YAAY,GAAE,CAAC,CACtBW,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAE,sBACV,CAAC,CACD,CACEJ,KAAK,CAAE,QAAQ,CACfC,KAAK,CAAE,OAAO,CACdC,IAAI,cAAEP,IAAA,CAACL,UAAU,GAAE,CAAC,CACpBa,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAE,qBACV,CAAC,CACF,CAED,mBACEP,KAAA,CAACV,GAAG,EAAAoB,QAAA,eACFZ,IAAA,CAACT,UAAU,EAAC0B,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACF,YAAY,MAAAJ,QAAA,CAAC,oBAErD,CAAY,CAAC,cACbZ,IAAA,CAACT,UAAU,EAAC0B,OAAO,CAAC,OAAO,CAACT,KAAK,CAAC,eAAe,CAACE,EAAE,CAAE,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,CAAC,6EAEjE,CAAY,CAAC,cAEbZ,IAAA,CAACR,GAAG,EAACkB,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEY,mBAAmB,CAAE,CAAEC,EAAE,CAAE,KAAK,CAAEC,EAAE,CAAE,SAAS,CAAEC,EAAE,CAAE,iBAAkB,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAjB,QAAA,CAC5GW,KAAK,CAACO,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBACrBhC,IAAA,CAACG,QAAQ,KAAiB4B,IAAI,EAAfC,KAAkB,CAClC,CAAC,CACC,CAAC,cAEN9B,KAAA,CAACV,GAAG,EAACkB,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEY,mBAAmB,CAAE,CAAEC,EAAE,CAAE,KAAK,CAAEE,EAAE,CAAE,SAAU,CAAC,CAAEC,GAAG,CAAE,CAAC,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,eAC7FV,KAAA,CAACT,KAAK,EAACiB,EAAE,CAAE,CAAEuB,CAAC,CAAE,CAAE,CAAE,CAAArB,QAAA,eAClBZ,IAAA,CAACT,UAAU,EAAC0B,OAAO,CAAC,IAAI,CAACD,YAAY,MAAAJ,QAAA,CAAC,iBAEtC,CAAY,CAAC,cACbZ,IAAA,CAACT,UAAU,EAAC0B,OAAO,CAAC,OAAO,CAACT,KAAK,CAAC,eAAe,CAAAI,QAAA,CAAC,2JAGlD,CAAY,CAAC,EACR,CAAC,cACRV,KAAA,CAACT,KAAK,EAACiB,EAAE,CAAE,CAAEuB,CAAC,CAAE,CAAE,CAAE,CAAArB,QAAA,eAClBZ,IAAA,CAACT,UAAU,EAAC0B,OAAO,CAAC,IAAI,CAACD,YAAY,MAAAJ,QAAA,CAAC,eAEtC,CAAY,CAAC,cACbZ,IAAA,CAACT,UAAU,EAAC0B,OAAO,CAAC,OAAO,CAACT,KAAK,CAAC,eAAe,CAAAI,QAAA,CAAC,4DAElD,CAAY,CAAC,EACR,CAAC,EACL,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAU,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}