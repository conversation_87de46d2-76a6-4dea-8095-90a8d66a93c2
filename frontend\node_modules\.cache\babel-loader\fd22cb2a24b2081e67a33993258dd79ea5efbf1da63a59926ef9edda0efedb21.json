{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Container } from 'react-bootstrap';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Navbar from './components/layout/Navbar';\nimport Home from './pages/Home';\nimport Login from './pages/auth/Login';\nimport Register from './pages/auth/Register';\nimport ForgotPassword from './pages/auth/ForgotPassword';\nimport ResetPassword from './pages/auth/ResetPassword';\nimport Profile from './pages/user/Profile';\nimport EditProfile from './pages/user/EditProfile';\nimport PageView from './pages/cms/PageView';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\nimport EmailVerificationNotice from './components/auth/EmailVerificationNotice';\n// Material Dashboard imports\nimport DashboardRoute from './components/dashboard/DashboardRoute';\nimport Dashboard from './pages/dashboard/Dashboard';\nimport Credit from './pages/dashboard/Credit';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(DashboardRoute, {\n            children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard/credit\",\n          element: /*#__PURE__*/_jsxDEV(DashboardRoute, {\n            children: /*#__PURE__*/_jsxDEV(Credit, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/*\",\n          element: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"App\",\n            children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Container, {\n              className: \"mt-4\",\n              children: /*#__PURE__*/_jsxDEV(Routes, {\n                children: [/*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/\",\n                  element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 56,\n                    columnNumber: 46\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/login\",\n                  element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 57,\n                    columnNumber: 51\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/register\",\n                  element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 58,\n                    columnNumber: 54\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/forgot-password\",\n                  element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 61\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/reset-password\",\n                  element: /*#__PURE__*/_jsxDEV(ResetPassword, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 60\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/pages/:slug\",\n                  element: /*#__PURE__*/_jsxDEV(PageView, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 61,\n                    columnNumber: 57\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 61,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/profile\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 68,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 67,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/profile/edit\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    children: /*#__PURE__*/_jsxDEV(EditProfile, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 76,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 75,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/email-verification\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    children: /*#__PURE__*/_jsxDEV(EmailVerificationNotice, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 86,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"*\",\n                  element: /*#__PURE__*/_jsxDEV(Navigate, {\n                    to: \"/\",\n                    replace: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 92,\n                    columnNumber: 46\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Container", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Home", "<PERSON><PERSON>", "Register", "ForgotPassword", "ResetPassword", "Profile", "EditProfile", "<PERSON><PERSON><PERSON><PERSON>", "ProtectedRoute", "EmailVerificationNotice", "DashboardRoute", "Dashboard", "Credit", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Container } from 'react-bootstrap';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Navbar from './components/layout/Navbar';\nimport Home from './pages/Home';\nimport Login from './pages/auth/Login';\nimport Register from './pages/auth/Register';\nimport ForgotPassword from './pages/auth/ForgotPassword';\nimport ResetPassword from './pages/auth/ResetPassword';\nimport Profile from './pages/user/Profile';\nimport EditProfile from './pages/user/EditProfile';\nimport PageView from './pages/cms/PageView';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\nimport EmailVerificationNotice from './components/auth/EmailVerificationNotice';\n// Material Dashboard imports\nimport DashboardRoute from './components/dashboard/DashboardRoute';\nimport Dashboard from './pages/dashboard/Dashboard';\nimport Credit from './pages/dashboard/Credit';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './App.css';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <Routes>\n          {/* Dashboard routes - these don't use the main layout */}\n          <Route\n            path=\"/dashboard\"\n            element={\n              <DashboardRoute>\n                <Dashboard />\n              </DashboardRoute>\n            }\n          />\n          <Route\n            path=\"/dashboard/credit\"\n            element={\n              <DashboardRoute>\n                <Credit />\n              </DashboardRoute>\n            }\n          />\n\n\n          {/* Main application routes with Bootstrap layout */}\n          <Route\n            path=\"/*\"\n            element={\n              <div className=\"App\">\n                <Navbar />\n                <Container className=\"mt-4\">\n                  <Routes>\n                    {/* Public routes */}\n                    <Route path=\"/\" element={<Home />} />\n                    <Route path=\"/login\" element={<Login />} />\n                    <Route path=\"/register\" element={<Register />} />\n                    <Route path=\"/forgot-password\" element={<ForgotPassword />} />\n                    <Route path=\"/reset-password\" element={<ResetPassword />} />\n                    <Route path=\"/pages/:slug\" element={<PageView />} />\n\n                    {/* Protected routes */}\n                    <Route\n                      path=\"/profile\"\n                      element={\n                        <ProtectedRoute>\n                          <Profile />\n                        </ProtectedRoute>\n                      }\n                    />\n                    <Route\n                      path=\"/profile/edit\"\n                      element={\n                        <ProtectedRoute>\n                          <EditProfile />\n                        </ProtectedRoute>\n                      }\n                    />\n\n                    {/* Email verification notice */}\n                    <Route\n                      path=\"/email-verification\"\n                      element={\n                        <ProtectedRoute>\n                          <EmailVerificationNotice />\n                        </ProtectedRoute>\n                      }\n                    />\n\n                    {/* Catch all route */}\n                    <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n                  </Routes>\n                </Container>\n              </div>\n            }\n          />\n        </Routes>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,uBAAuB,MAAM,2CAA2C;AAC/E;AACA,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAAChB,YAAY;IAAAkB,QAAA,eACXF,OAAA,CAACrB,MAAM;MAAAuB,QAAA,eACLF,OAAA,CAACpB,MAAM;QAAAsB,QAAA,gBAELF,OAAA,CAACnB,KAAK;UACJsB,IAAI,EAAC,YAAY;UACjBC,OAAO,eACLJ,OAAA,CAACJ,cAAc;YAAAM,QAAA,eACbF,OAAA,CAACH,SAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFR,OAAA,CAACnB,KAAK;UACJsB,IAAI,EAAC,mBAAmB;UACxBC,OAAO,eACLJ,OAAA,CAACJ,cAAc;YAAAM,QAAA,eACbF,OAAA,CAACF,MAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAIFR,OAAA,CAACnB,KAAK;UACJsB,IAAI,EAAC,IAAI;UACTC,OAAO,eACLJ,OAAA;YAAKS,SAAS,EAAC,KAAK;YAAAP,QAAA,gBAClBF,OAAA,CAACf,MAAM;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVR,OAAA,CAACjB,SAAS;cAAC0B,SAAS,EAAC,MAAM;cAAAP,QAAA,eACzBF,OAAA,CAACpB,MAAM;gBAAAsB,QAAA,gBAELF,OAAA,CAACnB,KAAK;kBAACsB,IAAI,EAAC,GAAG;kBAACC,OAAO,eAAEJ,OAAA,CAACd,IAAI;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCR,OAAA,CAACnB,KAAK;kBAACsB,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAAEJ,OAAA,CAACb,KAAK;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3CR,OAAA,CAACnB,KAAK;kBAACsB,IAAI,EAAC,WAAW;kBAACC,OAAO,eAAEJ,OAAA,CAACZ,QAAQ;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDR,OAAA,CAACnB,KAAK;kBAACsB,IAAI,EAAC,kBAAkB;kBAACC,OAAO,eAAEJ,OAAA,CAACX,cAAc;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9DR,OAAA,CAACnB,KAAK;kBAACsB,IAAI,EAAC,iBAAiB;kBAACC,OAAO,eAAEJ,OAAA,CAACV,aAAa;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5DR,OAAA,CAACnB,KAAK;kBAACsB,IAAI,EAAC,cAAc;kBAACC,OAAO,eAAEJ,OAAA,CAACP,QAAQ;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAGpDR,OAAA,CAACnB,KAAK;kBACJsB,IAAI,EAAC,UAAU;kBACfC,OAAO,eACLJ,OAAA,CAACN,cAAc;oBAAAQ,QAAA,eACbF,OAAA,CAACT,OAAO;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACFR,OAAA,CAACnB,KAAK;kBACJsB,IAAI,EAAC,eAAe;kBACpBC,OAAO,eACLJ,OAAA,CAACN,cAAc;oBAAAQ,QAAA,eACbF,OAAA,CAACR,WAAW;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAGFR,OAAA,CAACnB,KAAK;kBACJsB,IAAI,EAAC,qBAAqB;kBAC1BC,OAAO,eACLJ,OAAA,CAACN,cAAc;oBAAAQ,QAAA,eACbF,OAAA,CAACL,uBAAuB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAGFR,OAAA,CAACnB,KAAK;kBAACsB,IAAI,EAAC,GAAG;kBAACC,OAAO,eAAEJ,OAAA,CAAClB,QAAQ;oBAAC4B,EAAE,EAAC,GAAG;oBAACC,OAAO;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACI,EAAA,GA/EQX,GAAG;AAiFZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}