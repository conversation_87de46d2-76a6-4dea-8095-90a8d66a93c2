{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams,Navigate}from'react-router-dom';import{Container,Row,<PERSON>,<PERSON>,<PERSON><PERSON>,Spin<PERSON>,Badge}from'react-bootstrap';import cmsService from'../../services/cmsService';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PageView=()=>{var _page$creator;const{slug}=useParams();const[page,setPage]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState('');useEffect(()=>{if(slug){loadPage(slug);}},[slug]);const loadPage=async pageSlug=>{try{setLoading(true);setError('');const pageData=await cmsService.getPage(pageSlug);setPage(pageData);}catch(err){var _err$response;if(((_err$response=err.response)===null||_err$response===void 0?void 0:_err$response.status)===404){setError('Page not found');}else{setError('Failed to load page. Please try again later.');}}finally{setLoading(false);}};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString('en-US',{year:'numeric',month:'long',day:'numeric',hour:'2-digit',minute:'2-digit'});};if(!slug){return/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true});}if(loading){return/*#__PURE__*/_jsx(Container,{className:\"d-flex justify-content-center align-items-center\",style:{minHeight:'50vh'},children:/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsxs(Col,{className:\"text-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",role:\"status\",children:/*#__PURE__*/_jsx(\"span\",{className:\"visually-hidden\",children:\"Loading...\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:\"Loading page...\"})]})})});}if(error){return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{md:8,children:/*#__PURE__*/_jsxs(Alert,{variant:\"danger\",className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"Oops!\"}),/*#__PURE__*/_jsx(\"p\",{children:error})]})})})});}if(!page){return/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true});}return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{lg:8,children:/*#__PURE__*/_jsxs(Card,{children:[page.featured_image&&/*#__PURE__*/_jsx(Card.Img,{variant:\"top\",src:page.featured_image,style:{height:'300px',objectFit:'cover'}}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[page.is_featured&&/*#__PURE__*/_jsx(Badge,{bg:\"warning\",className:\"me-2\",children:\"Featured\"}),/*#__PURE__*/_jsx(Badge,{bg:\"success\",children:\"Published\"})]}),/*#__PURE__*/_jsx(Card.Title,{as:\"h1\",className:\"mb-3\",children:page.title}),/*#__PURE__*/_jsx(\"div\",{className:\"text-muted mb-4\",children:/*#__PURE__*/_jsxs(\"small\",{children:[\"By \",/*#__PURE__*/_jsx(\"strong\",{children:(_page$creator=page.creator)===null||_page$creator===void 0?void 0:_page$creator.name}),\" \\u2022 Published on \",formatDate(page.published_at||page.created_at),page.updated_at!==page.created_at&&page.updater&&/*#__PURE__*/_jsxs(_Fragment,{children:[\" \\u2022 Last updated by \",/*#__PURE__*/_jsx(\"strong\",{children:page.updater.name})]})]})}),page.meta_description&&/*#__PURE__*/_jsx(\"div\",{className:\"alert alert-light mb-4\",children:/*#__PURE__*/_jsx(\"em\",{children:page.meta_description})}),/*#__PURE__*/_jsx(\"div\",{className:\"content\",dangerouslySetInnerHTML:{__html:page.content}}),page.meta_keywords&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 pt-3 border-top\",children:/*#__PURE__*/_jsxs(\"small\",{className:\"text-muted\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Keywords:\"}),\" \",page.meta_keywords]})})]})]})})})});};export default PageView;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Navigate", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Spinner", "Badge", "cmsService", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON>", "_page$creator", "slug", "page", "setPage", "loading", "setLoading", "error", "setError", "loadPage", "pageSlug", "pageData", "getPage", "err", "_err$response", "response", "status", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "to", "replace", "className", "style", "minHeight", "children", "animation", "role", "md", "variant", "lg", "featured_image", "Img", "src", "height", "objectFit", "Body", "is_featured", "bg", "Title", "as", "title", "creator", "name", "published_at", "created_at", "updated_at", "updater", "meta_description", "dangerouslySetInnerHTML", "__html", "content", "meta_keywords"], "sources": ["C:/laragon/www/frontend/src/pages/cms/PageView.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, Navigate } from 'react-router-dom';\nimport { Container, Row, Col, <PERSON>, <PERSON><PERSON>, Spinner, Badge } from 'react-bootstrap';\nimport cmsService, { CmsPage } from '../../services/cmsService';\n\nconst PageView: React.FC = () => {\n  const { slug } = useParams<{ slug: string }>();\n  const [page, setPage] = useState<CmsPage | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    if (slug) {\n      loadPage(slug);\n    }\n  }, [slug]);\n\n  const loadPage = async (pageSlug: string) => {\n    try {\n      setLoading(true);\n      setError('');\n      const pageData = await cmsService.getPage(pageSlug);\n      setPage(pageData);\n    } catch (err: any) {\n      if (err.response?.status === 404) {\n        setError('Page not found');\n      } else {\n        setError('Failed to load page. Please try again later.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  if (!slug) {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  if (loading) {\n    return (\n      <Container className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '50vh' }}>\n        <Row>\n          <Col className=\"text-center\">\n            <Spinner animation=\"border\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </Spinner>\n            <div className=\"mt-2\">Loading page...</div>\n          </Col>\n        </Row>\n      </Container>\n    );\n  }\n\n  if (error) {\n    return (\n      <Container>\n        <Row className=\"justify-content-center\">\n          <Col md={8}>\n            <Alert variant=\"danger\" className=\"text-center\">\n              <h4>Oops!</h4>\n              <p>{error}</p>\n            </Alert>\n          </Col>\n        </Row>\n      </Container>\n    );\n  }\n\n  if (!page) {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  return (\n    <Container>\n      <Row className=\"justify-content-center\">\n        <Col lg={8}>\n          <Card>\n            {page.featured_image && (\n              <Card.Img \n                variant=\"top\" \n                src={page.featured_image} \n                style={{ height: '300px', objectFit: 'cover' }}\n              />\n            )}\n            <Card.Body>\n              <div className=\"mb-3\">\n                {page.is_featured && (\n                  <Badge bg=\"warning\" className=\"me-2\">Featured</Badge>\n                )}\n                <Badge bg=\"success\">Published</Badge>\n              </div>\n              \n              <Card.Title as=\"h1\" className=\"mb-3\">\n                {page.title}\n              </Card.Title>\n              \n              <div className=\"text-muted mb-4\">\n                <small>\n                  By <strong>{page.creator?.name}</strong> • \n                  Published on {formatDate(page.published_at || page.created_at)}\n                  {page.updated_at !== page.created_at && page.updater && (\n                    <> • Last updated by <strong>{page.updater.name}</strong></>\n                  )}\n                </small>\n              </div>\n\n              {page.meta_description && (\n                <div className=\"alert alert-light mb-4\">\n                  <em>{page.meta_description}</em>\n                </div>\n              )}\n\n              <div \n                className=\"content\"\n                dangerouslySetInnerHTML={{ __html: page.content }}\n              />\n\n              {page.meta_keywords && (\n                <div className=\"mt-4 pt-3 border-top\">\n                  <small className=\"text-muted\">\n                    <strong>Keywords:</strong> {page.meta_keywords}\n                  </small>\n                </div>\n              )}\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default PageView;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,QAAQ,KAAQ,kBAAkB,CACtD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,KAAK,KAAQ,iBAAiB,CAClF,MAAO,CAAAC,UAAU,KAAmB,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEhE,KAAM,CAAAC,QAAkB,CAAGA,CAAA,GAAM,KAAAC,aAAA,CAC/B,KAAM,CAAEC,IAAK,CAAC,CAAGlB,SAAS,CAAmB,CAAC,CAC9C,KAAM,CAACmB,IAAI,CAAEC,OAAO,CAAC,CAAGtB,QAAQ,CAAiB,IAAI,CAAC,CACtD,KAAM,CAACuB,OAAO,CAAEC,UAAU,CAAC,CAAGxB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACyB,KAAK,CAAEC,QAAQ,CAAC,CAAG1B,QAAQ,CAAS,EAAE,CAAC,CAE9CC,SAAS,CAAC,IAAM,CACd,GAAImB,IAAI,CAAE,CACRO,QAAQ,CAACP,IAAI,CAAC,CAChB,CACF,CAAC,CAAE,CAACA,IAAI,CAAC,CAAC,CAEV,KAAM,CAAAO,QAAQ,CAAG,KAAO,CAAAC,QAAgB,EAAK,CAC3C,GAAI,CACFJ,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZ,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAlB,UAAU,CAACmB,OAAO,CAACF,QAAQ,CAAC,CACnDN,OAAO,CAACO,QAAQ,CAAC,CACnB,CAAE,MAAOE,GAAQ,CAAE,KAAAC,aAAA,CACjB,GAAI,EAAAA,aAAA,CAAAD,GAAG,CAACE,QAAQ,UAAAD,aAAA,iBAAZA,aAAA,CAAcE,MAAM,IAAK,GAAG,CAAE,CAChCR,QAAQ,CAAC,gBAAgB,CAAC,CAC5B,CAAC,IAAM,CACLA,QAAQ,CAAC,8CAA8C,CAAC,CAC1D,CACF,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAW,UAAU,CAAIC,UAAkB,EAAK,CACzC,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtDC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CAAC,CAED,GAAI,CAACvB,IAAI,CAAE,CACT,mBAAOP,IAAA,CAACV,QAAQ,EAACyC,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAC,CACpC,CAEA,GAAItB,OAAO,CAAE,CACX,mBACEV,IAAA,CAACT,SAAS,EAAC0C,SAAS,CAAC,kDAAkD,CAACC,KAAK,CAAE,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAC,QAAA,cACnGpC,IAAA,CAACR,GAAG,EAAA4C,QAAA,cACFlC,KAAA,CAACT,GAAG,EAACwC,SAAS,CAAC,aAAa,CAAAG,QAAA,eAC1BpC,IAAA,CAACJ,OAAO,EAACyC,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,QAAQ,CAAAF,QAAA,cACvCpC,IAAA,SAAMiC,SAAS,CAAC,iBAAiB,CAAAG,QAAA,CAAC,YAAU,CAAM,CAAC,CAC5C,CAAC,cACVpC,IAAA,QAAKiC,SAAS,CAAC,MAAM,CAAAG,QAAA,CAAC,iBAAe,CAAK,CAAC,EACxC,CAAC,CACH,CAAC,CACG,CAAC,CAEhB,CAEA,GAAIxB,KAAK,CAAE,CACT,mBACEZ,IAAA,CAACT,SAAS,EAAA6C,QAAA,cACRpC,IAAA,CAACR,GAAG,EAACyC,SAAS,CAAC,wBAAwB,CAAAG,QAAA,cACrCpC,IAAA,CAACP,GAAG,EAAC8C,EAAE,CAAE,CAAE,CAAAH,QAAA,cACTlC,KAAA,CAACP,KAAK,EAAC6C,OAAO,CAAC,QAAQ,CAACP,SAAS,CAAC,aAAa,CAAAG,QAAA,eAC7CpC,IAAA,OAAAoC,QAAA,CAAI,OAAK,CAAI,CAAC,cACdpC,IAAA,MAAAoC,QAAA,CAAIxB,KAAK,CAAI,CAAC,EACT,CAAC,CACL,CAAC,CACH,CAAC,CACG,CAAC,CAEhB,CAEA,GAAI,CAACJ,IAAI,CAAE,CACT,mBAAOR,IAAA,CAACV,QAAQ,EAACyC,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAC,CACpC,CAEA,mBACEhC,IAAA,CAACT,SAAS,EAAA6C,QAAA,cACRpC,IAAA,CAACR,GAAG,EAACyC,SAAS,CAAC,wBAAwB,CAAAG,QAAA,cACrCpC,IAAA,CAACP,GAAG,EAACgD,EAAE,CAAE,CAAE,CAAAL,QAAA,cACTlC,KAAA,CAACR,IAAI,EAAA0C,QAAA,EACF5B,IAAI,CAACkC,cAAc,eAClB1C,IAAA,CAACN,IAAI,CAACiD,GAAG,EACPH,OAAO,CAAC,KAAK,CACbI,GAAG,CAAEpC,IAAI,CAACkC,cAAe,CACzBR,KAAK,CAAE,CAAEW,MAAM,CAAE,OAAO,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAChD,CACF,cACD5C,KAAA,CAACR,IAAI,CAACqD,IAAI,EAAAX,QAAA,eACRlC,KAAA,QAAK+B,SAAS,CAAC,MAAM,CAAAG,QAAA,EAClB5B,IAAI,CAACwC,WAAW,eACfhD,IAAA,CAACH,KAAK,EAACoD,EAAE,CAAC,SAAS,CAAChB,SAAS,CAAC,MAAM,CAAAG,QAAA,CAAC,UAAQ,CAAO,CACrD,cACDpC,IAAA,CAACH,KAAK,EAACoD,EAAE,CAAC,SAAS,CAAAb,QAAA,CAAC,WAAS,CAAO,CAAC,EAClC,CAAC,cAENpC,IAAA,CAACN,IAAI,CAACwD,KAAK,EAACC,EAAE,CAAC,IAAI,CAAClB,SAAS,CAAC,MAAM,CAAAG,QAAA,CACjC5B,IAAI,CAAC4C,KAAK,CACD,CAAC,cAEbpD,IAAA,QAAKiC,SAAS,CAAC,iBAAiB,CAAAG,QAAA,cAC9BlC,KAAA,UAAAkC,QAAA,EAAO,KACF,cAAApC,IAAA,WAAAoC,QAAA,EAAA9B,aAAA,CAASE,IAAI,CAAC6C,OAAO,UAAA/C,aAAA,iBAAZA,aAAA,CAAcgD,IAAI,CAAS,CAAC,wBAC3B,CAAChC,UAAU,CAACd,IAAI,CAAC+C,YAAY,EAAI/C,IAAI,CAACgD,UAAU,CAAC,CAC7DhD,IAAI,CAACiD,UAAU,GAAKjD,IAAI,CAACgD,UAAU,EAAIhD,IAAI,CAACkD,OAAO,eAClDxD,KAAA,CAAAE,SAAA,EAAAgC,QAAA,EAAE,0BAAmB,cAAApC,IAAA,WAAAoC,QAAA,CAAS5B,IAAI,CAACkD,OAAO,CAACJ,IAAI,CAAS,CAAC,EAAE,CAC5D,EACI,CAAC,CACL,CAAC,CAEL9C,IAAI,CAACmD,gBAAgB,eACpB3D,IAAA,QAAKiC,SAAS,CAAC,wBAAwB,CAAAG,QAAA,cACrCpC,IAAA,OAAAoC,QAAA,CAAK5B,IAAI,CAACmD,gBAAgB,CAAK,CAAC,CAC7B,CACN,cAED3D,IAAA,QACEiC,SAAS,CAAC,SAAS,CACnB2B,uBAAuB,CAAE,CAAEC,MAAM,CAAErD,IAAI,CAACsD,OAAQ,CAAE,CACnD,CAAC,CAEDtD,IAAI,CAACuD,aAAa,eACjB/D,IAAA,QAAKiC,SAAS,CAAC,sBAAsB,CAAAG,QAAA,cACnClC,KAAA,UAAO+B,SAAS,CAAC,YAAY,CAAAG,QAAA,eAC3BpC,IAAA,WAAAoC,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAAC5B,IAAI,CAACuD,aAAa,EACzC,CAAC,CACL,CACN,EACQ,CAAC,EACR,CAAC,CACJ,CAAC,CACH,CAAC,CACG,CAAC,CAEhB,CAAC,CAED,cAAe,CAAA1D,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}