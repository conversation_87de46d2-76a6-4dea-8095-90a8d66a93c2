{"ast": null, "code": "/* eslint-disable import/prefer-default-export */\nimport { isPlainObject } from '@mui/utils/deepmerge';\nfunction isSerializable(val) {\n  return isPlainObject(val) || typeof val === 'undefined' || typeof val === 'string' || typeof val === 'boolean' || typeof val === 'number' || Array.isArray(val);\n}\n\n/**\n * `baseTheme` usually comes from `createTheme()` or `extendTheme()`.\n *\n * This function is intended to be used with zero-runtime CSS-in-JS like Pigment CSS\n * For example, in a Next.js project:\n *\n * ```js\n * // next.config.js\n * const { extendTheme } = require('@mui/material/styles');\n *\n * const theme = extendTheme();\n * // `.toRuntimeSource` is Pigment CSS specific to create a theme that is available at runtime.\n * theme.toRuntimeSource = stringifyTheme;\n *\n * module.exports = withPigment({\n *  theme,\n * });\n * ```\n */\nexport function stringifyTheme() {\n  let baseTheme = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const serializableTheme = {\n    ...baseTheme\n  };\n  function serializeTheme(object) {\n    const array = Object.entries(object);\n    // eslint-disable-next-line no-plusplus\n    for (let index = 0; index < array.length; index++) {\n      const [key, value] = array[index];\n      if (!isSerializable(value) || key.startsWith('unstable_')) {\n        delete object[key];\n      } else if (isPlainObject(value)) {\n        object[key] = {\n          ...value\n        };\n        serializeTheme(object[key]);\n      }\n    }\n  }\n  serializeTheme(serializableTheme);\n  return `import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';\n\nconst theme = ${JSON.stringify(serializableTheme, null, 2)};\n\ntheme.breakpoints = createBreakpoints(theme.breakpoints || {});\ntheme.transitions = createTransitions(theme.transitions || {});\n\nexport default theme;`;\n}", "map": {"version": 3, "names": ["isPlainObject", "isSerializable", "val", "Array", "isArray", "stringifyTheme", "baseTheme", "arguments", "length", "undefined", "serializableTheme", "serializeTheme", "object", "array", "Object", "entries", "index", "key", "value", "startsWith", "JSON", "stringify"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/styles/stringifyTheme.js"], "sourcesContent": ["/* eslint-disable import/prefer-default-export */\nimport { isPlainObject } from '@mui/utils/deepmerge';\nfunction isSerializable(val) {\n  return isPlainObject(val) || typeof val === 'undefined' || typeof val === 'string' || typeof val === 'boolean' || typeof val === 'number' || Array.isArray(val);\n}\n\n/**\n * `baseTheme` usually comes from `createTheme()` or `extendTheme()`.\n *\n * This function is intended to be used with zero-runtime CSS-in-JS like Pigment CSS\n * For example, in a Next.js project:\n *\n * ```js\n * // next.config.js\n * const { extendTheme } = require('@mui/material/styles');\n *\n * const theme = extendTheme();\n * // `.toRuntimeSource` is Pigment CSS specific to create a theme that is available at runtime.\n * theme.toRuntimeSource = stringifyTheme;\n *\n * module.exports = withPigment({\n *  theme,\n * });\n * ```\n */\nexport function stringifyTheme(baseTheme = {}) {\n  const serializableTheme = {\n    ...baseTheme\n  };\n  function serializeTheme(object) {\n    const array = Object.entries(object);\n    // eslint-disable-next-line no-plusplus\n    for (let index = 0; index < array.length; index++) {\n      const [key, value] = array[index];\n      if (!isSerializable(value) || key.startsWith('unstable_')) {\n        delete object[key];\n      } else if (isPlainObject(value)) {\n        object[key] = {\n          ...value\n        };\n        serializeTheme(object[key]);\n      }\n    }\n  }\n  serializeTheme(serializableTheme);\n  return `import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';\n\nconst theme = ${JSON.stringify(serializableTheme, null, 2)};\n\ntheme.breakpoints = createBreakpoints(theme.breakpoints || {});\ntheme.transitions = createTransitions(theme.transitions || {});\n\nexport default theme;`;\n}"], "mappings": "AAAA;AACA,SAASA,aAAa,QAAQ,sBAAsB;AACpD,SAASC,cAAcA,CAACC,GAAG,EAAE;EAC3B,OAAOF,aAAa,CAACE,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,WAAW,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,SAAS,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC;AACjK;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,cAAcA,CAAA,EAAiB;EAAA,IAAhBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC3C,MAAMG,iBAAiB,GAAG;IACxB,GAAGJ;EACL,CAAC;EACD,SAASK,cAAcA,CAACC,MAAM,EAAE;IAC9B,MAAMC,KAAK,GAAGC,MAAM,CAACC,OAAO,CAACH,MAAM,CAAC;IACpC;IACA,KAAK,IAAII,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,KAAK,CAACL,MAAM,EAAEQ,KAAK,EAAE,EAAE;MACjD,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,GAAGL,KAAK,CAACG,KAAK,CAAC;MACjC,IAAI,CAACf,cAAc,CAACiB,KAAK,CAAC,IAAID,GAAG,CAACE,UAAU,CAAC,WAAW,CAAC,EAAE;QACzD,OAAOP,MAAM,CAACK,GAAG,CAAC;MACpB,CAAC,MAAM,IAAIjB,aAAa,CAACkB,KAAK,CAAC,EAAE;QAC/BN,MAAM,CAACK,GAAG,CAAC,GAAG;UACZ,GAAGC;QACL,CAAC;QACDP,cAAc,CAACC,MAAM,CAACK,GAAG,CAAC,CAAC;MAC7B;IACF;EACF;EACAN,cAAc,CAACD,iBAAiB,CAAC;EACjC,OAAO;AACT;AACA,gBAAgBU,IAAI,CAACC,SAAS,CAACX,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1D;AACA;AACA;AACA;AACA,sBAAsB;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}