{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Tablelvl2Context from \"../Table/Tablelvl2Context.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTableHeadUtilityClass } from \"./tableHeadClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getTableHeadUtilityClass, classes);\n};\nconst TableHeadRoot = styled('thead', {\n  name: 'MuiTableHead',\n  slot: 'Root'\n})({\n  display: 'table-header-group'\n});\nconst tablelvl2 = {\n  variant: 'head'\n};\nconst defaultComponent = 'thead';\nconst TableHead = /*#__PURE__*/React.forwardRef(function TableHead(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTableHead'\n  });\n  const {\n    className,\n    component = defaultComponent,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(Tablelvl2Context.Provider, {\n    value: tablelvl2,\n    children: /*#__PURE__*/_jsx(TableHeadRoot, {\n      as: component,\n      className: clsx(classes.root, className),\n      ref: ref,\n      role: component === defaultComponent ? null : 'rowgroup',\n      ownerState: ownerState,\n      ...other\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TableHead.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `TableRow`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TableHead;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "Tablelvl2Context", "styled", "useDefaultProps", "getTableHeadUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "TableHeadRoot", "name", "slot", "display", "tablelvl2", "variant", "defaultComponent", "TableHead", "forwardRef", "inProps", "ref", "props", "className", "component", "other", "Provider", "value", "children", "as", "role", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "elementType", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/TableHead/TableHead.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Tablelvl2Context from \"../Table/Tablelvl2Context.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTableHeadUtilityClass } from \"./tableHeadClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getTableHeadUtilityClass, classes);\n};\nconst TableHeadRoot = styled('thead', {\n  name: 'MuiTableHead',\n  slot: 'Root'\n})({\n  display: 'table-header-group'\n});\nconst tablelvl2 = {\n  variant: 'head'\n};\nconst defaultComponent = 'thead';\nconst TableHead = /*#__PURE__*/React.forwardRef(function TableHead(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTableHead'\n  });\n  const {\n    className,\n    component = defaultComponent,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(Tablelvl2Context.Provider, {\n    value: tablelvl2,\n    children: /*#__PURE__*/_jsx(TableHeadRoot, {\n      as: component,\n      className: clsx(classes.root, className),\n      ref: ref,\n      role: component === defaultComponent ? null : 'rowgroup',\n      ownerState: ownerState,\n      ...other\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TableHead.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `TableRow`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TableHead;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOX,cAAc,CAACU,KAAK,EAAEN,wBAAwB,EAAEK,OAAO,CAAC;AACjE,CAAC;AACD,MAAMG,aAAa,GAAGV,MAAM,CAAC,OAAO,EAAE;EACpCW,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDC,OAAO,EAAE;AACX,CAAC,CAAC;AACF,MAAMC,SAAS,GAAG;EAChBC,OAAO,EAAE;AACX,CAAC;AACD,MAAMC,gBAAgB,GAAG,OAAO;AAChC,MAAMC,SAAS,GAAG,aAAatB,KAAK,CAACuB,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/E,MAAMC,KAAK,GAAGpB,eAAe,CAAC;IAC5BoB,KAAK,EAAEF,OAAO;IACdR,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJW,SAAS;IACTC,SAAS,GAAGP,gBAAgB;IAC5B,GAAGQ;EACL,CAAC,GAAGH,KAAK;EACT,MAAMf,UAAU,GAAG;IACjB,GAAGe,KAAK;IACRE;EACF,CAAC;EACD,MAAMhB,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACL,gBAAgB,CAAC0B,QAAQ,EAAE;IAClDC,KAAK,EAAEZ,SAAS;IAChBa,QAAQ,EAAE,aAAavB,IAAI,CAACM,aAAa,EAAE;MACzCkB,EAAE,EAAEL,SAAS;MACbD,SAAS,EAAEzB,IAAI,CAACU,OAAO,CAACE,IAAI,EAAEa,SAAS,CAAC;MACxCF,GAAG,EAAEA,GAAG;MACRS,IAAI,EAAEN,SAAS,KAAKP,gBAAgB,GAAG,IAAI,GAAG,UAAU;MACxDV,UAAU,EAAEA,UAAU;MACtB,GAAGkB;IACL,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGf,SAAS,CAACgB,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACEN,QAAQ,EAAE/B,SAAS,CAACsC,IAAI;EACxB;AACF;AACA;EACE3B,OAAO,EAAEX,SAAS,CAACuC,MAAM;EACzB;AACF;AACA;EACEb,SAAS,EAAE1B,SAAS,CAACwC,MAAM;EAC3B;AACF;AACA;AACA;EACEb,SAAS,EAAE3B,SAAS,CAACyC,WAAW;EAChC;AACF;AACA;EACEC,EAAE,EAAE1C,SAAS,CAAC2C,SAAS,CAAC,CAAC3C,SAAS,CAAC4C,OAAO,CAAC5C,SAAS,CAAC2C,SAAS,CAAC,CAAC3C,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACuC,MAAM,EAAEvC,SAAS,CAAC8C,IAAI,CAAC,CAAC,CAAC,EAAE9C,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACuC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAelB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}