{"ast": null, "code": "import api,{endpoints}from'./api';class AuthService{// Register new user\nasync register(data){const response=await api.post(endpoints.register,data);const{user,token}=response.data;if(token){this.setAuthData(user,token);}return response.data;}// Login user\nasync login(credentials){const response=await api.post(endpoints.login,credentials);const{user,token}=response.data;if(token){this.setAuthData(user,token);}return response.data;}// Logout user\nasync logout(){try{await api.post(endpoints.logout);}catch(error){console.error('Logout error:',error);}finally{this.clearAuthData();}}// Get current user profile\nasync getProfile(){const response=await api.get(endpoints.profile);return response.data.user;}// Update user profile\nasync updateProfile(data){const response=await api.put(endpoints.updateProfile,data);const user=response.data.user;// Update stored user data\nlocalStorage.setItem('user',JSON.stringify(user));return user;}// Upload avatar\nasync uploadAvatar(file){const formData=new FormData();formData.append('avatar',file);const response=await api.post(endpoints.uploadAvatar,formData,{headers:{'Content-Type':'multipart/form-data'}});const user=response.data.user;localStorage.setItem('user',JSON.stringify(user));return user;}// Forgot password\nasync forgotPassword(email){const response=await api.post(endpoints.forgotPassword,{email});return response.data;}// Reset password\nasync resetPassword(data){const response=await api.post(endpoints.resetPassword,data);return response.data;}// Resend email verification\nasync resendVerification(){const response=await api.post(endpoints.resendVerification);return response.data;}// Helper methods\nsetAuthData(user,token){localStorage.setItem('auth_token',token);localStorage.setItem('user',JSON.stringify(user));}clearAuthData(){localStorage.removeItem('auth_token');localStorage.removeItem('user');}getStoredUser(){const userStr=localStorage.getItem('user');return userStr?JSON.parse(userStr):null;}getStoredToken(){return localStorage.getItem('auth_token');}isAuthenticated(){return!!this.getStoredToken();}isEmailVerified(){const user=this.getStoredUser();return!!(user!==null&&user!==void 0&&user.email_verified_at);}}const authService=new AuthService();export default authService;", "map": {"version": 3, "names": ["api", "endpoints", "AuthService", "register", "data", "response", "post", "user", "token", "setAuthData", "login", "credentials", "logout", "error", "console", "clearAuthData", "getProfile", "get", "profile", "updateProfile", "put", "localStorage", "setItem", "JSON", "stringify", "uploadAvatar", "file", "formData", "FormData", "append", "headers", "forgotPassword", "email", "resetPassword", "resendVerification", "removeItem", "getStoredUser", "userStr", "getItem", "parse", "getStoredToken", "isAuthenticated", "isEmailVerified", "email_verified_at", "authService"], "sources": ["C:/laragon/www/frontend/src/services/authService.ts"], "sourcesContent": ["import api, { endpoints } from './api';\n\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  phone?: string;\n  bio?: string;\n  avatar?: string;\n  date_of_birth?: string;\n  role: 'admin' | 'user';\n  is_active: boolean;\n  email_verified_at?: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  password_confirmation: string;\n  phone?: string;\n  date_of_birth?: string;\n}\n\nexport interface AuthResponse {\n  message: string;\n  user: User;\n  token: string;\n}\n\nexport interface UpdateProfileData {\n  name?: string;\n  email?: string;\n  phone?: string;\n  bio?: string;\n  date_of_birth?: string;\n  current_password?: string;\n  password?: string;\n  password_confirmation?: string;\n}\n\nclass AuthService {\n  // Register new user\n  async register(data: RegisterData): Promise<AuthResponse> {\n    const response = await api.post(endpoints.register, data);\n    const { user, token } = response.data;\n\n    if (token) {\n      this.setAuthData(user, token);\n    }\n\n    return response.data;\n  }\n\n  // Login user\n  async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    const response = await api.post(endpoints.login, credentials);\n    const { user, token } = response.data;\n\n    if (token) {\n      this.setAuthData(user, token);\n    }\n\n    return response.data;\n  }\n\n  // Logout user\n  async logout(): Promise<void> {\n    try {\n      await api.post(endpoints.logout);\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      this.clearAuthData();\n    }\n  }\n\n  // Get current user profile\n  async getProfile(): Promise<User> {\n    const response = await api.get(endpoints.profile);\n    return response.data.user;\n  }\n\n  // Update user profile\n  async updateProfile(data: UpdateProfileData): Promise<User> {\n    const response = await api.put(endpoints.updateProfile, data);\n    const user = response.data.user;\n\n    // Update stored user data\n    localStorage.setItem('user', JSON.stringify(user));\n\n    return user;\n  }\n\n  // Upload avatar\n  async uploadAvatar(file: File): Promise<User> {\n    const formData = new FormData();\n    formData.append('avatar', file);\n\n    const response = await api.post(endpoints.uploadAvatar, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n\n    const user = response.data.user;\n    localStorage.setItem('user', JSON.stringify(user));\n\n    return user;\n  }\n\n  // Forgot password\n  async forgotPassword(email: string): Promise<{ message: string }> {\n    const response = await api.post(endpoints.forgotPassword, { email });\n    return response.data;\n  }\n\n  // Reset password\n  async resetPassword(data: {\n    token: string;\n    email: string;\n    password: string;\n    password_confirmation: string;\n  }): Promise<{ message: string }> {\n    const response = await api.post(endpoints.resetPassword, data);\n    return response.data;\n  }\n\n  // Resend email verification\n  async resendVerification(): Promise<{ message: string }> {\n    const response = await api.post(endpoints.resendVerification);\n    return response.data;\n  }\n\n  // Helper methods\n  setAuthData(user: User, token: string): void {\n    localStorage.setItem('auth_token', token);\n    localStorage.setItem('user', JSON.stringify(user));\n  }\n\n  clearAuthData(): void {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user');\n  }\n\n  getStoredUser(): User | null {\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n  }\n\n  getStoredToken(): string | null {\n    return localStorage.getItem('auth_token');\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getStoredToken();\n  }\n\n  isEmailVerified(): boolean {\n    const user = this.getStoredUser();\n    return !!user?.email_verified_at;\n  }\n}\n\nconst authService = new AuthService();\nexport default authService;\n"], "mappings": "AAAA,MAAO,CAAAA,GAAG,EAAIC,SAAS,KAAQ,OAAO,CAgDtC,KAAM,CAAAC,WAAY,CAChB;AACA,KAAM,CAAAC,QAAQA,CAACC,IAAkB,CAAyB,CACxD,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAL,GAAG,CAACM,IAAI,CAACL,SAAS,CAACE,QAAQ,CAAEC,IAAI,CAAC,CACzD,KAAM,CAAEG,IAAI,CAAEC,KAAM,CAAC,CAAGH,QAAQ,CAACD,IAAI,CAErC,GAAII,KAAK,CAAE,CACT,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEC,KAAK,CAAC,CAC/B,CAEA,MAAO,CAAAH,QAAQ,CAACD,IAAI,CACtB,CAEA;AACA,KAAM,CAAAM,KAAKA,CAACC,WAA6B,CAAyB,CAChE,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAAL,GAAG,CAACM,IAAI,CAACL,SAAS,CAACS,KAAK,CAAEC,WAAW,CAAC,CAC7D,KAAM,CAAEJ,IAAI,CAAEC,KAAM,CAAC,CAAGH,QAAQ,CAACD,IAAI,CAErC,GAAII,KAAK,CAAE,CACT,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEC,KAAK,CAAC,CAC/B,CAEA,MAAO,CAAAH,QAAQ,CAACD,IAAI,CACtB,CAEA;AACA,KAAM,CAAAQ,MAAMA,CAAA,CAAkB,CAC5B,GAAI,CACF,KAAM,CAAAZ,GAAG,CAACM,IAAI,CAACL,SAAS,CAACW,MAAM,CAAC,CAClC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CAAC,OAAS,CACR,IAAI,CAACE,aAAa,CAAC,CAAC,CACtB,CACF,CAEA;AACA,KAAM,CAAAC,UAAUA,CAAA,CAAkB,CAChC,KAAM,CAAAX,QAAQ,CAAG,KAAM,CAAAL,GAAG,CAACiB,GAAG,CAAChB,SAAS,CAACiB,OAAO,CAAC,CACjD,MAAO,CAAAb,QAAQ,CAACD,IAAI,CAACG,IAAI,CAC3B,CAEA;AACA,KAAM,CAAAY,aAAaA,CAACf,IAAuB,CAAiB,CAC1D,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAL,GAAG,CAACoB,GAAG,CAACnB,SAAS,CAACkB,aAAa,CAAEf,IAAI,CAAC,CAC7D,KAAM,CAAAG,IAAI,CAAGF,QAAQ,CAACD,IAAI,CAACG,IAAI,CAE/B;AACAc,YAAY,CAACC,OAAO,CAAC,MAAM,CAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC,CAElD,MAAO,CAAAA,IAAI,CACb,CAEA;AACA,KAAM,CAAAkB,YAAYA,CAACC,IAAU,CAAiB,CAC5C,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,CAAEH,IAAI,CAAC,CAE/B,KAAM,CAAArB,QAAQ,CAAG,KAAM,CAAAL,GAAG,CAACM,IAAI,CAACL,SAAS,CAACwB,YAAY,CAAEE,QAAQ,CAAE,CAChEG,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAvB,IAAI,CAAGF,QAAQ,CAACD,IAAI,CAACG,IAAI,CAC/Bc,YAAY,CAACC,OAAO,CAAC,MAAM,CAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC,CAElD,MAAO,CAAAA,IAAI,CACb,CAEA;AACA,KAAM,CAAAwB,cAAcA,CAACC,KAAa,CAAgC,CAChE,KAAM,CAAA3B,QAAQ,CAAG,KAAM,CAAAL,GAAG,CAACM,IAAI,CAACL,SAAS,CAAC8B,cAAc,CAAE,CAAEC,KAAM,CAAC,CAAC,CACpE,MAAO,CAAA3B,QAAQ,CAACD,IAAI,CACtB,CAEA;AACA,KAAM,CAAA6B,aAAaA,CAAC7B,IAKnB,CAAgC,CAC/B,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAL,GAAG,CAACM,IAAI,CAACL,SAAS,CAACgC,aAAa,CAAE7B,IAAI,CAAC,CAC9D,MAAO,CAAAC,QAAQ,CAACD,IAAI,CACtB,CAEA;AACA,KAAM,CAAA8B,kBAAkBA,CAAA,CAAiC,CACvD,KAAM,CAAA7B,QAAQ,CAAG,KAAM,CAAAL,GAAG,CAACM,IAAI,CAACL,SAAS,CAACiC,kBAAkB,CAAC,CAC7D,MAAO,CAAA7B,QAAQ,CAACD,IAAI,CACtB,CAEA;AACAK,WAAWA,CAACF,IAAU,CAAEC,KAAa,CAAQ,CAC3Ca,YAAY,CAACC,OAAO,CAAC,YAAY,CAAEd,KAAK,CAAC,CACzCa,YAAY,CAACC,OAAO,CAAC,MAAM,CAAEC,IAAI,CAACC,SAAS,CAACjB,IAAI,CAAC,CAAC,CACpD,CAEAQ,aAAaA,CAAA,CAAS,CACpBM,YAAY,CAACc,UAAU,CAAC,YAAY,CAAC,CACrCd,YAAY,CAACc,UAAU,CAAC,MAAM,CAAC,CACjC,CAEAC,aAAaA,CAAA,CAAgB,CAC3B,KAAM,CAAAC,OAAO,CAAGhB,YAAY,CAACiB,OAAO,CAAC,MAAM,CAAC,CAC5C,MAAO,CAAAD,OAAO,CAAGd,IAAI,CAACgB,KAAK,CAACF,OAAO,CAAC,CAAG,IAAI,CAC7C,CAEAG,cAAcA,CAAA,CAAkB,CAC9B,MAAO,CAAAnB,YAAY,CAACiB,OAAO,CAAC,YAAY,CAAC,CAC3C,CAEAG,eAAeA,CAAA,CAAY,CACzB,MAAO,CAAC,CAAC,IAAI,CAACD,cAAc,CAAC,CAAC,CAChC,CAEAE,eAAeA,CAAA,CAAY,CACzB,KAAM,CAAAnC,IAAI,CAAG,IAAI,CAAC6B,aAAa,CAAC,CAAC,CACjC,MAAO,CAAC,EAAC7B,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEoC,iBAAiB,EAClC,CACF,CAEA,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAA1C,WAAW,CAAC,CAAC,CACrC,cAAe,CAAA0C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}