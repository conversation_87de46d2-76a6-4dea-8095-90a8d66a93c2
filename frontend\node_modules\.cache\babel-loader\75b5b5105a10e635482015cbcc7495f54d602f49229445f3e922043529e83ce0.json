{"ast": null, "code": "export { default } from \"./Stack.js\";\nexport { default as stackClasses } from \"./stackClasses.js\";", "map": {"version": 3, "names": ["default", "stackClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Stack/index.js"], "sourcesContent": ["export { default } from \"./Stack.js\";\nexport { default as stackClasses } from \"./stackClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASA,OAAO,IAAIC,YAAY,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}