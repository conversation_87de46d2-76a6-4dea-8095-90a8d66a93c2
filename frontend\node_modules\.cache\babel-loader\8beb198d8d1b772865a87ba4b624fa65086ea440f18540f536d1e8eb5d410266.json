{"ast": null, "code": "export { ClickAwayListener as default } from \"./ClickAwayListener.js\";", "map": {"version": 3, "names": ["ClickAwayListener", "default"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/ClickAwayListener/index.js"], "sourcesContent": ["export { ClickAwayListener as default } from \"./ClickAwayListener.js\";"], "mappings": "AAAA,SAASA,iBAAiB,IAAIC,OAAO,QAAQ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}