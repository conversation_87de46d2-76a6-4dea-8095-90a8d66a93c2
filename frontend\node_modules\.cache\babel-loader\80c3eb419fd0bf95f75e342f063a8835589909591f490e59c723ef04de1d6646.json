{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\auth\\\\EmailVerificationNotice.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EmailVerificationNotice = () => {\n  _s();\n  const {\n    user,\n    resendVerification,\n    isEmailVerified\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const handleResendVerification = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      setMessage('');\n      const response = await resendVerification();\n      setMessage(response.message);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to resend verification email.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (isEmailVerified) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-success mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"bi bi-check-circle-fill\",\n                  style: {\n                    fontSize: '3rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Email Verified!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"Your email address has been successfully verified.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-warning mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bi bi-envelope-exclamation-fill\",\n                style: {\n                  fontSize: '3rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Verify Your Email Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-4\",\n              children: [\"We've sent a verification link to \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: user === null || user === void 0 ? void 0 : user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 51\n              }, this), \". Please check your email and click the verification link to activate your account.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), message && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"success\",\n              children: message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 27\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"small text-muted\",\n                children: \"Didn't receive the email? Check your spam folder or click the button below to resend.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              onClick: handleResendVerification,\n              disabled: loading,\n              className: \"mb-3\",\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                  as: \"span\",\n                  animation: \"border\",\n                  size: \"sm\",\n                  role: \"status\",\n                  \"aria-hidden\": \"true\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 21\n                }, this), \"Sending...\"]\n              }, void 0, true) : 'Resend Verification Email'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"Make sure to check your spam/junk folder if you don't see the email in your inbox.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_s(EmailVerificationNotice, \"aPHPqMBzlvcgBAxV59fNnkIAhaU=\", false, function () {\n  return [useAuth];\n});\n_c = EmailVerificationNotice;\nexport default EmailVerificationNotice;\nvar _c;\n$RefreshReg$(_c, \"EmailVerificationNotice\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EmailVerificationNotice", "_s", "user", "resendVerification", "isEmailVerified", "loading", "setLoading", "message", "setMessage", "error", "setError", "handleResendVerification", "response", "err", "_err$response", "_err$response$data", "data", "children", "className", "md", "Body", "style", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "variant", "onClick", "disabled", "as", "animation", "size", "role", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/auth/EmailVerificationNotice.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst EmailVerificationNotice: React.FC = () => {\n  const { user, resendVerification, isEmailVerified } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState<string>('');\n  const [error, setError] = useState<string>('');\n\n  const handleResendVerification = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      setMessage('');\n      \n      const response = await resendVerification();\n      setMessage(response.message);\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to resend verification email.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (isEmailVerified) {\n    return (\n      <Container>\n        <Row className=\"justify-content-center\">\n          <Col md={6}>\n            <Card>\n              <Card.Body className=\"text-center\">\n                <div className=\"text-success mb-3\">\n                  <i className=\"bi bi-check-circle-fill\" style={{ fontSize: '3rem' }}></i>\n                </div>\n                <h4>Email Verified!</h4>\n                <p className=\"text-muted\">Your email address has been successfully verified.</p>\n              </Card.Body>\n            </Card>\n          </Col>\n        </Row>\n      </Container>\n    );\n  }\n\n  return (\n    <Container>\n      <Row className=\"justify-content-center\">\n        <Col md={6}>\n          <Card>\n            <Card.Body className=\"text-center\">\n              <div className=\"text-warning mb-3\">\n                <i className=\"bi bi-envelope-exclamation-fill\" style={{ fontSize: '3rem' }}></i>\n              </div>\n              <h4>Verify Your Email Address</h4>\n              <p className=\"text-muted mb-4\">\n                We've sent a verification link to <strong>{user?.email}</strong>. \n                Please check your email and click the verification link to activate your account.\n              </p>\n\n              {message && <Alert variant=\"success\">{message}</Alert>}\n              {error && <Alert variant=\"danger\">{error}</Alert>}\n\n              <div className=\"mb-3\">\n                <p className=\"small text-muted\">\n                  Didn't receive the email? Check your spam folder or click the button below to resend.\n                </p>\n              </div>\n\n              <Button\n                variant=\"primary\"\n                onClick={handleResendVerification}\n                disabled={loading}\n                className=\"mb-3\"\n              >\n                {loading ? (\n                  <>\n                    <Spinner\n                      as=\"span\"\n                      animation=\"border\"\n                      size=\"sm\"\n                      role=\"status\"\n                      aria-hidden=\"true\"\n                      className=\"me-2\"\n                    />\n                    Sending...\n                  </>\n                ) : (\n                  'Resend Verification Email'\n                )}\n              </Button>\n\n              <div className=\"text-center\">\n                <small className=\"text-muted\">\n                  Make sure to check your spam/junk folder if you don't see the email in your inbox.\n                </small>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default EmailVerificationNotice;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,QAAQ,iBAAiB;AACnF,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,uBAAiC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9C,MAAM;IAAEC,IAAI;IAAEC,kBAAkB;IAAEC;EAAgB,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC/D,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAS,EAAE,CAAC;EAClD,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAS,EAAE,CAAC;EAE9C,MAAMwB,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;MACZF,UAAU,CAAC,EAAE,CAAC;MAEd,MAAMI,QAAQ,GAAG,MAAMT,kBAAkB,CAAC,CAAC;MAC3CK,UAAU,CAACI,QAAQ,CAACL,OAAO,CAAC;IAC9B,CAAC,CAAC,OAAOM,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBL,QAAQ,CAAC,EAAAI,aAAA,GAAAD,GAAG,CAACD,QAAQ,cAAAE,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcE,IAAI,cAAAD,kBAAA,uBAAlBA,kBAAA,CAAoBR,OAAO,KAAI,sCAAsC,CAAC;IACjF,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAIF,eAAe,EAAE;IACnB,oBACEP,OAAA,CAACT,SAAS;MAAA6B,QAAA,eACRpB,OAAA,CAACR,GAAG;QAAC6B,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrCpB,OAAA,CAACP,GAAG;UAAC6B,EAAE,EAAE,CAAE;UAAAF,QAAA,eACTpB,OAAA,CAACN,IAAI;YAAA0B,QAAA,eACHpB,OAAA,CAACN,IAAI,CAAC6B,IAAI;cAACF,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAChCpB,OAAA;gBAAKqB,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,eAChCpB,OAAA;kBAAGqB,SAAS,EAAC,yBAAyB;kBAACG,KAAK,EAAE;oBAAEC,QAAQ,EAAE;kBAAO;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACN7B,OAAA;gBAAAoB,QAAA,EAAI;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB7B,OAAA;gBAAGqB,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAkD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;EAEA,oBACE7B,OAAA,CAACT,SAAS;IAAA6B,QAAA,eACRpB,OAAA,CAACR,GAAG;MAAC6B,SAAS,EAAC,wBAAwB;MAAAD,QAAA,eACrCpB,OAAA,CAACP,GAAG;QAAC6B,EAAE,EAAE,CAAE;QAAAF,QAAA,eACTpB,OAAA,CAACN,IAAI;UAAA0B,QAAA,eACHpB,OAAA,CAACN,IAAI,CAAC6B,IAAI;YAACF,SAAS,EAAC,aAAa;YAAAD,QAAA,gBAChCpB,OAAA;cAAKqB,SAAS,EAAC,mBAAmB;cAAAD,QAAA,eAChCpB,OAAA;gBAAGqB,SAAS,EAAC,iCAAiC;gBAACG,KAAK,EAAE;kBAAEC,QAAQ,EAAE;gBAAO;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eACN7B,OAAA;cAAAoB,QAAA,EAAI;YAAyB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC7B,OAAA;cAAGqB,SAAS,EAAC,iBAAiB;cAAAD,QAAA,GAAC,oCACK,eAAApB,OAAA;gBAAAoB,QAAA,EAASf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB;cAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,uFAElE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAEHnB,OAAO,iBAAIV,OAAA,CAACL,KAAK;cAACoC,OAAO,EAAC,SAAS;cAAAX,QAAA,EAAEV;YAAO;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACrDjB,KAAK,iBAAIZ,OAAA,CAACL,KAAK;cAACoC,OAAO,EAAC,QAAQ;cAAAX,QAAA,EAAER;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEjD7B,OAAA;cAAKqB,SAAS,EAAC,MAAM;cAAAD,QAAA,eACnBpB,OAAA;gBAAGqB,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,EAAC;cAEhC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN7B,OAAA,CAACJ,MAAM;cACLmC,OAAO,EAAC,SAAS;cACjBC,OAAO,EAAElB,wBAAyB;cAClCmB,QAAQ,EAAEzB,OAAQ;cAClBa,SAAS,EAAC,MAAM;cAAAD,QAAA,EAEfZ,OAAO,gBACNR,OAAA,CAAAE,SAAA;gBAAAkB,QAAA,gBACEpB,OAAA,CAACH,OAAO;kBACNqC,EAAE,EAAC,MAAM;kBACTC,SAAS,EAAC,QAAQ;kBAClBC,IAAI,EAAC,IAAI;kBACTC,IAAI,EAAC,QAAQ;kBACb,eAAY,MAAM;kBAClBhB,SAAS,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,cAEJ;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAET7B,OAAA;cAAKqB,SAAS,EAAC,aAAa;cAAAD,QAAA,eAC1BpB,OAAA;gBAAOqB,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAE9B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACzB,EAAA,CAnGID,uBAAiC;EAAA,QACiBL,OAAO;AAAA;AAAAwC,EAAA,GADzDnC,uBAAiC;AAqGvC,eAAeA,uBAAuB;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}