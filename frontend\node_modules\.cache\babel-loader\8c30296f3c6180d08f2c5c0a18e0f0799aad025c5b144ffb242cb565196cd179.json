{"ast": null, "code": "export { default } from \"./Switch.js\";\nexport { default as switchClasses } from \"./switchClasses.js\";\nexport * from \"./switchClasses.js\";", "map": {"version": 3, "names": ["default", "switchClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Switch/index.js"], "sourcesContent": ["export { default } from \"./Switch.js\";\nexport { default as switchClasses } from \"./switchClasses.js\";\nexport * from \"./switchClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,aAAa,QAAQ,oBAAoB;AAC7D,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}