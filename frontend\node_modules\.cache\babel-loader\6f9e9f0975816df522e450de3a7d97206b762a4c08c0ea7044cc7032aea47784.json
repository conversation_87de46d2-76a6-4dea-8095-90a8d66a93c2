{"ast": null, "code": "export { default, createFilterOptions } from \"./useAutocomplete.js\";", "map": {"version": 3, "names": ["default", "createFilterOptions"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/useAutocomplete/index.js"], "sourcesContent": ["export { default, createFilterOptions } from \"./useAutocomplete.js\";"], "mappings": "AAAA,SAASA,OAAO,EAAEC,mBAAmB,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}