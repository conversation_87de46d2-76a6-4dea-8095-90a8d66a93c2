import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Box,

  CircularProgress,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  CheckCircle,
  ShoppingCart,
  Star,
} from '@mui/icons-material';
import creditService, { CreditPackage, PaymentConfig } from '../../services/creditService';

interface CreditPackagesProps {
  onPurchaseSuccess?: () => void;
}

const CreditPackages: React.FC<CreditPackagesProps> = ({ onPurchaseSuccess }) => {
  const [packages, setPackages] = useState<CreditPackage[]>([]);
  const [paymentConfig, setPaymentConfig] = useState<PaymentConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [purchasing, setPurchasing] = useState<number | null>(null);
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    package: CreditPackage | null;
  }>({ open: false, package: null });

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const [packagesData, configData] = await Promise.all([
        creditService.getPackages(),
        creditService.getPaymentConfig(),
      ]);
      setPackages(packagesData);
      setPaymentConfig(configData);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to load credit packages');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handlePurchaseClick = (pkg: CreditPackage) => {
    setConfirmDialog({ open: true, package: pkg });
  };

  const handleConfirmPurchase = async () => {
    if (!confirmDialog.package) return;

    try {
      setPurchasing(confirmDialog.package.id);
      const response = await creditService.createPayment(
        confirmDialog.package.id,
        `${window.location.origin}/dashboard/wallet`
      );

      if (response.success && response.payment_url) {
        // Call success callback if provided
        if (onPurchaseSuccess) {
          onPurchaseSuccess();
        }
        // Redirect to Billplz payment page
        window.location.href = response.payment_url;
      } else {
        setError(response.error || 'Payment creation failed');
        setPurchasing(null);
      }
    } catch (err: any) {
      console.error('Payment creation error:', err);
      const errorMessage = err.response?.data?.error ||
                          err.response?.data?.message ||
                          err.message ||
                          'Failed to create payment. Please try again or contact support.';
      setError(errorMessage);
      setPurchasing(null);
    }
    setConfirmDialog({ open: false, package: null });
  };

  const handleCloseDialog = () => {
    setConfirmDialog({ open: false, package: null });
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  if (!paymentConfig?.billplz_enabled) {
    return (
      <Card>
        <CardContent>
          <Alert severity="warning">
            Payment gateway is currently disabled. Please contact support for assistance.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Box>
        <Typography variant="h5" gutterBottom>
          Wallet Top-Up Packages
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Choose a top-up package that suits your needs. All payments are processed securely through Billplz.
        </Typography>

        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: {
              xs: '1fr',
              sm: 'repeat(2, 1fr)',
              md: 'repeat(3, 1fr)',
              lg: 'repeat(4, 1fr)',
            },
            gap: 3,
          }}
        >
          {packages.map((pkg, index) => (
            <Card
              key={pkg.id}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative',
                ...(index === 1 && {
                  border: 2,
                  borderColor: 'primary.main',
                }),
              }}
            >
                {index === 1 && (
                  <Chip
                    label="Most Popular"
                    color="primary"
                    icon={<Star />}
                    sx={{
                      position: 'absolute',
                      top: -10,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      zIndex: 1,
                    }}
                  />
                )}

                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h6" component="h3" gutterBottom>
                    {pkg.name}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {pkg.description}
                  </Typography>

                  <Box textAlign="center" my={2}>
                    <Typography variant="h4" component="div" color="primary">
                      {pkg.formatted_price}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {creditService.formatCredits(pkg.credit_amount)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {creditService.formatCurrency(pkg.price_per_credit)} per credit
                    </Typography>
                  </Box>

                  {pkg.features && pkg.features.length > 0 && (
                    <List dense>
                      {pkg.features.map((feature, featureIndex) => {
                        // Handle both old format (string) and new format (object with feature key)
                        const featureText = typeof feature === 'string' ? feature : (feature as any).feature;
                        return (
                          <ListItem key={featureIndex} sx={{ px: 0 }}>
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              <CheckCircle color="success" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Typography variant="body2">
                                  {featureText}
                                </Typography>
                              }
                            />
                          </ListItem>
                        );
                      })}
                    </List>
                  )}
                </CardContent>

                <CardActions sx={{ p: 2 }}>
                  <Button
                    variant={index === 1 ? 'contained' : 'outlined'}
                    fullWidth
                    startIcon={<ShoppingCart />}
                    onClick={() => handlePurchaseClick(pkg)}
                    disabled={purchasing === pkg.id}
                  >
                    {purchasing === pkg.id ? (
                      <CircularProgress size={20} />
                    ) : (
                      'Purchase Now'
                    )}
                  </Button>
                </CardActions>
              </Card>
          ))}
        </Box>
      </Box>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialog.open} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Confirm Purchase</DialogTitle>
        <DialogContent>
          {confirmDialog.package && (
            <Box>
              <Typography variant="body1" sx={{ mb: 2 }}>
                You are about to purchase the <strong>{confirmDialog.package.name}</strong> package.
              </Typography>
              <Box sx={{ backgroundColor: 'background.default', p: 2, borderRadius: 1 }}>
                <Typography variant="body2">
                  <strong>Package:</strong> {confirmDialog.package.name}
                </Typography>
                <Typography variant="body2">
                  <strong>Credits:</strong> {creditService.formatCredits(confirmDialog.package.credit_amount)}
                </Typography>
                <Typography variant="body2">
                  <strong>Price:</strong> {confirmDialog.package.formatted_price}
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                You will be redirected to Billplz to complete your payment securely.
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleConfirmPurchase} variant="contained">
            Proceed to Payment
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default CreditPackages;
