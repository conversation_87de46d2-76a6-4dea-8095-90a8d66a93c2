{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\pages\\\\dashboard\\\\Order.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Stepper, Step, StepLabel, Button, Paper, Card, CardContent, CardMedia, Chip, Alert, TextField, Divider } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport printingService from '../../services/printingService';\nimport FileUpload from '../../components/dashboard/FileUpload';\nimport ErrorBoundary from '../../components/common/ErrorBoundary';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst steps = ['Select Category', 'Choose Product', 'Configure Order', 'Upload Files', 'Review & Submit'];\nconst Order = () => {\n  _s();\n  const navigate = useNavigate();\n  const [activeStep, setActiveStep] = useState(0);\n  const [categories, setCategories] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState(null);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [orderItems, setOrderItems] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Product configuration state\n  const [quantity, setQuantity] = useState(1);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [priceCalculation, setPriceCalculation] = useState(null);\n  const [calculatingPrice, setCalculatingPrice] = useState(false);\n\n  // Order and file upload state\n  const [createdOrder, setCreatedOrder] = useState(null);\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [submittingOrder, setSubmittingOrder] = useState(false);\n  useEffect(() => {\n    loadCategories();\n  }, []);\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const data = await printingService.getCategories();\n      setCategories(data);\n    } catch (err) {\n      setError('Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadProducts = async categorySlug => {\n    try {\n      setLoading(true);\n      const data = await printingService.getProducts(categorySlug);\n      setProducts(data);\n    } catch (err) {\n      setError('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCategorySelect = async category => {\n    setSelectedCategory(category);\n    await loadProducts(category.slug);\n    setActiveStep(1);\n  };\n  const handleProductSelect = product => {\n    setSelectedProduct(product);\n    // Reset configuration when selecting a new product\n    // Ensure we use the product's actual minimum quantity, with a fallback to 1\n    const initialQuantity = product.min_quantity && product.min_quantity > 0 ? product.min_quantity : 1;\n    setQuantity(initialQuantity);\n    setSelectedOptions({});\n    setPriceCalculation(null);\n    setActiveStep(2);\n\n    // Debug log to verify product data (can be removed in production)\n    console.log('Product selected:', {\n      name: product.name,\n      min_quantity: product.min_quantity,\n      max_quantity: product.max_quantity,\n      initialQuantity: initialQuantity\n    });\n  };\n\n  // Calculate price when quantity or options change\n  const calculatePrice = async () => {\n    if (!selectedProduct) return;\n\n    // Convert quantity to number for API call\n    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n    if (isNaN(numericQuantity) || numericQuantity < (selectedProduct.min_quantity || 1)) return;\n    setCalculatingPrice(true);\n    try {\n      const result = await printingService.calculatePrice(selectedProduct.id, numericQuantity, selectedOptions);\n      setPriceCalculation(result);\n    } catch (err) {\n      setError('Failed to calculate price');\n      console.error('Price calculation error:', err);\n    } finally {\n      setCalculatingPrice(false);\n    }\n  };\n\n  // Effect to calculate price when quantity or options change\n  useEffect(() => {\n    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n    if (selectedProduct && !isNaN(numericQuantity) && numericQuantity >= (selectedProduct.min_quantity || 1)) {\n      const timeoutId = setTimeout(() => {\n        calculatePrice();\n      }, 300); // Debounce price calculation\n\n      return () => clearTimeout(timeoutId);\n    } else {\n      setPriceCalculation(null);\n    }\n  }, [selectedProduct, quantity, selectedOptions]);\n  const handleNext = async () => {\n    // If moving from configuration step, save the order item and create order\n    if (activeStep === 2 && selectedProduct && priceCalculation) {\n      const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n      const orderItem = {\n        product_id: selectedProduct.id,\n        quantity: numericQuantity,\n        selected_options: selectedOptions,\n        specifications: selectedProduct.specifications\n      };\n      setOrderItems([orderItem]);\n\n      // Create the order when moving to file upload step\n      await createOrder([orderItem]);\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n  const createOrder = async items => {\n    try {\n      setLoading(true);\n      const orderData = {\n        items,\n        special_instructions: '',\n        delivery_method: 'standard'\n      };\n      const order = await printingService.createOrder(orderData);\n      setCreatedOrder(order);\n    } catch (err) {\n      setError(err.message || 'Failed to create order');\n      console.error('Order creation error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleFilesUploaded = files => {\n    setUploadedFiles(prev => [...prev, ...files]);\n  };\n  const handleFileUploadError = error => {\n    setError(error);\n  };\n  const handleSubmitOrder = async () => {\n    if (!createdOrder) {\n      setError('No order to submit');\n      return;\n    }\n    try {\n      setSubmittingOrder(true);\n      // Order is already created, just navigate to success or orders page\n      navigate('/dashboard/orders');\n    } catch (err) {\n      setError(err.message || 'Failed to submit order');\n    } finally {\n      setSubmittingOrder(false);\n    }\n  };\n  const renderStepContent = step => {\n    var _selectedProduct$opti;\n    switch (step) {\n      case 0:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Select a Printing Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: {\n                xs: '1fr',\n                sm: 'repeat(2, 1fr)',\n                md: 'repeat(3, 1fr)'\n              },\n              gap: 3\n            },\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                cursor: 'pointer',\n                '&:hover': {\n                  elevation: 4\n                }\n              },\n              onClick: () => handleCategorySelect(category),\n              children: [category.image && /*#__PURE__*/_jsxDEV(CardMedia, {\n                component: \"img\",\n                height: \"140\",\n                image: category.image,\n                alt: category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  gutterBottom: true,\n                  variant: \"h6\",\n                  component: \"div\",\n                  children: category.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: category.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this), category.products_count !== undefined && /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `${category.products_count} products`,\n                  size: \"small\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)]\n            }, category.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this);\n      case 1:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [\"Choose a Product from \", selectedCategory === null || selectedCategory === void 0 ? void 0 : selectedCategory.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: {\n                xs: '1fr',\n                sm: 'repeat(2, 1fr)',\n                md: 'repeat(3, 1fr)'\n              },\n              gap: 3\n            },\n            children: products.map(product => /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                cursor: 'pointer',\n                '&:hover': {\n                  elevation: 4\n                }\n              },\n              onClick: () => handleProductSelect(product),\n              children: [product.image && /*#__PURE__*/_jsxDEV(CardMedia, {\n                component: \"img\",\n                height: \"140\",\n                image: product.image,\n                alt: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  gutterBottom: true,\n                  variant: \"h6\",\n                  component: \"div\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: product.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  children: product.formatted_base_price\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: [\"Min: \", product.min_quantity, \" | Production: \", product.production_time_days, \" days\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Configure Your Order\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), selectedProduct && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: {\n                xs: 'column',\n                md: 'row'\n              },\n              gap: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: selectedProduct.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: selectedProduct.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  children: \"Specifications:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 2\n                  },\n                  children: Object.entries(selectedProduct.specifications || {}).map(([key, value]) => /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      mb: 0.5\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [key, \":\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 27\n                    }, this), \" \", value]\n                  }, key, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Production Time: \", selectedProduct.production_time_days, \" days\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Order Configuration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  label: \"Quantity\",\n                  type: \"text\",\n                  value: quantity,\n                  onChange: e => {\n                    const inputValue = e.target.value;\n\n                    // Allow empty input for better user experience while typing\n                    if (inputValue === '') {\n                      setQuantity('');\n                      return;\n                    }\n\n                    // Only allow numeric characters\n                    if (!/^\\d+$/.test(inputValue)) {\n                      return;\n                    }\n                    const numericValue = parseInt(inputValue, 10);\n                    const minQty = selectedProduct.min_quantity && selectedProduct.min_quantity > 0 ? selectedProduct.min_quantity : 1;\n                    const maxQty = selectedProduct.max_quantity;\n\n                    // Apply validation constraints\n                    let validatedQuantity = Math.max(minQty, numericValue);\n                    if (maxQty && validatedQuantity > maxQty) {\n                      validatedQuantity = maxQty;\n                    }\n                    setQuantity(validatedQuantity);\n                  },\n                  onBlur: e => {\n                    // Ensure we have a valid number when user leaves the field\n                    const currentValue = e.target.value;\n                    if (currentValue === '' || isNaN(parseInt(currentValue, 10))) {\n                      const fallbackQuantity = selectedProduct.min_quantity && selectedProduct.min_quantity > 0 ? selectedProduct.min_quantity : 1;\n                      setQuantity(fallbackQuantity);\n                    }\n                  },\n                  onKeyDown: e => {\n                    // Allow navigation keys, backspace, delete, tab, escape, enter\n                    if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'Home', 'End'].includes(e.key)) {\n                      return;\n                    }\n\n                    // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z\n                    if (e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) {\n                      return;\n                    }\n\n                    // Only allow numeric characters\n                    if (!/^\\d$/.test(e.key)) {\n                      e.preventDefault();\n                    }\n                  },\n                  slotProps: {\n                    htmlInput: {\n                      inputMode: 'numeric',\n                      pattern: '[0-9]*',\n                      autoComplete: 'off',\n                      style: {\n                        fontSize: '16px',\n                        // Prevents zoom on mobile devices\n                        textAlign: 'left'\n                      }\n                    }\n                  },\n                  helperText: `Min: ${selectedProduct.min_quantity || 1}${selectedProduct.max_quantity ? `, Max: ${selectedProduct.max_quantity}` : ''} • Type quantity directly`,\n                  fullWidth: true,\n                  sx: {\n                    mb: 3,\n                    '& .MuiOutlinedInput-root': {\n                      '&:hover fieldset': {\n                        borderColor: 'primary.main'\n                      },\n                      '&.Mui-focused fieldset': {\n                        borderColor: 'primary.main'\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this), ((_selectedProduct$opti = selectedProduct.options) === null || _selectedProduct$opti === void 0 ? void 0 : _selectedProduct$opti.quantity_pricing) && /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    gutterBottom: true,\n                    children: \"Pricing Tiers:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      flexDirection: 'column',\n                      gap: 1\n                    },\n                    children: selectedProduct.options.quantity_pricing.map((tier, index) => {\n                      var _selectedProduct$opti2;\n                      const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n                      const isCurrentTier = !isNaN(numericQuantity) && numericQuantity >= tier.min_quantity && (index === selectedProduct.options.quantity_pricing.length - 1 || numericQuantity < ((_selectedProduct$opti2 = selectedProduct.options.quantity_pricing[index + 1]) === null || _selectedProduct$opti2 === void 0 ? void 0 : _selectedProduct$opti2.min_quantity));\n                      return /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          p: 1.5,\n                          borderRadius: 1,\n                          border: '1px solid',\n                          borderColor: isCurrentTier ? 'primary.main' : 'grey.300',\n                          backgroundColor: isCurrentTier ? 'primary.50' : 'transparent',\n                          transition: 'all 0.2s ease-in-out'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: isCurrentTier ? 600 : 400,\n                            color: isCurrentTier ? 'primary.main' : 'text.primary'\n                          },\n                          children: [tier.min_quantity, \"+ units: RM \", tier.price_per_unit.toFixed(2), \" each\", isCurrentTier && /*#__PURE__*/_jsxDEV(Chip, {\n                            label: \"Current\",\n                            size: \"small\",\n                            color: \"primary\",\n                            sx: {\n                              ml: 1,\n                              height: 20\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 478,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 469,\n                          columnNumber: 33\n                        }, this)\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 458,\n                        columnNumber: 31\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    my: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    gutterBottom: true,\n                    children: \"Price Summary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 23\n                  }, this), calculatingPrice ? /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: \"Calculating...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 504,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 25\n                  }, this) : priceCalculation ? /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: [\"Unit Price: RM \", priceCalculation.unit_price.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: [\"Quantity: \", typeof quantity === 'string' ? parseInt(quantity, 10) || 0 : quantity]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 511,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      color: \"primary\",\n                      sx: {\n                        mt: 1\n                      },\n                      children: [\"Total: \", priceCalculation.formatted_total_price]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      sx: {\n                        mt: 1,\n                        display: 'block'\n                      },\n                      children: [\"Estimated production time: \", priceCalculation.production_time_days, \" days\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 517,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 25\n                  }, this) : (() => {\n                    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n                    return !isNaN(numericQuantity) && numericQuantity >= ((selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.min_quantity) || 1);\n                  })() ? /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: \"Loading pricing...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"error\",\n                    children: [\"Please enter a valid quantity (minimum: \", (selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.min_quantity) || 1, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Upload Artwork Files\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Upload your artwork files for printing. You can upload multiple files and specify the file type for each upload.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this), createdOrder ? /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n            fallback: /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Failed to load file upload component. Please refresh the page and try again.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 19\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(FileUpload, {\n              orderId: createdOrder.id,\n              onFilesUploaded: handleFilesUploaded,\n              onError: handleFileUploadError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: \"Creating order... Please wait.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this);\n      case 4:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Review Your Order\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this), createdOrder && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: {\n                xs: '1fr',\n                md: '1fr 1fr'\n              },\n              gap: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Order Summary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                gutterBottom: true,\n                children: [\"Order #\", createdOrder.order_number]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 19\n              }, this), selectedProduct && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Product:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: selectedProduct.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: \"Quantity:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: typeof quantity === 'string' ? parseInt(quantity, 10) || 0 : quantity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 23\n                }, this), priceCalculation && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    sx: {\n                      mt: 1\n                    },\n                    children: \"Total Price:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    color: \"primary\",\n                    children: priceCalculation.formatted_total_price\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: createdOrder.status || 'Pending',\n                  color: \"warning\",\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: [\"Uploaded Files (\", uploadedFiles.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 19\n              }, this), uploadedFiles.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                children: uploadedFiles.map((file, index) => /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mb: 1,\n                    p: 1,\n                    bgcolor: 'grey.50',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    noWrap: true,\n                    children: file.original_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 628,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [file.formatted_file_size, \" \\u2022 \", file.file_type_label]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 27\n                  }, this)]\n                }, file.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"warning\",\n                children: \"No files uploaded yet. You can still submit the order and upload files later.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                gridColumn: '1 / -1',\n                textAlign: 'center',\n                mt: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                size: \"large\",\n                onClick: handleSubmitOrder,\n                disabled: submittingOrder,\n                children: submittingOrder ? 'Submitting...' : 'Submit Order'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this);\n      default:\n        return 'Unknown step';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 668,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Create New Order\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Stepper, {\n        activeStep: activeStep,\n        sx: {\n          mb: 4\n        },\n        children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n          children: /*#__PURE__*/_jsxDEV(StepLabel, {\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 15\n          }, this)\n        }, label, false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 686,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: 400\n        },\n        children: renderStepContent(activeStep)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 694,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'row',\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          disabled: activeStep === 0,\n          onClick: handleBack,\n          sx: {\n            mr: 1\n          },\n          children: \"Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flex: '1 1 auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 11\n        }, this), activeStep < steps.length - 1 && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNext,\n          disabled: loading || activeStep === 0 && !selectedCategory || activeStep === 1 && !selectedProduct || activeStep === 2 && (!priceCalculation || quantity < ((selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.min_quantity) || 1)) || activeStep === 3 && !createdOrder,\n          children: loading ? 'Creating Order...' : 'Next'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 13\n        }, this), activeStep === steps.length - 1 && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSubmitOrder,\n          disabled: submittingOrder || !createdOrder,\n          children: submittingOrder ? 'Submitting...' : 'Complete Order'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 685,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 674,\n    columnNumber: 5\n  }, this);\n};\n_s(Order, \"lLC0bf0nonp3GUIjOhzUO9jXvDE=\", false, function () {\n  return [useNavigate];\n});\n_c = Order;\nexport default Order;\nvar _c;\n$RefreshReg$(_c, \"Order\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Paper", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Chip", "<PERSON><PERSON>", "TextField", "Divider", "useNavigate", "printingService", "FileUpload", "Error<PERSON>ou<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "steps", "Order", "_s", "navigate", "activeStep", "setActiveStep", "categories", "setCategories", "products", "setProducts", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedProduct", "setSelectedProduct", "orderItems", "setOrderItems", "loading", "setLoading", "error", "setError", "quantity", "setQuantity", "selectedOptions", "setSelectedOptions", "priceCalculation", "setPriceCalculation", "calculatingPrice", "setCalculatingPrice", "createdOrder", "setCreated<PERSON><PERSON>r", "uploadedFiles", "setUploadedFiles", "submittingOrder", "setSubmittingOrder", "loadCategories", "data", "getCategories", "err", "loadProducts", "categorySlug", "getProducts", "handleCategorySelect", "category", "slug", "handleProductSelect", "product", "initialQuantity", "min_quantity", "console", "log", "name", "max_quantity", "calculatePrice", "numericQuantity", "parseInt", "isNaN", "result", "id", "timeoutId", "setTimeout", "clearTimeout", "handleNext", "orderItem", "product_id", "selected_options", "specifications", "createOrder", "prevActiveStep", "handleBack", "items", "orderData", "special_instructions", "delivery_method", "order", "message", "handleFilesUploaded", "files", "prev", "handleFileUploadError", "handleSubmitOrder", "renderStepContent", "step", "_selectedProduct$opti", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "display", "gridTemplateColumns", "xs", "sm", "md", "gap", "map", "cursor", "elevation", "onClick", "image", "component", "height", "alt", "color", "description", "products_count", "undefined", "label", "size", "mt", "mb", "formatted_base_price", "production_time_days", "flexDirection", "flex", "p", "Object", "entries", "key", "value", "type", "onChange", "e", "inputValue", "target", "test", "numericValue", "min<PERSON>ty", "max<PERSON>ty", "validatedQuantity", "Math", "max", "onBlur", "currentValue", "fallbackQuantity", "onKeyDown", "includes", "ctrl<PERSON>ey", "toLowerCase", "preventDefault", "slotProps", "htmlInput", "inputMode", "pattern", "autoComplete", "style", "fontSize", "textAlign", "helperText", "fullWidth", "borderColor", "options", "quantity_pricing", "tier", "index", "_selectedProduct$opti2", "isCurrentTier", "length", "borderRadius", "border", "backgroundColor", "transition", "fontWeight", "price_per_unit", "toFixed", "ml", "my", "alignItems", "unit_price", "formatted_total_price", "fallback", "severity", "orderId", "onFilesUploaded", "onError", "order_number", "status", "file", "bgcolor", "noWrap", "original_name", "formatted_file_size", "file_type_label", "gridColumn", "disabled", "justifyContent", "minHeight", "pt", "mr", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/pages/dashboard/Order.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Stepper,\n  Step,\n  StepLabel,\n  Button,\n  Paper,\n  Card,\n  CardContent,\n  CardMedia,\n  Chip,\n  Alert,\n  TextField,\n\n  Divider,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n} from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport printingService, { PrintingCategory, PrintingProduct, OrderItem, PriceCalculation, PrintingOrder, OrderFile } from '../../services/printingService';\nimport FileUpload from '../../components/dashboard/FileUpload';\nimport ErrorBoundary from '../../components/common/ErrorBoundary';\n\nconst steps = ['Select Category', 'Choose Product', 'Configure Order', 'Upload Files', 'Review & Submit'];\n\nconst Order: React.FC = () => {\n  const navigate = useNavigate();\n  const [activeStep, setActiveStep] = useState(0);\n  const [categories, setCategories] = useState<PrintingCategory[]>([]);\n  const [products, setProducts] = useState<PrintingProduct[]>([]);\n  const [selectedCategory, setSelectedCategory] = useState<PrintingCategory | null>(null);\n  const [selectedProduct, setSelectedProduct] = useState<PrintingProduct | null>(null);\n  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Product configuration state\n  const [quantity, setQuantity] = useState<number | string>(1);\n  const [selectedOptions, setSelectedOptions] = useState<Record<string, any>>({});\n  const [priceCalculation, setPriceCalculation] = useState<PriceCalculation | null>(null);\n  const [calculatingPrice, setCalculatingPrice] = useState(false);\n\n  // Order and file upload state\n  const [createdOrder, setCreatedOrder] = useState<PrintingOrder | null>(null);\n  const [uploadedFiles, setUploadedFiles] = useState<OrderFile[]>([]);\n  const [submittingOrder, setSubmittingOrder] = useState(false);\n\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const data = await printingService.getCategories();\n      setCategories(data);\n    } catch (err) {\n      setError('Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadProducts = async (categorySlug: string) => {\n    try {\n      setLoading(true);\n      const data = await printingService.getProducts(categorySlug);\n      setProducts(data);\n    } catch (err) {\n      setError('Failed to load products');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCategorySelect = async (category: PrintingCategory) => {\n    setSelectedCategory(category);\n    await loadProducts(category.slug);\n    setActiveStep(1);\n  };\n\n  const handleProductSelect = (product: PrintingProduct) => {\n    setSelectedProduct(product);\n    // Reset configuration when selecting a new product\n    // Ensure we use the product's actual minimum quantity, with a fallback to 1\n    const initialQuantity = product.min_quantity && product.min_quantity > 0 ? product.min_quantity : 1;\n    setQuantity(initialQuantity);\n    setSelectedOptions({});\n    setPriceCalculation(null);\n    setActiveStep(2);\n\n    // Debug log to verify product data (can be removed in production)\n    console.log('Product selected:', {\n      name: product.name,\n      min_quantity: product.min_quantity,\n      max_quantity: product.max_quantity,\n      initialQuantity: initialQuantity\n    });\n  };\n\n  // Calculate price when quantity or options change\n  const calculatePrice = async () => {\n    if (!selectedProduct) return;\n\n    // Convert quantity to number for API call\n    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n    if (isNaN(numericQuantity) || numericQuantity < (selectedProduct.min_quantity || 1)) return;\n\n    setCalculatingPrice(true);\n    try {\n      const result = await printingService.calculatePrice(\n        selectedProduct.id,\n        numericQuantity,\n        selectedOptions\n      );\n      setPriceCalculation(result);\n    } catch (err) {\n      setError('Failed to calculate price');\n      console.error('Price calculation error:', err);\n    } finally {\n      setCalculatingPrice(false);\n    }\n  };\n\n  // Effect to calculate price when quantity or options change\n  useEffect(() => {\n    const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n    if (selectedProduct && !isNaN(numericQuantity) && numericQuantity >= (selectedProduct.min_quantity || 1)) {\n      const timeoutId = setTimeout(() => {\n        calculatePrice();\n      }, 300); // Debounce price calculation\n\n      return () => clearTimeout(timeoutId);\n    } else {\n      setPriceCalculation(null);\n    }\n  }, [selectedProduct, quantity, selectedOptions]);\n\n  const handleNext = async () => {\n    // If moving from configuration step, save the order item and create order\n    if (activeStep === 2 && selectedProduct && priceCalculation) {\n      const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n      const orderItem: OrderItem = {\n        product_id: selectedProduct.id,\n        quantity: numericQuantity,\n        selected_options: selectedOptions,\n        specifications: selectedProduct.specifications,\n      };\n      setOrderItems([orderItem]);\n\n      // Create the order when moving to file upload step\n      await createOrder([orderItem]);\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  const createOrder = async (items: OrderItem[]) => {\n    try {\n      setLoading(true);\n      const orderData = {\n        items,\n        special_instructions: '',\n        delivery_method: 'standard',\n      };\n\n      const order = await printingService.createOrder(orderData);\n      setCreatedOrder(order);\n    } catch (err: any) {\n      setError(err.message || 'Failed to create order');\n      console.error('Order creation error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFilesUploaded = (files: OrderFile[]) => {\n    setUploadedFiles(prev => [...prev, ...files]);\n  };\n\n  const handleFileUploadError = (error: string) => {\n    setError(error);\n  };\n\n  const handleSubmitOrder = async () => {\n    if (!createdOrder) {\n      setError('No order to submit');\n      return;\n    }\n\n    try {\n      setSubmittingOrder(true);\n      // Order is already created, just navigate to success or orders page\n      navigate('/dashboard/orders');\n    } catch (err: any) {\n      setError(err.message || 'Failed to submit order');\n    } finally {\n      setSubmittingOrder(false);\n    }\n  };\n\n  const renderStepContent = (step: number) => {\n    switch (step) {\n      case 0:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Select a Printing Category\n            </Typography>\n            <Box\n              sx={{\n                display: 'grid',\n                gridTemplateColumns: {\n                  xs: '1fr',\n                  sm: 'repeat(2, 1fr)',\n                  md: 'repeat(3, 1fr)'\n                },\n                gap: 3\n              }}\n            >\n              {categories.map((category) => (\n                <Card\n                  key={category.id}\n                  sx={{ cursor: 'pointer', '&:hover': { elevation: 4 } }}\n                  onClick={() => handleCategorySelect(category)}\n                >\n                  {category.image && (\n                    <CardMedia\n                      component=\"img\"\n                      height=\"140\"\n                      image={category.image}\n                      alt={category.name}\n                    />\n                  )}\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h6\" component=\"div\">\n                      {category.name}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {category.description}\n                    </Typography>\n                    {category.products_count !== undefined && (\n                      <Chip\n                        label={`${category.products_count} products`}\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    )}\n                  </CardContent>\n                </Card>\n              ))}\n            </Box>\n          </Box>\n        );\n\n      case 1:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Choose a Product from {selectedCategory?.name}\n            </Typography>\n            <Box\n              sx={{\n                display: 'grid',\n                gridTemplateColumns: {\n                  xs: '1fr',\n                  sm: 'repeat(2, 1fr)',\n                  md: 'repeat(3, 1fr)'\n                },\n                gap: 3\n              }}\n            >\n              {products.map((product) => (\n                <Card\n                  key={product.id}\n                  sx={{ cursor: 'pointer', '&:hover': { elevation: 4 } }}\n                  onClick={() => handleProductSelect(product)}\n                >\n                  {product.image && (\n                    <CardMedia\n                      component=\"img\"\n                      height=\"140\"\n                      image={product.image}\n                      alt={product.name}\n                    />\n                  )}\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h6\" component=\"div\">\n                      {product.name}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                      {product.description}\n                    </Typography>\n                    <Typography variant=\"h6\" color=\"primary\">\n                      {product.formatted_base_price}\n                    </Typography>\n                    <Typography variant=\"caption\" display=\"block\">\n                      Min: {product.min_quantity} | Production: {product.production_time_days} days\n                    </Typography>\n                  </CardContent>\n                </Card>\n              ))}\n            </Box>\n          </Box>\n        );\n\n      case 2:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Configure Your Order\n            </Typography>\n\n            {selectedProduct && (\n              <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>\n                {/* Product Summary */}\n                <Box sx={{ flex: 1 }}>\n                  <Paper sx={{ p: 3 }}>\n                    <Typography variant=\"h6\" gutterBottom>\n                      {selectedProduct.name}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                      {selectedProduct.description}\n                    </Typography>\n\n                    {/* Specifications */}\n                    <Typography variant=\"subtitle2\" gutterBottom>\n                      Specifications:\n                    </Typography>\n                    <Box sx={{ mb: 2 }}>\n                      {Object.entries(selectedProduct.specifications || {}).map(([key, value]) => (\n                        <Typography key={key} variant=\"body2\" sx={{ mb: 0.5 }}>\n                          <strong>{key}:</strong> {value}\n                        </Typography>\n                      ))}\n                    </Box>\n\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Production Time: {selectedProduct.production_time_days} days\n                    </Typography>\n                  </Paper>\n                </Box>\n\n                {/* Configuration Form */}\n                <Box sx={{ flex: 1 }}>\n                  <Paper sx={{ p: 3 }}>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Order Configuration\n                    </Typography>\n\n                    {/* Quantity Input - Enhanced for Manual Text Entry */}\n                    <TextField\n                      label=\"Quantity\"\n                      type=\"text\"\n                      value={quantity}\n                      onChange={(e) => {\n                        const inputValue = e.target.value;\n\n                        // Allow empty input for better user experience while typing\n                        if (inputValue === '') {\n                          setQuantity('');\n                          return;\n                        }\n\n                        // Only allow numeric characters\n                        if (!/^\\d+$/.test(inputValue)) {\n                          return;\n                        }\n\n                        const numericValue = parseInt(inputValue, 10);\n                        const minQty = selectedProduct.min_quantity && selectedProduct.min_quantity > 0 ? selectedProduct.min_quantity : 1;\n                        const maxQty = selectedProduct.max_quantity;\n\n                        // Apply validation constraints\n                        let validatedQuantity = Math.max(minQty, numericValue);\n                        if (maxQty && validatedQuantity > maxQty) {\n                          validatedQuantity = maxQty;\n                        }\n\n                        setQuantity(validatedQuantity);\n                      }}\n                      onBlur={(e) => {\n                        // Ensure we have a valid number when user leaves the field\n                        const currentValue = e.target.value;\n                        if (currentValue === '' || isNaN(parseInt(currentValue, 10))) {\n                          const fallbackQuantity = selectedProduct.min_quantity && selectedProduct.min_quantity > 0 ? selectedProduct.min_quantity : 1;\n                          setQuantity(fallbackQuantity);\n                        }\n                      }}\n                      onKeyDown={(e) => {\n                        // Allow navigation keys, backspace, delete, tab, escape, enter\n                        if ([\n                          'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',\n                          'Backspace', 'Delete', 'Tab', 'Escape', 'Enter',\n                          'Home', 'End'\n                        ].includes(e.key)) {\n                          return;\n                        }\n\n                        // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z\n                        if (e.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(e.key.toLowerCase())) {\n                          return;\n                        }\n\n                        // Only allow numeric characters\n                        if (!/^\\d$/.test(e.key)) {\n                          e.preventDefault();\n                        }\n                      }}\n                      slotProps={{\n                        htmlInput: {\n                          inputMode: 'numeric',\n                          pattern: '[0-9]*',\n                          autoComplete: 'off',\n                          style: {\n                            fontSize: '16px', // Prevents zoom on mobile devices\n                            textAlign: 'left'\n                          }\n                        }\n                      }}\n                      helperText={`Min: ${selectedProduct.min_quantity || 1}${selectedProduct.max_quantity ? `, Max: ${selectedProduct.max_quantity}` : ''} • Type quantity directly`}\n                      fullWidth\n                      sx={{\n                        mb: 3,\n                        '& .MuiOutlinedInput-root': {\n                          '&:hover fieldset': {\n                            borderColor: 'primary.main',\n                          },\n                          '&.Mui-focused fieldset': {\n                            borderColor: 'primary.main',\n                          }\n                        }\n                      }}\n                    />\n\n                    {/* Quantity-based Pricing Tiers */}\n                    {selectedProduct.options?.quantity_pricing && (\n                      <Box sx={{ mb: 3 }}>\n                        <Typography variant=\"subtitle1\" gutterBottom>\n                          Pricing Tiers:\n                        </Typography>\n                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n                          {selectedProduct.options.quantity_pricing.map((tier: any, index: number) => {\n                            const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n                            const isCurrentTier = !isNaN(numericQuantity) && numericQuantity >= tier.min_quantity &&\n                              (index === selectedProduct.options.quantity_pricing.length - 1 ||\n                               numericQuantity < selectedProduct.options.quantity_pricing[index + 1]?.min_quantity);\n\n                            return (\n                              <Box\n                                key={index}\n                                sx={{\n                                  p: 1.5,\n                                  borderRadius: 1,\n                                  border: '1px solid',\n                                  borderColor: isCurrentTier ? 'primary.main' : 'grey.300',\n                                  backgroundColor: isCurrentTier ? 'primary.50' : 'transparent',\n                                  transition: 'all 0.2s ease-in-out'\n                                }}\n                              >\n                                <Typography\n                                  variant=\"body2\"\n                                  sx={{\n                                    fontWeight: isCurrentTier ? 600 : 400,\n                                    color: isCurrentTier ? 'primary.main' : 'text.primary'\n                                  }}\n                                >\n                                  {tier.min_quantity}+ units: RM {tier.price_per_unit.toFixed(2)} each\n                                  {isCurrentTier && (\n                                    <Chip\n                                      label=\"Current\"\n                                      size=\"small\"\n                                      color=\"primary\"\n                                      sx={{ ml: 1, height: 20 }}\n                                    />\n                                  )}\n                                </Typography>\n                              </Box>\n                            );\n                          })}\n                        </Box>\n                      </Box>\n                    )}\n\n                    <Divider sx={{ my: 2 }} />\n\n                    {/* Price Calculation */}\n                    <Box>\n                      <Typography variant=\"h6\" gutterBottom>\n                        Price Summary\n                      </Typography>\n\n\n                      {calculatingPrice ? (\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <Typography variant=\"body2\">Calculating...</Typography>\n                        </Box>\n                      ) : priceCalculation ? (\n                        <Box>\n                          <Typography variant=\"body1\">\n                            Unit Price: RM {priceCalculation.unit_price.toFixed(2)}\n                          </Typography>\n                          <Typography variant=\"body1\">\n                            Quantity: {typeof quantity === 'string' ? parseInt(quantity, 10) || 0 : quantity}\n                          </Typography>\n                          <Typography variant=\"h5\" color=\"primary\" sx={{ mt: 1 }}>\n                            Total: {priceCalculation.formatted_total_price}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1, display: 'block' }}>\n                            Estimated production time: {priceCalculation.production_time_days} days\n                          </Typography>\n                        </Box>\n                      ) : (() => {\n                          const numericQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;\n                          return !isNaN(numericQuantity) && numericQuantity >= (selectedProduct?.min_quantity || 1);\n                        })() ? (\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Loading pricing...\n                        </Typography>\n                      ) : (\n                        <Typography variant=\"body2\" color=\"error\">\n                          Please enter a valid quantity (minimum: {selectedProduct?.min_quantity || 1})\n                        </Typography>\n                      )}\n                    </Box>\n                  </Paper>\n                </Box>\n              </Box>\n            )}\n          </Box>\n        );\n\n      case 3:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Upload Artwork Files\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n              Upload your artwork files for printing. You can upload multiple files and specify the file type for each upload.\n            </Typography>\n\n            {createdOrder ? (\n              <ErrorBoundary\n                fallback={\n                  <Alert severity=\"error\" sx={{ mb: 2 }}>\n                    Failed to load file upload component. Please refresh the page and try again.\n                  </Alert>\n                }\n              >\n                <FileUpload\n                  orderId={createdOrder.id}\n                  onFilesUploaded={handleFilesUploaded}\n                  onError={handleFileUploadError}\n                />\n              </ErrorBoundary>\n            ) : (\n              <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                Creating order... Please wait.\n              </Alert>\n            )}\n          </Box>\n        );\n\n      case 4:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Review Your Order\n            </Typography>\n\n            {createdOrder && (\n              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>\n                {/* Order Summary */}\n                <Paper sx={{ p: 3 }}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Order Summary\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                    Order #{createdOrder.order_number}\n                  </Typography>\n\n                  {selectedProduct && (\n                    <Box sx={{ mt: 2 }}>\n                      <Typography variant=\"subtitle2\">Product:</Typography>\n                      <Typography variant=\"body2\">{selectedProduct.name}</Typography>\n\n                      <Typography variant=\"subtitle2\" sx={{ mt: 1 }}>Quantity:</Typography>\n                      <Typography variant=\"body2\">{typeof quantity === 'string' ? parseInt(quantity, 10) || 0 : quantity}</Typography>\n\n                      {priceCalculation && (\n                        <>\n                          <Typography variant=\"subtitle2\" sx={{ mt: 1 }}>Total Price:</Typography>\n                          <Typography variant=\"h6\" color=\"primary\">\n                            {priceCalculation.formatted_total_price}\n                          </Typography>\n                        </>\n                      )}\n\n                      <Typography variant=\"subtitle2\" sx={{ mt: 1 }}>Status:</Typography>\n                      <Chip\n                        label={createdOrder.status || 'Pending'}\n                        color=\"warning\"\n                        size=\"small\"\n                      />\n                    </Box>\n                  )}\n                </Paper>\n\n                {/* Files Summary */}\n                <Paper sx={{ p: 3 }}>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Uploaded Files ({uploadedFiles.length})\n                  </Typography>\n\n                  {uploadedFiles.length > 0 ? (\n                    <Box>\n                      {uploadedFiles.map((file, index) => (\n                        <Box key={file.id} sx={{ mb: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>\n                          <Typography variant=\"body2\" noWrap>\n                            {file.original_name}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {file.formatted_file_size} • {file.file_type_label}\n                          </Typography>\n                        </Box>\n                      ))}\n                    </Box>\n                  ) : (\n                    <Alert severity=\"warning\">\n                      No files uploaded yet. You can still submit the order and upload files later.\n                    </Alert>\n                  )}\n                </Paper>\n\n                {/* Submit Button */}\n                <Box sx={{ gridColumn: '1 / -1', textAlign: 'center', mt: 2 }}>\n                  <Button\n                    variant=\"contained\"\n                    size=\"large\"\n                    onClick={handleSubmitOrder}\n                    disabled={submittingOrder}\n                  >\n                    {submittingOrder ? 'Submitting...' : 'Submit Order'}\n                  </Button>\n                </Box>\n              </Box>\n            )}\n          </Box>\n        );\n\n      default:\n        return 'Unknown step';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <Typography>Loading...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Create New Order\n      </Typography>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      <Paper sx={{ p: 3 }}>\n        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>\n          {steps.map((label) => (\n            <Step key={label}>\n              <StepLabel>{label}</StepLabel>\n            </Step>\n          ))}\n        </Stepper>\n\n        <Box sx={{ minHeight: 400 }}>\n          {renderStepContent(activeStep)}\n        </Box>\n\n        <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>\n          <Button\n            color=\"inherit\"\n            disabled={activeStep === 0}\n            onClick={handleBack}\n            sx={{ mr: 1 }}\n          >\n            Back\n          </Button>\n          <Box sx={{ flex: '1 1 auto' }} />\n          {activeStep < steps.length - 1 && (\n            <Button\n              onClick={handleNext}\n              disabled={\n                loading ||\n                (activeStep === 0 && !selectedCategory) ||\n                (activeStep === 1 && !selectedProduct) ||\n                (activeStep === 2 && (!priceCalculation || quantity < (selectedProduct?.min_quantity || 1))) ||\n                (activeStep === 3 && !createdOrder)\n              }\n            >\n              {loading ? 'Creating Order...' : 'Next'}\n            </Button>\n          )}\n          {activeStep === steps.length - 1 && (\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSubmitOrder}\n              disabled={submittingOrder || !createdOrder}\n            >\n              {submittingOrder ? 'Submitting...' : 'Complete Order'}\n            </Button>\n          )}\n        </Box>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default Order;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,SAAS,EAETC,OAAO,QAKF,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAoG,gCAAgC;AAC1J,OAAOC,UAAU,MAAM,uCAAuC;AAC9D,OAAOC,aAAa,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,KAAK,GAAG,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,cAAc,EAAE,iBAAiB,CAAC;AAEzG,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAqB,EAAE,CAAC;EACpE,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAoB,EAAE,CAAC;EAC/D,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAA0B,IAAI,CAAC;EACvF,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAyB,IAAI,CAAC;EACpF,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAkB,CAAC,CAAC;EAC5D,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAsB,CAAC,CAAC,CAAC;EAC/E,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAA0B,IAAI,CAAC;EACvF,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAuB,IAAI,CAAC;EAC5E,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAc,EAAE,CAAC;EACnE,MAAM,CAACwD,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACdyD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkB,IAAI,GAAG,MAAM1C,eAAe,CAAC2C,aAAa,CAAC,CAAC;MAClD7B,aAAa,CAAC4B,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZlB,QAAQ,CAAC,2BAA2B,CAAC;IACvC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,YAAY,GAAG,MAAOC,YAAoB,IAAK;IACnD,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkB,IAAI,GAAG,MAAM1C,eAAe,CAAC+C,WAAW,CAACD,YAAY,CAAC;MAC5D9B,WAAW,CAAC0B,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZlB,QAAQ,CAAC,yBAAyB,CAAC;IACrC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,oBAAoB,GAAG,MAAOC,QAA0B,IAAK;IACjE/B,mBAAmB,CAAC+B,QAAQ,CAAC;IAC7B,MAAMJ,YAAY,CAACI,QAAQ,CAACC,IAAI,CAAC;IACjCtC,aAAa,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,MAAMuC,mBAAmB,GAAIC,OAAwB,IAAK;IACxDhC,kBAAkB,CAACgC,OAAO,CAAC;IAC3B;IACA;IACA,MAAMC,eAAe,GAAGD,OAAO,CAACE,YAAY,IAAIF,OAAO,CAACE,YAAY,GAAG,CAAC,GAAGF,OAAO,CAACE,YAAY,GAAG,CAAC;IACnG1B,WAAW,CAACyB,eAAe,CAAC;IAC5BvB,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACtBE,mBAAmB,CAAC,IAAI,CAAC;IACzBpB,aAAa,CAAC,CAAC,CAAC;;IAEhB;IACA2C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/BC,IAAI,EAAEL,OAAO,CAACK,IAAI;MAClBH,YAAY,EAAEF,OAAO,CAACE,YAAY;MAClCI,YAAY,EAAEN,OAAO,CAACM,YAAY;MAClCL,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMM,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACxC,eAAe,EAAE;;IAEtB;IACA,MAAMyC,eAAe,GAAG,OAAOjC,QAAQ,KAAK,QAAQ,GAAGkC,QAAQ,CAAClC,QAAQ,EAAE,EAAE,CAAC,GAAGA,QAAQ;IACxF,IAAImC,KAAK,CAACF,eAAe,CAAC,IAAIA,eAAe,IAAIzC,eAAe,CAACmC,YAAY,IAAI,CAAC,CAAC,EAAE;IAErFpB,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACF,MAAM6B,MAAM,GAAG,MAAM/D,eAAe,CAAC2D,cAAc,CACjDxC,eAAe,CAAC6C,EAAE,EAClBJ,eAAe,EACf/B,eACF,CAAC;MACDG,mBAAmB,CAAC+B,MAAM,CAAC;IAC7B,CAAC,CAAC,OAAOnB,GAAG,EAAE;MACZlB,QAAQ,CAAC,2BAA2B,CAAC;MACrC6B,OAAO,CAAC9B,KAAK,CAAC,0BAA0B,EAAEmB,GAAG,CAAC;IAChD,CAAC,SAAS;MACRV,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACAlD,SAAS,CAAC,MAAM;IACd,MAAM4E,eAAe,GAAG,OAAOjC,QAAQ,KAAK,QAAQ,GAAGkC,QAAQ,CAAClC,QAAQ,EAAE,EAAE,CAAC,GAAGA,QAAQ;IACxF,IAAIR,eAAe,IAAI,CAAC2C,KAAK,CAACF,eAAe,CAAC,IAAIA,eAAe,KAAKzC,eAAe,CAACmC,YAAY,IAAI,CAAC,CAAC,EAAE;MACxG,MAAMW,SAAS,GAAGC,UAAU,CAAC,MAAM;QACjCP,cAAc,CAAC,CAAC;MAClB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;MAET,OAAO,MAAMQ,YAAY,CAACF,SAAS,CAAC;IACtC,CAAC,MAAM;MACLjC,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC,EAAE,CAACb,eAAe,EAAEQ,QAAQ,EAAEE,eAAe,CAAC,CAAC;EAEhD,MAAMuC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B;IACA,IAAIzD,UAAU,KAAK,CAAC,IAAIQ,eAAe,IAAIY,gBAAgB,EAAE;MAC3D,MAAM6B,eAAe,GAAG,OAAOjC,QAAQ,KAAK,QAAQ,GAAGkC,QAAQ,CAAClC,QAAQ,EAAE,EAAE,CAAC,GAAGA,QAAQ;MACxF,MAAM0C,SAAoB,GAAG;QAC3BC,UAAU,EAAEnD,eAAe,CAAC6C,EAAE;QAC9BrC,QAAQ,EAAEiC,eAAe;QACzBW,gBAAgB,EAAE1C,eAAe;QACjC2C,cAAc,EAAErD,eAAe,CAACqD;MAClC,CAAC;MACDlD,aAAa,CAAC,CAAC+C,SAAS,CAAC,CAAC;;MAE1B;MACA,MAAMI,WAAW,CAAC,CAACJ,SAAS,CAAC,CAAC;IAChC;IAEAzD,aAAa,CAAE8D,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB/D,aAAa,CAAE8D,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;EAED,MAAMD,WAAW,GAAG,MAAOG,KAAkB,IAAK;IAChD,IAAI;MACFpD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqD,SAAS,GAAG;QAChBD,KAAK;QACLE,oBAAoB,EAAE,EAAE;QACxBC,eAAe,EAAE;MACnB,CAAC;MAED,MAAMC,KAAK,GAAG,MAAMhF,eAAe,CAACyE,WAAW,CAACI,SAAS,CAAC;MAC1DzC,eAAe,CAAC4C,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOpC,GAAQ,EAAE;MACjBlB,QAAQ,CAACkB,GAAG,CAACqC,OAAO,IAAI,wBAAwB,CAAC;MACjD1B,OAAO,CAAC9B,KAAK,CAAC,uBAAuB,EAAEmB,GAAG,CAAC;IAC7C,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0D,mBAAmB,GAAIC,KAAkB,IAAK;IAClD7C,gBAAgB,CAAC8C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGD,KAAK,CAAC,CAAC;EAC/C,CAAC;EAED,MAAME,qBAAqB,GAAI5D,KAAa,IAAK;IAC/CC,QAAQ,CAACD,KAAK,CAAC;EACjB,CAAC;EAED,MAAM6D,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACnD,YAAY,EAAE;MACjBT,QAAQ,CAAC,oBAAoB,CAAC;MAC9B;IACF;IAEA,IAAI;MACFc,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACA9B,QAAQ,CAAC,mBAAmB,CAAC;IAC/B,CAAC,CAAC,OAAOkC,GAAQ,EAAE;MACjBlB,QAAQ,CAACkB,GAAG,CAACqC,OAAO,IAAI,wBAAwB,CAAC;IACnD,CAAC,SAAS;MACRzC,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAM+C,iBAAiB,GAAIC,IAAY,IAAK;IAAA,IAAAC,qBAAA;IAC1C,QAAQD,IAAI;MACV,KAAK,CAAC;QACJ,oBACEpF,OAAA,CAACnB,GAAG;UAAAyG,QAAA,gBACFtF,OAAA,CAAClB,UAAU;YAACyG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5F,OAAA,CAACnB,GAAG;YACFgH,EAAE,EAAE;cACFC,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE;gBACnBC,EAAE,EAAE,KAAK;gBACTC,EAAE,EAAE,gBAAgB;gBACpBC,EAAE,EAAE;cACN,CAAC;cACDC,GAAG,EAAE;YACP,CAAE;YAAAb,QAAA,EAED7E,UAAU,CAAC2F,GAAG,CAAEvD,QAAQ,iBACvB7C,OAAA,CAACZ,IAAI;cAEHyG,EAAE,EAAE;gBAAEQ,MAAM,EAAE,SAAS;gBAAE,SAAS,EAAE;kBAAEC,SAAS,EAAE;gBAAE;cAAE,CAAE;cACvDC,OAAO,EAAEA,CAAA,KAAM3D,oBAAoB,CAACC,QAAQ,CAAE;cAAAyC,QAAA,GAE7CzC,QAAQ,CAAC2D,KAAK,iBACbxG,OAAA,CAACV,SAAS;gBACRmH,SAAS,EAAC,KAAK;gBACfC,MAAM,EAAC,KAAK;gBACZF,KAAK,EAAE3D,QAAQ,CAAC2D,KAAM;gBACtBG,GAAG,EAAE9D,QAAQ,CAACQ;cAAK;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACF,eACD5F,OAAA,CAACX,WAAW;gBAAAiG,QAAA,gBACVtF,OAAA,CAAClB,UAAU;kBAAC0G,YAAY;kBAACD,OAAO,EAAC,IAAI;kBAACkB,SAAS,EAAC,KAAK;kBAAAnB,QAAA,EAClDzC,QAAQ,CAACQ;gBAAI;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACb5F,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,OAAO;kBAACqB,KAAK,EAAC,gBAAgB;kBAAAtB,QAAA,EAC/CzC,QAAQ,CAACgE;gBAAW;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,EACZ/C,QAAQ,CAACiE,cAAc,KAAKC,SAAS,iBACpC/G,OAAA,CAACT,IAAI;kBACHyH,KAAK,EAAE,GAAGnE,QAAQ,CAACiE,cAAc,WAAY;kBAC7CG,IAAI,EAAC,OAAO;kBACZpB,EAAE,EAAE;oBAAEqB,EAAE,EAAE;kBAAE;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA,GA1BT/C,QAAQ,CAACe,EAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2BZ,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACE5F,OAAA,CAACnB,GAAG;UAAAyG,QAAA,gBACFtF,OAAA,CAAClB,UAAU;YAACyG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,GAAC,wBACd,EAACzE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwC,IAAI;UAAA;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACb5F,OAAA,CAACnB,GAAG;YACFgH,EAAE,EAAE;cACFC,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE;gBACnBC,EAAE,EAAE,KAAK;gBACTC,EAAE,EAAE,gBAAgB;gBACpBC,EAAE,EAAE;cACN,CAAC;cACDC,GAAG,EAAE;YACP,CAAE;YAAAb,QAAA,EAED3E,QAAQ,CAACyF,GAAG,CAAEpD,OAAO,iBACpBhD,OAAA,CAACZ,IAAI;cAEHyG,EAAE,EAAE;gBAAEQ,MAAM,EAAE,SAAS;gBAAE,SAAS,EAAE;kBAAEC,SAAS,EAAE;gBAAE;cAAE,CAAE;cACvDC,OAAO,EAAEA,CAAA,KAAMxD,mBAAmB,CAACC,OAAO,CAAE;cAAAsC,QAAA,GAE3CtC,OAAO,CAACwD,KAAK,iBACZxG,OAAA,CAACV,SAAS;gBACRmH,SAAS,EAAC,KAAK;gBACfC,MAAM,EAAC,KAAK;gBACZF,KAAK,EAAExD,OAAO,CAACwD,KAAM;gBACrBG,GAAG,EAAE3D,OAAO,CAACK;cAAK;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CACF,eACD5F,OAAA,CAACX,WAAW;gBAAAiG,QAAA,gBACVtF,OAAA,CAAClB,UAAU;kBAAC0G,YAAY;kBAACD,OAAO,EAAC,IAAI;kBAACkB,SAAS,EAAC,KAAK;kBAAAnB,QAAA,EAClDtC,OAAO,CAACK;gBAAI;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACb5F,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,OAAO;kBAACqB,KAAK,EAAC,gBAAgB;kBAACf,EAAE,EAAE;oBAAEsB,EAAE,EAAE;kBAAE,CAAE;kBAAA7B,QAAA,EAC9DtC,OAAO,CAAC6D;gBAAW;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACb5F,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,IAAI;kBAACqB,KAAK,EAAC,SAAS;kBAAAtB,QAAA,EACrCtC,OAAO,CAACoE;gBAAoB;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACb5F,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,SAAS;kBAACO,OAAO,EAAC,OAAO;kBAAAR,QAAA,GAAC,OACvC,EAACtC,OAAO,CAACE,YAAY,EAAC,iBAAe,EAACF,OAAO,CAACqE,oBAAoB,EAAC,OAC1E;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,GAzBT5C,OAAO,CAACY,EAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BX,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACE5F,OAAA,CAACnB,GAAG;UAAAyG,QAAA,gBACFtF,OAAA,CAAClB,UAAU;YAACyG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZ7E,eAAe,iBACdf,OAAA,CAACnB,GAAG;YAACgH,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEwB,aAAa,EAAE;gBAAEtB,EAAE,EAAE,QAAQ;gBAAEE,EAAE,EAAE;cAAM,CAAC;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAE/EtF,OAAA,CAACnB,GAAG;cAACgH,EAAE,EAAE;gBAAE0B,IAAI,EAAE;cAAE,CAAE;cAAAjC,QAAA,eACnBtF,OAAA,CAACb,KAAK;gBAAC0G,EAAE,EAAE;kBAAE2B,CAAC,EAAE;gBAAE,CAAE;gBAAAlC,QAAA,gBAClBtF,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAF,QAAA,EAClCvE,eAAe,CAACsC;gBAAI;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACb5F,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,OAAO;kBAACqB,KAAK,EAAC,gBAAgB;kBAACf,EAAE,EAAE;oBAAEsB,EAAE,EAAE;kBAAE,CAAE;kBAAA7B,QAAA,EAC9DvE,eAAe,CAAC8F;gBAAW;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAGb5F,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAAAF,QAAA,EAAC;gBAE7C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5F,OAAA,CAACnB,GAAG;kBAACgH,EAAE,EAAE;oBAAEsB,EAAE,EAAE;kBAAE,CAAE;kBAAA7B,QAAA,EAChBmC,MAAM,CAACC,OAAO,CAAC3G,eAAe,CAACqD,cAAc,IAAI,CAAC,CAAC,CAAC,CAACgC,GAAG,CAAC,CAAC,CAACuB,GAAG,EAAEC,KAAK,CAAC,kBACrE5H,OAAA,CAAClB,UAAU;oBAAWyG,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEsB,EAAE,EAAE;oBAAI,CAAE;oBAAA7B,QAAA,gBACpDtF,OAAA;sBAAAsF,QAAA,GAASqC,GAAG,EAAC,GAAC;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACgC,KAAK;kBAAA,GADfD,GAAG;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAER,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN5F,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,OAAO;kBAACqB,KAAK,EAAC,gBAAgB;kBAAAtB,QAAA,GAAC,mBAChC,EAACvE,eAAe,CAACsG,oBAAoB,EAAC,OACzD;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGN5F,OAAA,CAACnB,GAAG;cAACgH,EAAE,EAAE;gBAAE0B,IAAI,EAAE;cAAE,CAAE;cAAAjC,QAAA,eACnBtF,OAAA,CAACb,KAAK;gBAAC0G,EAAE,EAAE;kBAAE2B,CAAC,EAAE;gBAAE,CAAE;gBAAAlC,QAAA,gBAClBtF,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAF,QAAA,EAAC;gBAEtC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAGb5F,OAAA,CAACP,SAAS;kBACRuH,KAAK,EAAC,UAAU;kBAChBa,IAAI,EAAC,MAAM;kBACXD,KAAK,EAAErG,QAAS;kBAChBuG,QAAQ,EAAGC,CAAC,IAAK;oBACf,MAAMC,UAAU,GAAGD,CAAC,CAACE,MAAM,CAACL,KAAK;;oBAEjC;oBACA,IAAII,UAAU,KAAK,EAAE,EAAE;sBACrBxG,WAAW,CAAC,EAAE,CAAC;sBACf;oBACF;;oBAEA;oBACA,IAAI,CAAC,OAAO,CAAC0G,IAAI,CAACF,UAAU,CAAC,EAAE;sBAC7B;oBACF;oBAEA,MAAMG,YAAY,GAAG1E,QAAQ,CAACuE,UAAU,EAAE,EAAE,CAAC;oBAC7C,MAAMI,MAAM,GAAGrH,eAAe,CAACmC,YAAY,IAAInC,eAAe,CAACmC,YAAY,GAAG,CAAC,GAAGnC,eAAe,CAACmC,YAAY,GAAG,CAAC;oBAClH,MAAMmF,MAAM,GAAGtH,eAAe,CAACuC,YAAY;;oBAE3C;oBACA,IAAIgF,iBAAiB,GAAGC,IAAI,CAACC,GAAG,CAACJ,MAAM,EAAED,YAAY,CAAC;oBACtD,IAAIE,MAAM,IAAIC,iBAAiB,GAAGD,MAAM,EAAE;sBACxCC,iBAAiB,GAAGD,MAAM;oBAC5B;oBAEA7G,WAAW,CAAC8G,iBAAiB,CAAC;kBAChC,CAAE;kBACFG,MAAM,EAAGV,CAAC,IAAK;oBACb;oBACA,MAAMW,YAAY,GAAGX,CAAC,CAACE,MAAM,CAACL,KAAK;oBACnC,IAAIc,YAAY,KAAK,EAAE,IAAIhF,KAAK,CAACD,QAAQ,CAACiF,YAAY,EAAE,EAAE,CAAC,CAAC,EAAE;sBAC5D,MAAMC,gBAAgB,GAAG5H,eAAe,CAACmC,YAAY,IAAInC,eAAe,CAACmC,YAAY,GAAG,CAAC,GAAGnC,eAAe,CAACmC,YAAY,GAAG,CAAC;sBAC5H1B,WAAW,CAACmH,gBAAgB,CAAC;oBAC/B;kBACF,CAAE;kBACFC,SAAS,EAAGb,CAAC,IAAK;oBAChB;oBACA,IAAI,CACF,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EACjD,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAC/C,MAAM,EAAE,KAAK,CACd,CAACc,QAAQ,CAACd,CAAC,CAACJ,GAAG,CAAC,EAAE;sBACjB;oBACF;;oBAEA;oBACA,IAAII,CAAC,CAACe,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACD,QAAQ,CAACd,CAAC,CAACJ,GAAG,CAACoB,WAAW,CAAC,CAAC,CAAC,EAAE;sBACxE;oBACF;;oBAEA;oBACA,IAAI,CAAC,MAAM,CAACb,IAAI,CAACH,CAAC,CAACJ,GAAG,CAAC,EAAE;sBACvBI,CAAC,CAACiB,cAAc,CAAC,CAAC;oBACpB;kBACF,CAAE;kBACFC,SAAS,EAAE;oBACTC,SAAS,EAAE;sBACTC,SAAS,EAAE,SAAS;sBACpBC,OAAO,EAAE,QAAQ;sBACjBC,YAAY,EAAE,KAAK;sBACnBC,KAAK,EAAE;wBACLC,QAAQ,EAAE,MAAM;wBAAE;wBAClBC,SAAS,EAAE;sBACb;oBACF;kBACF,CAAE;kBACFC,UAAU,EAAE,QAAQ1I,eAAe,CAACmC,YAAY,IAAI,CAAC,GAAGnC,eAAe,CAACuC,YAAY,GAAG,UAAUvC,eAAe,CAACuC,YAAY,EAAE,GAAG,EAAE,2BAA4B;kBAChKoG,SAAS;kBACT7D,EAAE,EAAE;oBACFsB,EAAE,EAAE,CAAC;oBACL,0BAA0B,EAAE;sBAC1B,kBAAkB,EAAE;wBAClBwC,WAAW,EAAE;sBACf,CAAC;sBACD,wBAAwB,EAAE;wBACxBA,WAAW,EAAE;sBACf;oBACF;kBACF;gBAAE;kBAAAlE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGD,EAAAP,qBAAA,GAAAtE,eAAe,CAAC6I,OAAO,cAAAvE,qBAAA,uBAAvBA,qBAAA,CAAyBwE,gBAAgB,kBACxC7J,OAAA,CAACnB,GAAG;kBAACgH,EAAE,EAAE;oBAAEsB,EAAE,EAAE;kBAAE,CAAE;kBAAA7B,QAAA,gBACjBtF,OAAA,CAAClB,UAAU;oBAACyG,OAAO,EAAC,WAAW;oBAACC,YAAY;oBAAAF,QAAA,EAAC;kBAE7C;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5F,OAAA,CAACnB,GAAG;oBAACgH,EAAE,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEwB,aAAa,EAAE,QAAQ;sBAAEnB,GAAG,EAAE;oBAAE,CAAE;oBAAAb,QAAA,EAC3DvE,eAAe,CAAC6I,OAAO,CAACC,gBAAgB,CAACzD,GAAG,CAAC,CAAC0D,IAAS,EAAEC,KAAa,KAAK;sBAAA,IAAAC,sBAAA;sBAC1E,MAAMxG,eAAe,GAAG,OAAOjC,QAAQ,KAAK,QAAQ,GAAGkC,QAAQ,CAAClC,QAAQ,EAAE,EAAE,CAAC,GAAGA,QAAQ;sBACxF,MAAM0I,aAAa,GAAG,CAACvG,KAAK,CAACF,eAAe,CAAC,IAAIA,eAAe,IAAIsG,IAAI,CAAC5G,YAAY,KAClF6G,KAAK,KAAKhJ,eAAe,CAAC6I,OAAO,CAACC,gBAAgB,CAACK,MAAM,GAAG,CAAC,IAC7D1G,eAAe,KAAAwG,sBAAA,GAAGjJ,eAAe,CAAC6I,OAAO,CAACC,gBAAgB,CAACE,KAAK,GAAG,CAAC,CAAC,cAAAC,sBAAA,uBAAnDA,sBAAA,CAAqD9G,YAAY,EAAC;sBAEvF,oBACElD,OAAA,CAACnB,GAAG;wBAEFgH,EAAE,EAAE;0BACF2B,CAAC,EAAE,GAAG;0BACN2C,YAAY,EAAE,CAAC;0BACfC,MAAM,EAAE,WAAW;0BACnBT,WAAW,EAAEM,aAAa,GAAG,cAAc,GAAG,UAAU;0BACxDI,eAAe,EAAEJ,aAAa,GAAG,YAAY,GAAG,aAAa;0BAC7DK,UAAU,EAAE;wBACd,CAAE;wBAAAhF,QAAA,eAEFtF,OAAA,CAAClB,UAAU;0BACTyG,OAAO,EAAC,OAAO;0BACfM,EAAE,EAAE;4BACF0E,UAAU,EAAEN,aAAa,GAAG,GAAG,GAAG,GAAG;4BACrCrD,KAAK,EAAEqD,aAAa,GAAG,cAAc,GAAG;0BAC1C,CAAE;0BAAA3E,QAAA,GAEDwE,IAAI,CAAC5G,YAAY,EAAC,cAAY,EAAC4G,IAAI,CAACU,cAAc,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,OAC/D,EAACR,aAAa,iBACZjK,OAAA,CAACT,IAAI;4BACHyH,KAAK,EAAC,SAAS;4BACfC,IAAI,EAAC,OAAO;4BACZL,KAAK,EAAC,SAAS;4BACff,EAAE,EAAE;8BAAE6E,EAAE,EAAE,CAAC;8BAAEhE,MAAM,EAAE;4BAAG;0BAAE;4BAAAjB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3B,CACF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACS;sBAAC,GA1BRmE,KAAK;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2BP,CAAC;oBAEV,CAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAED5F,OAAA,CAACN,OAAO;kBAACmG,EAAE,EAAE;oBAAE8E,EAAE,EAAE;kBAAE;gBAAE;kBAAAlF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAG1B5F,OAAA,CAACnB,GAAG;kBAAAyG,QAAA,gBACFtF,OAAA,CAAClB,UAAU;oBAACyG,OAAO,EAAC,IAAI;oBAACC,YAAY;oBAAAF,QAAA,EAAC;kBAEtC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EAGZ/D,gBAAgB,gBACf7B,OAAA,CAACnB,GAAG;oBAACgH,EAAE,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAE8E,UAAU,EAAE,QAAQ;sBAAEzE,GAAG,EAAE;oBAAE,CAAE;oBAAAb,QAAA,eACzDtF,OAAA,CAAClB,UAAU;sBAACyG,OAAO,EAAC,OAAO;sBAAAD,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC,GACJjE,gBAAgB,gBAClB3B,OAAA,CAACnB,GAAG;oBAAAyG,QAAA,gBACFtF,OAAA,CAAClB,UAAU;sBAACyG,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAC,iBACX,EAAC3D,gBAAgB,CAACkJ,UAAU,CAACJ,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACb5F,OAAA,CAAClB,UAAU;sBAACyG,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAC,YAChB,EAAC,OAAO/D,QAAQ,KAAK,QAAQ,GAAGkC,QAAQ,CAAClC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,GAAGA,QAAQ;oBAAA;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC,eACb5F,OAAA,CAAClB,UAAU;sBAACyG,OAAO,EAAC,IAAI;sBAACqB,KAAK,EAAC,SAAS;sBAACf,EAAE,EAAE;wBAAEqB,EAAE,EAAE;sBAAE,CAAE;sBAAA5B,QAAA,GAAC,SAC/C,EAAC3D,gBAAgB,CAACmJ,qBAAqB;oBAAA;sBAAArF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACb5F,OAAA,CAAClB,UAAU;sBAACyG,OAAO,EAAC,SAAS;sBAACqB,KAAK,EAAC,gBAAgB;sBAACf,EAAE,EAAE;wBAAEqB,EAAE,EAAE,CAAC;wBAAEpB,OAAO,EAAE;sBAAQ,CAAE;sBAAAR,QAAA,GAAC,6BACzD,EAAC3D,gBAAgB,CAAC0F,oBAAoB,EAAC,OACpE;oBAAA;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,GACJ,CAAC,MAAM;oBACP,MAAMpC,eAAe,GAAG,OAAOjC,QAAQ,KAAK,QAAQ,GAAGkC,QAAQ,CAAClC,QAAQ,EAAE,EAAE,CAAC,GAAGA,QAAQ;oBACxF,OAAO,CAACmC,KAAK,CAACF,eAAe,CAAC,IAAIA,eAAe,KAAK,CAAAzC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmC,YAAY,KAAI,CAAC,CAAC;kBAC3F,CAAC,EAAE,CAAC,gBACJlD,OAAA,CAAClB,UAAU;oBAACyG,OAAO,EAAC,OAAO;oBAACqB,KAAK,EAAC,gBAAgB;oBAAAtB,QAAA,EAAC;kBAEnD;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,gBAEb5F,OAAA,CAAClB,UAAU;oBAACyG,OAAO,EAAC,OAAO;oBAACqB,KAAK,EAAC,OAAO;oBAAAtB,QAAA,GAAC,0CACA,EAAC,CAAAvE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmC,YAAY,KAAI,CAAC,EAAC,GAC9E;kBAAA;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACE5F,OAAA,CAACnB,GAAG;UAAAyG,QAAA,gBACFtF,OAAA,CAAClB,UAAU;YAACyG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5F,OAAA,CAAClB,UAAU;YAACyG,OAAO,EAAC,OAAO;YAACqB,KAAK,EAAC,gBAAgB;YAACf,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE,CAAE;YAAA7B,QAAA,EAAC;UAElE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZ7D,YAAY,gBACX/B,OAAA,CAACF,aAAa;YACZiL,QAAQ,eACN/K,OAAA,CAACR,KAAK;cAACwL,QAAQ,EAAC,OAAO;cAACnF,EAAE,EAAE;gBAAEsB,EAAE,EAAE;cAAE,CAAE;cAAA7B,QAAA,EAAC;YAEvC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;YAAAN,QAAA,eAEDtF,OAAA,CAACH,UAAU;cACToL,OAAO,EAAElJ,YAAY,CAAC6B,EAAG;cACzBsH,eAAe,EAAEpG,mBAAoB;cACrCqG,OAAO,EAAElG;YAAsB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC,gBAEhB5F,OAAA,CAACR,KAAK;YAACwL,QAAQ,EAAC,SAAS;YAACnF,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE,CAAE;YAAA7B,QAAA,EAAC;UAEzC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACE5F,OAAA,CAACnB,GAAG;UAAAyG,QAAA,gBACFtF,OAAA,CAAClB,UAAU;YAACyG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZ7D,YAAY,iBACX/B,OAAA,CAACnB,GAAG;YAACgH,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,mBAAmB,EAAE;gBAAEC,EAAE,EAAE,KAAK;gBAAEE,EAAE,EAAE;cAAU,CAAC;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAEtFtF,OAAA,CAACb,KAAK;cAAC0G,EAAE,EAAE;gBAAE2B,CAAC,EAAE;cAAE,CAAE;cAAAlC,QAAA,gBAClBtF,OAAA,CAAClB,UAAU;gBAACyG,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAF,QAAA,EAAC;cAEtC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5F,OAAA,CAAClB,UAAU;gBAACyG,OAAO,EAAC,OAAO;gBAACqB,KAAK,EAAC,gBAAgB;gBAACpB,YAAY;gBAAAF,QAAA,GAAC,SACvD,EAACvD,YAAY,CAACqJ,YAAY;cAAA;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,EAEZ7E,eAAe,iBACdf,OAAA,CAACnB,GAAG;gBAACgH,EAAE,EAAE;kBAAEqB,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,gBACjBtF,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,WAAW;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrD5F,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAEvE,eAAe,CAACsC;gBAAI;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAE/D5F,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,WAAW;kBAACM,EAAE,EAAE;oBAAEqB,EAAE,EAAE;kBAAE,CAAE;kBAAA5B,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrE5F,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAE,OAAO/D,QAAQ,KAAK,QAAQ,GAAGkC,QAAQ,CAAClC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,GAAGA;gBAAQ;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,EAE/GjE,gBAAgB,iBACf3B,OAAA,CAAAE,SAAA;kBAAAoF,QAAA,gBACEtF,OAAA,CAAClB,UAAU;oBAACyG,OAAO,EAAC,WAAW;oBAACM,EAAE,EAAE;sBAAEqB,EAAE,EAAE;oBAAE,CAAE;oBAAA5B,QAAA,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxE5F,OAAA,CAAClB,UAAU;oBAACyG,OAAO,EAAC,IAAI;oBAACqB,KAAK,EAAC,SAAS;oBAAAtB,QAAA,EACrC3D,gBAAgB,CAACmJ;kBAAqB;oBAAArF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA,eACb,CACH,eAED5F,OAAA,CAAClB,UAAU;kBAACyG,OAAO,EAAC,WAAW;kBAACM,EAAE,EAAE;oBAAEqB,EAAE,EAAE;kBAAE,CAAE;kBAAA5B,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnE5F,OAAA,CAACT,IAAI;kBACHyH,KAAK,EAAEjF,YAAY,CAACsJ,MAAM,IAAI,SAAU;kBACxCzE,KAAK,EAAC,SAAS;kBACfK,IAAI,EAAC;gBAAO;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGR5F,OAAA,CAACb,KAAK;cAAC0G,EAAE,EAAE;gBAAE2B,CAAC,EAAE;cAAE,CAAE;cAAAlC,QAAA,gBAClBtF,OAAA,CAAClB,UAAU;gBAACyG,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAF,QAAA,GAAC,kBACpB,EAACrD,aAAa,CAACiI,MAAM,EAAC,GACxC;cAAA;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEZ3D,aAAa,CAACiI,MAAM,GAAG,CAAC,gBACvBlK,OAAA,CAACnB,GAAG;gBAAAyG,QAAA,EACDrD,aAAa,CAACmE,GAAG,CAAC,CAACkF,IAAI,EAAEvB,KAAK,kBAC7B/J,OAAA,CAACnB,GAAG;kBAAegH,EAAE,EAAE;oBAAEsB,EAAE,EAAE,CAAC;oBAAEK,CAAC,EAAE,CAAC;oBAAE+D,OAAO,EAAE,SAAS;oBAAEpB,YAAY,EAAE;kBAAE,CAAE;kBAAA7E,QAAA,gBAC1EtF,OAAA,CAAClB,UAAU;oBAACyG,OAAO,EAAC,OAAO;oBAACiG,MAAM;oBAAAlG,QAAA,EAC/BgG,IAAI,CAACG;kBAAa;oBAAAhG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACb5F,OAAA,CAAClB,UAAU;oBAACyG,OAAO,EAAC,SAAS;oBAACqB,KAAK,EAAC,gBAAgB;oBAAAtB,QAAA,GACjDgG,IAAI,CAACI,mBAAmB,EAAC,UAAG,EAACJ,IAAI,CAACK,eAAe;kBAAA;oBAAAlG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA,GANL0F,IAAI,CAAC1H,EAAE;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOZ,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAEN5F,OAAA,CAACR,KAAK;gBAACwL,QAAQ,EAAC,SAAS;gBAAA1F,QAAA,EAAC;cAE1B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGR5F,OAAA,CAACnB,GAAG;cAACgH,EAAE,EAAE;gBAAE+F,UAAU,EAAE,QAAQ;gBAAEpC,SAAS,EAAE,QAAQ;gBAAEtC,EAAE,EAAE;cAAE,CAAE;cAAA5B,QAAA,eAC5DtF,OAAA,CAACd,MAAM;gBACLqG,OAAO,EAAC,WAAW;gBACnB0B,IAAI,EAAC,OAAO;gBACZV,OAAO,EAAErB,iBAAkB;gBAC3B2G,QAAQ,EAAE1J,eAAgB;gBAAAmD,QAAA,EAEzBnD,eAAe,GAAG,eAAe,GAAG;cAAc;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV;QACE,OAAO,cAAc;IACzB;EACF,CAAC;EAED,IAAIzE,OAAO,EAAE;IACX,oBACEnB,OAAA,CAACnB,GAAG;MAACiH,OAAO,EAAC,MAAM;MAACgG,cAAc,EAAC,QAAQ;MAAClB,UAAU,EAAC,QAAQ;MAACmB,SAAS,EAAC,OAAO;MAAAzG,QAAA,eAC/EtF,OAAA,CAAClB,UAAU;QAAAwG,QAAA,EAAC;MAAU;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAEV;EAEA,oBACE5F,OAAA,CAACnB,GAAG;IAAAyG,QAAA,gBACFtF,OAAA,CAAClB,UAAU;MAACyG,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZvE,KAAK,iBACJrB,OAAA,CAACR,KAAK;MAACwL,QAAQ,EAAC,OAAO;MAACnF,EAAE,EAAE;QAAEsB,EAAE,EAAE;MAAE,CAAE;MAAA7B,QAAA,EACnCjE;IAAK;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAED5F,OAAA,CAACb,KAAK;MAAC0G,EAAE,EAAE;QAAE2B,CAAC,EAAE;MAAE,CAAE;MAAAlC,QAAA,gBAClBtF,OAAA,CAACjB,OAAO;QAACwB,UAAU,EAAEA,UAAW;QAACsF,EAAE,EAAE;UAAEsB,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,EAC5CnF,KAAK,CAACiG,GAAG,CAAEY,KAAK,iBACfhH,OAAA,CAAChB,IAAI;UAAAsG,QAAA,eACHtF,OAAA,CAACf,SAAS;YAAAqG,QAAA,EAAE0B;UAAK;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC,GADrBoB,KAAK;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEV5F,OAAA,CAACnB,GAAG;QAACgH,EAAE,EAAE;UAAEkG,SAAS,EAAE;QAAI,CAAE;QAAAzG,QAAA,EACzBH,iBAAiB,CAAC5E,UAAU;MAAC;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eAEN5F,OAAA,CAACnB,GAAG;QAACgH,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEwB,aAAa,EAAE,KAAK;UAAE0E,EAAE,EAAE;QAAE,CAAE;QAAA1G,QAAA,gBACxDtF,OAAA,CAACd,MAAM;UACL0H,KAAK,EAAC,SAAS;UACfiF,QAAQ,EAAEtL,UAAU,KAAK,CAAE;UAC3BgG,OAAO,EAAEhC,UAAW;UACpBsB,EAAE,EAAE;YAAEoG,EAAE,EAAE;UAAE,CAAE;UAAA3G,QAAA,EACf;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5F,OAAA,CAACnB,GAAG;UAACgH,EAAE,EAAE;YAAE0B,IAAI,EAAE;UAAW;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAChCrF,UAAU,GAAGJ,KAAK,CAAC+J,MAAM,GAAG,CAAC,iBAC5BlK,OAAA,CAACd,MAAM;UACLqH,OAAO,EAAEvC,UAAW;UACpB6H,QAAQ,EACN1K,OAAO,IACNZ,UAAU,KAAK,CAAC,IAAI,CAACM,gBAAiB,IACtCN,UAAU,KAAK,CAAC,IAAI,CAACQ,eAAgB,IACrCR,UAAU,KAAK,CAAC,KAAK,CAACoB,gBAAgB,IAAIJ,QAAQ,IAAI,CAAAR,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmC,YAAY,KAAI,CAAC,CAAC,CAAE,IAC3F3C,UAAU,KAAK,CAAC,IAAI,CAACwB,YACvB;UAAAuD,QAAA,EAEAnE,OAAO,GAAG,mBAAmB,GAAG;QAAM;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CACT,EACArF,UAAU,KAAKJ,KAAK,CAAC+J,MAAM,GAAG,CAAC,iBAC9BlK,OAAA,CAACd,MAAM;UACLqG,OAAO,EAAC,WAAW;UACnBqB,KAAK,EAAC,SAAS;UACfL,OAAO,EAAErB,iBAAkB;UAC3B2G,QAAQ,EAAE1J,eAAe,IAAI,CAACJ,YAAa;UAAAuD,QAAA,EAE1CnD,eAAe,GAAG,eAAe,GAAG;QAAgB;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvF,EAAA,CAlsBID,KAAe;EAAA,QACFT,WAAW;AAAA;AAAAuM,EAAA,GADxB9L,KAAe;AAosBrB,eAAeA,KAAK;AAAC,IAAA8L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}