{"ast": null, "code": "import api,{endpoints}from'./api';class PrintingService{// Categories\nasync getCategories(){const response=await api.get(`${endpoints.printing}/categories`);return response.data.data;}// Products\nasync getProducts(categorySlug){const url=categorySlug?`${endpoints.printing}/products/category/${categorySlug}`:`${endpoints.printing}/products`;const response=await api.get(url);return response.data.data;}async getProduct(slug){const response=await api.get(`${endpoints.printing}/product/${slug}`);return response.data.data;}async calculatePrice(productId,quantity,options){const response=await api.post(`${endpoints.printing}/calculate-price`,{product_id:productId,quantity,options:options||{}});return response.data.data;}// Orders\nasync getOrders(){let page=arguments.length>0&&arguments[0]!==undefined?arguments[0]:1;const response=await api.get(`${endpoints.orders}?page=${page}`);return response.data;}async getOrder(id){const response=await api.get(`${endpoints.orders}/${id}`);return response.data.data;}async createOrder(orderData){const response=await api.post(endpoints.orders,orderData);return response.data.data;}async cancelOrder(id){const response=await api.post(`${endpoints.orders}/${id}/cancel`);return response.data.data;}async reorder(id){const response=await api.post(`${endpoints.orders}/${id}/reorder`);return response.data.data;}// File uploads\nasync uploadFiles(orderId,files){let fileType=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'artwork';const formData=new FormData();files.forEach(file=>{formData.append('files[]',file);});formData.append('file_type',fileType);const response=await api.post(`${endpoints.orders}/${orderId}/files`,formData,{headers:{'Content-Type':'multipart/form-data'}});return response.data.data;}async getOrderFiles(orderId){const response=await api.get(`${endpoints.orders}/${orderId}/files`);return response.data.data;}async deleteFile(orderId,fileId){await api.delete(`${endpoints.orders}/${orderId}/files/${fileId}`);}// Get file upload settings\nasync getUploadSettings(){const response=await api.get('/file-upload/settings');return response.data.data;}// File validation helpers (now uses dynamic settings)\nasync validateFile(file){try{var _file$name$split$pop;const settings=await this.getUploadSettings();const errors=[];const warnings=[];// Validate file size\nconst maxSizeMB=settings.max_file_size_mb||50;const maxSizeBytes=maxSizeMB*1024*1024;if(file.size>maxSizeBytes){errors.push(`File size must be less than ${maxSizeMB}MB`);}// Validate file type\nconst allowedTypes=settings.allowed_file_types||['pdf','png','jpg','jpeg'];const fileExtension=(_file$name$split$pop=file.name.split('.').pop())===null||_file$name$split$pop===void 0?void 0:_file$name$split$pop.toLowerCase();if(!fileExtension||!allowedTypes.includes(fileExtension)){errors.push(`File type '${fileExtension}' is not allowed. Allowed types: ${allowedTypes.join(', ')}`);}// For images, validate dimensions if possible\nif(file.type.startsWith('image/')&&settings.enable_dimension_validation){try{const dimensions=await this.getImageDimensions(file);if(dimensions){if(dimensions.width<settings.min_width_px||dimensions.height<settings.min_height_px){errors.push(`Image dimensions (${dimensions.width}x${dimensions.height}px) are below minimum requirements (${settings.min_width_px}x${settings.min_height_px}px)`);}if(dimensions.width>settings.max_width_px||dimensions.height>settings.max_height_px){errors.push(`Image dimensions (${dimensions.width}x${dimensions.height}px) exceed maximum allowed size (${settings.max_width_px}x${settings.max_height_px}px)`);}}}catch(e){// Ignore dimension validation errors\n}}return{valid:errors.length===0,message:errors.length>0?errors.join('; '):undefined,warnings:warnings.length>0?warnings:undefined};}catch(error){// Fallback to basic validation if settings can't be loaded\nconst maxSize=50*1024*1024;// 50MB\nconst allowedTypes=['image/png','image/jpeg','image/jpg','image/svg+xml','application/pdf'];if(file.size>maxSize){return{valid:false,message:'File size must be less than 50MB'};}if(!allowedTypes.includes(file.type)){return{valid:false,message:'File must be PNG, JPG, SVG, or PDF format'};}return{valid:true};}}// Helper to get image dimensions\ngetImageDimensions(file){return new Promise(resolve=>{if(!file.type.startsWith('image/')){resolve(null);return;}const img=new Image();img.onload=()=>{resolve({width:img.width,height:img.height});};img.onerror=()=>{resolve(null);};img.src=URL.createObjectURL(file);});}async validateImageDPI(file){return new Promise(resolve=>{if(!file.type.startsWith('image/')){resolve({});return;}const img=new Image();img.onload=()=>{const dimensions={width:img.naturalWidth,height:img.naturalHeight};// Estimate DPI based on common print sizes\nconst estimatedDPI=this.estimateDPI(dimensions);resolve({dimensions,dpi:estimatedDPI});};img.onerror=()=>resolve({});img.src=URL.createObjectURL(file);});}estimateDPI(dimensions){// Business card at 300 DPI: ~1050x600px\n// A5 flyer at 300 DPI: ~1240x1754px\n// This is a rough estimation\nconst{width,height}=dimensions;// Common print sizes at 300 DPI\nconst printSizes=[{width:1050,height:600,dpi:300},// Business card\n{width:1240,height:1754,dpi:300},// A5\n{width:2480,height:3508,dpi:300}// A4\n];for(const size of printSizes){const widthRatio=width/size.width;const heightRatio=height/size.height;if(Math.abs(widthRatio-heightRatio)<0.1){return Math.round(size.dpi*widthRatio);}}// Default estimation\nreturn Math.round(72*Math.min(width/800,height/600));}}export default new PrintingService();", "map": {"version": 3, "names": ["api", "endpoints", "PrintingService", "getCategories", "response", "get", "printing", "data", "getProducts", "categorySlug", "url", "getProduct", "slug", "calculatePrice", "productId", "quantity", "options", "post", "product_id", "getOrders", "page", "arguments", "length", "undefined", "orders", "getOrder", "id", "createOrder", "orderData", "cancelOrder", "reorder", "uploadFiles", "orderId", "files", "fileType", "formData", "FormData", "for<PERSON>ach", "file", "append", "headers", "getOrderFiles", "deleteFile", "fileId", "delete", "getUploadSettings", "validateFile", "_file$name$split$pop", "settings", "errors", "warnings", "maxSizeMB", "max_file_size_mb", "maxSizeBytes", "size", "push", "allowedTypes", "allowed_file_types", "fileExtension", "name", "split", "pop", "toLowerCase", "includes", "join", "type", "startsWith", "enable_dimension_validation", "dimensions", "getImageDimensions", "width", "min_width_px", "height", "min_height_px", "max_width_px", "max_height_px", "e", "valid", "message", "error", "maxSize", "Promise", "resolve", "img", "Image", "onload", "onerror", "src", "URL", "createObjectURL", "validateImageDPI", "naturalWidth", "naturalHeight", "estimatedDPI", "estimateDPI", "dpi", "printSizes", "widthRatio", "heightRatio", "Math", "abs", "round", "min"], "sources": ["C:/laragon/www/frontend/src/services/printingService.ts"], "sourcesContent": ["import api, { endpoints } from './api';\n\nexport interface PrintingCategory {\n  id: number;\n  name: string;\n  description: string;\n  slug: string;\n  image: string | null;\n  is_active: boolean;\n  sort_order: number;\n  products_count?: number;\n}\n\nexport interface PrintingProduct {\n  id: number;\n  printing_category_id: number;\n  name: string;\n  description: string;\n  slug: string;\n  base_price: number;\n  formatted_base_price: string;\n  image: string | null;\n  specifications: Record<string, any>;\n  options: Record<string, any>;\n  is_active: boolean;\n  min_quantity: number;\n  max_quantity: number | null;\n  production_time_days: number;\n  category?: PrintingCategory;\n}\n\nexport interface OrderItem {\n  product_id: number;\n  quantity: number;\n  specifications?: Record<string, any>;\n  selected_options?: Record<string, any>;\n  notes?: string;\n}\n\nexport interface PrintingOrder {\n  id: number;\n  user_id: number;\n  order_number: string;\n  status: string;\n  status_label: string;\n  total_amount: number;\n  formatted_total_amount: string;\n  payment_status: string;\n  payment_status_label: string;\n  payment_method: string | null;\n  payment_reference: string | null;\n  special_instructions: string | null;\n  delivery_address: Record<string, any> | null;\n  delivery_method: string | null;\n  estimated_completion_date: string | null;\n  completed_at: string | null;\n  shipped_at: string | null;\n  notes: string | null;\n  items: OrderItemDetail[];\n  files: OrderFile[];\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface OrderItemDetail {\n  id: number;\n  printing_order_id: number;\n  printing_product_id: number;\n  quantity: number;\n  unit_price: number;\n  total_price: number;\n  formatted_unit_price: string;\n  formatted_total_price: string;\n  specifications: Record<string, any>;\n  selected_options: Record<string, any>;\n  notes: string | null;\n  product: PrintingProduct;\n}\n\nexport interface OrderFile {\n  id: number;\n  printing_order_id: number;\n  original_name: string;\n  file_name: string;\n  file_path: string;\n  file_size: number;\n  formatted_file_size: string;\n  mime_type: string;\n  file_type: string;\n  file_type_label: string;\n  dimensions: { width: number; height: number } | null;\n  dpi: number | null;\n  dpi_status: string;\n  is_approved: boolean;\n  notes: string | null;\n  file_url: string;\n  is_image: boolean;\n  uploaded_by: number;\n  uploader?: { id: number; name: string };\n  created_at: string;\n}\n\nexport interface PriceCalculation {\n  product_id: number;\n  quantity: number;\n  unit_price: number;\n  total_price: number;\n  formatted_total_price: string;\n  production_time_days: number;\n}\n\nclass PrintingService {\n  // Categories\n  async getCategories(): Promise<PrintingCategory[]> {\n    const response = await api.get(`${endpoints.printing}/categories`);\n    return response.data.data;\n  }\n\n  // Products\n  async getProducts(categorySlug?: string): Promise<PrintingProduct[]> {\n    const url = categorySlug\n      ? `${endpoints.printing}/products/category/${categorySlug}`\n      : `${endpoints.printing}/products`;\n    const response = await api.get(url);\n    return response.data.data;\n  }\n\n  async getProduct(slug: string): Promise<PrintingProduct> {\n    const response = await api.get(`${endpoints.printing}/product/${slug}`);\n    return response.data.data;\n  }\n\n  async calculatePrice(productId: number, quantity: number, options?: Record<string, any>): Promise<PriceCalculation> {\n    const response = await api.post(`${endpoints.printing}/calculate-price`, {\n      product_id: productId,\n      quantity,\n      options: options || {}\n    });\n    return response.data.data;\n  }\n\n  // Orders\n  async getOrders(page: number = 1): Promise<{ data: PrintingOrder[]; meta: any }> {\n    const response = await api.get(`${endpoints.orders}?page=${page}`);\n    return response.data;\n  }\n\n  async getOrder(id: number): Promise<PrintingOrder> {\n    const response = await api.get(`${endpoints.orders}/${id}`);\n    return response.data.data;\n  }\n\n  async createOrder(orderData: {\n    items: OrderItem[];\n    special_instructions?: string;\n    delivery_address?: Record<string, any>;\n    delivery_method?: string;\n  }): Promise<PrintingOrder> {\n    const response = await api.post(endpoints.orders, orderData);\n    return response.data.data;\n  }\n\n  async cancelOrder(id: number): Promise<PrintingOrder> {\n    const response = await api.post(`${endpoints.orders}/${id}/cancel`);\n    return response.data.data;\n  }\n\n  async reorder(id: number): Promise<PrintingOrder> {\n    const response = await api.post(`${endpoints.orders}/${id}/reorder`);\n    return response.data.data;\n  }\n\n  // File uploads\n  async uploadFiles(orderId: number, files: File[], fileType: string = 'artwork'): Promise<OrderFile[]> {\n    const formData = new FormData();\n    files.forEach(file => {\n      formData.append('files[]', file);\n    });\n    formData.append('file_type', fileType);\n\n    const response = await api.post(`${endpoints.orders}/${orderId}/files`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data.data;\n  }\n\n  async getOrderFiles(orderId: number): Promise<OrderFile[]> {\n    const response = await api.get(`${endpoints.orders}/${orderId}/files`);\n    return response.data.data;\n  }\n\n  async deleteFile(orderId: number, fileId: number): Promise<void> {\n    await api.delete(`${endpoints.orders}/${orderId}/files/${fileId}`);\n  }\n\n  // Get file upload settings\n  async getUploadSettings(): Promise<any> {\n    const response = await api.get('/file-upload/settings');\n    return response.data.data;\n  }\n\n  // File validation helpers (now uses dynamic settings)\n  async validateFile(file: File): Promise<{ valid: boolean; message?: string; warnings?: string[] }> {\n    try {\n      const settings = await this.getUploadSettings();\n      const errors: string[] = [];\n      const warnings: string[] = [];\n\n      // Validate file size\n      const maxSizeMB = settings.max_file_size_mb || 50;\n      const maxSizeBytes = maxSizeMB * 1024 * 1024;\n      if (file.size > maxSizeBytes) {\n        errors.push(`File size must be less than ${maxSizeMB}MB`);\n      }\n\n      // Validate file type\n      const allowedTypes = settings.allowed_file_types || ['pdf', 'png', 'jpg', 'jpeg'];\n      const fileExtension = file.name.split('.').pop()?.toLowerCase();\n      if (!fileExtension || !allowedTypes.includes(fileExtension)) {\n        errors.push(`File type '${fileExtension}' is not allowed. Allowed types: ${allowedTypes.join(', ')}`);\n      }\n\n      // For images, validate dimensions if possible\n      if (file.type.startsWith('image/') && settings.enable_dimension_validation) {\n        try {\n          const dimensions = await this.getImageDimensions(file);\n          if (dimensions) {\n            if (dimensions.width < settings.min_width_px || dimensions.height < settings.min_height_px) {\n              errors.push(`Image dimensions (${dimensions.width}x${dimensions.height}px) are below minimum requirements (${settings.min_width_px}x${settings.min_height_px}px)`);\n            }\n            if (dimensions.width > settings.max_width_px || dimensions.height > settings.max_height_px) {\n              errors.push(`Image dimensions (${dimensions.width}x${dimensions.height}px) exceed maximum allowed size (${settings.max_width_px}x${settings.max_height_px}px)`);\n            }\n          }\n        } catch (e) {\n          // Ignore dimension validation errors\n        }\n      }\n\n      return {\n        valid: errors.length === 0,\n        message: errors.length > 0 ? errors.join('; ') : undefined,\n        warnings: warnings.length > 0 ? warnings : undefined\n      };\n\n    } catch (error) {\n      // Fallback to basic validation if settings can't be loaded\n      const maxSize = 50 * 1024 * 1024; // 50MB\n      const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml', 'application/pdf'];\n\n      if (file.size > maxSize) {\n        return { valid: false, message: 'File size must be less than 50MB' };\n      }\n\n      if (!allowedTypes.includes(file.type)) {\n        return { valid: false, message: 'File must be PNG, JPG, SVG, or PDF format' };\n      }\n\n      return { valid: true };\n    }\n  }\n\n  // Helper to get image dimensions\n  private getImageDimensions(file: File): Promise<{ width: number; height: number } | null> {\n    return new Promise((resolve) => {\n      if (!file.type.startsWith('image/')) {\n        resolve(null);\n        return;\n      }\n\n      const img = new Image();\n      img.onload = () => {\n        resolve({ width: img.width, height: img.height });\n      };\n      img.onerror = () => {\n        resolve(null);\n      };\n      img.src = URL.createObjectURL(file);\n    });\n  }\n\n  async validateImageDPI(file: File): Promise<{ dpi?: number; dimensions?: { width: number; height: number } }> {\n    return new Promise((resolve) => {\n      if (!file.type.startsWith('image/')) {\n        resolve({});\n        return;\n      }\n\n      const img = new Image();\n      img.onload = () => {\n        const dimensions = {\n          width: img.naturalWidth,\n          height: img.naturalHeight\n        };\n\n        // Estimate DPI based on common print sizes\n        const estimatedDPI = this.estimateDPI(dimensions);\n\n        resolve({ dimensions, dpi: estimatedDPI });\n      };\n      img.onerror = () => resolve({});\n      img.src = URL.createObjectURL(file);\n    });\n  }\n\n  private estimateDPI(dimensions: { width: number; height: number }): number {\n    // Business card at 300 DPI: ~1050x600px\n    // A5 flyer at 300 DPI: ~1240x1754px\n    // This is a rough estimation\n    const { width, height } = dimensions;\n\n    // Common print sizes at 300 DPI\n    const printSizes = [\n      { width: 1050, height: 600, dpi: 300 }, // Business card\n      { width: 1240, height: 1754, dpi: 300 }, // A5\n      { width: 2480, height: 3508, dpi: 300 }, // A4\n    ];\n\n    for (const size of printSizes) {\n      const widthRatio = width / size.width;\n      const heightRatio = height / size.height;\n\n      if (Math.abs(widthRatio - heightRatio) < 0.1) {\n        return Math.round(size.dpi * widthRatio);\n      }\n    }\n\n    // Default estimation\n    return Math.round(72 * Math.min(width / 800, height / 600));\n  }\n}\n\nexport default new PrintingService();\n"], "mappings": "AAAA,MAAO,CAAAA,GAAG,EAAIC,SAAS,KAAQ,OAAO,CA+GtC,KAAM,CAAAC,eAAgB,CACpB;AACA,KAAM,CAAAC,aAAaA,CAAA,CAAgC,CACjD,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACK,GAAG,CAAC,GAAGJ,SAAS,CAACK,QAAQ,aAAa,CAAC,CAClE,MAAO,CAAAF,QAAQ,CAACG,IAAI,CAACA,IAAI,CAC3B,CAEA;AACA,KAAM,CAAAC,WAAWA,CAACC,YAAqB,CAA8B,CACnE,KAAM,CAAAC,GAAG,CAAGD,YAAY,CACpB,GAAGR,SAAS,CAACK,QAAQ,sBAAsBG,YAAY,EAAE,CACzD,GAAGR,SAAS,CAACK,QAAQ,WAAW,CACpC,KAAM,CAAAF,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACK,GAAG,CAACK,GAAG,CAAC,CACnC,MAAO,CAAAN,QAAQ,CAACG,IAAI,CAACA,IAAI,CAC3B,CAEA,KAAM,CAAAI,UAAUA,CAACC,IAAY,CAA4B,CACvD,KAAM,CAAAR,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACK,GAAG,CAAC,GAAGJ,SAAS,CAACK,QAAQ,YAAYM,IAAI,EAAE,CAAC,CACvE,MAAO,CAAAR,QAAQ,CAACG,IAAI,CAACA,IAAI,CAC3B,CAEA,KAAM,CAAAM,cAAcA,CAACC,SAAiB,CAAEC,QAAgB,CAAEC,OAA6B,CAA6B,CAClH,KAAM,CAAAZ,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACiB,IAAI,CAAC,GAAGhB,SAAS,CAACK,QAAQ,kBAAkB,CAAE,CACvEY,UAAU,CAAEJ,SAAS,CACrBC,QAAQ,CACRC,OAAO,CAAEA,OAAO,EAAI,CAAC,CACvB,CAAC,CAAC,CACF,MAAO,CAAAZ,QAAQ,CAACG,IAAI,CAACA,IAAI,CAC3B,CAEA;AACA,KAAM,CAAAY,SAASA,CAAA,CAAkE,IAAjE,CAAAC,IAAY,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAC9B,KAAM,CAAAjB,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACK,GAAG,CAAC,GAAGJ,SAAS,CAACuB,MAAM,SAASJ,IAAI,EAAE,CAAC,CAClE,MAAO,CAAAhB,QAAQ,CAACG,IAAI,CACtB,CAEA,KAAM,CAAAkB,QAAQA,CAACC,EAAU,CAA0B,CACjD,KAAM,CAAAtB,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACK,GAAG,CAAC,GAAGJ,SAAS,CAACuB,MAAM,IAAIE,EAAE,EAAE,CAAC,CAC3D,MAAO,CAAAtB,QAAQ,CAACG,IAAI,CAACA,IAAI,CAC3B,CAEA,KAAM,CAAAoB,WAAWA,CAACC,SAKjB,CAA0B,CACzB,KAAM,CAAAxB,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACiB,IAAI,CAAChB,SAAS,CAACuB,MAAM,CAAEI,SAAS,CAAC,CAC5D,MAAO,CAAAxB,QAAQ,CAACG,IAAI,CAACA,IAAI,CAC3B,CAEA,KAAM,CAAAsB,WAAWA,CAACH,EAAU,CAA0B,CACpD,KAAM,CAAAtB,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACiB,IAAI,CAAC,GAAGhB,SAAS,CAACuB,MAAM,IAAIE,EAAE,SAAS,CAAC,CACnE,MAAO,CAAAtB,QAAQ,CAACG,IAAI,CAACA,IAAI,CAC3B,CAEA,KAAM,CAAAuB,OAAOA,CAACJ,EAAU,CAA0B,CAChD,KAAM,CAAAtB,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACiB,IAAI,CAAC,GAAGhB,SAAS,CAACuB,MAAM,IAAIE,EAAE,UAAU,CAAC,CACpE,MAAO,CAAAtB,QAAQ,CAACG,IAAI,CAACA,IAAI,CAC3B,CAEA;AACA,KAAM,CAAAwB,WAAWA,CAACC,OAAe,CAAEC,KAAa,CAAsD,IAApD,CAAAC,QAAgB,CAAAb,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,CAC5E,KAAM,CAAAc,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BH,KAAK,CAACI,OAAO,CAACC,IAAI,EAAI,CACpBH,QAAQ,CAACI,MAAM,CAAC,SAAS,CAAED,IAAI,CAAC,CAClC,CAAC,CAAC,CACFH,QAAQ,CAACI,MAAM,CAAC,WAAW,CAAEL,QAAQ,CAAC,CAEtC,KAAM,CAAA9B,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACiB,IAAI,CAAC,GAAGhB,SAAS,CAACuB,MAAM,IAAIQ,OAAO,QAAQ,CAAEG,QAAQ,CAAE,CAChFK,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CACF,MAAO,CAAApC,QAAQ,CAACG,IAAI,CAACA,IAAI,CAC3B,CAEA,KAAM,CAAAkC,aAAaA,CAACT,OAAe,CAAwB,CACzD,KAAM,CAAA5B,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACK,GAAG,CAAC,GAAGJ,SAAS,CAACuB,MAAM,IAAIQ,OAAO,QAAQ,CAAC,CACtE,MAAO,CAAA5B,QAAQ,CAACG,IAAI,CAACA,IAAI,CAC3B,CAEA,KAAM,CAAAmC,UAAUA,CAACV,OAAe,CAAEW,MAAc,CAAiB,CAC/D,KAAM,CAAA3C,GAAG,CAAC4C,MAAM,CAAC,GAAG3C,SAAS,CAACuB,MAAM,IAAIQ,OAAO,UAAUW,MAAM,EAAE,CAAC,CACpE,CAEA;AACA,KAAM,CAAAE,iBAAiBA,CAAA,CAAiB,CACtC,KAAM,CAAAzC,QAAQ,CAAG,KAAM,CAAAJ,GAAG,CAACK,GAAG,CAAC,uBAAuB,CAAC,CACvD,MAAO,CAAAD,QAAQ,CAACG,IAAI,CAACA,IAAI,CAC3B,CAEA;AACA,KAAM,CAAAuC,YAAYA,CAACR,IAAU,CAAsE,CACjG,GAAI,KAAAS,oBAAA,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,KAAI,CAACH,iBAAiB,CAAC,CAAC,CAC/C,KAAM,CAAAI,MAAgB,CAAG,EAAE,CAC3B,KAAM,CAAAC,QAAkB,CAAG,EAAE,CAE7B;AACA,KAAM,CAAAC,SAAS,CAAGH,QAAQ,CAACI,gBAAgB,EAAI,EAAE,CACjD,KAAM,CAAAC,YAAY,CAAGF,SAAS,CAAG,IAAI,CAAG,IAAI,CAC5C,GAAIb,IAAI,CAACgB,IAAI,CAAGD,YAAY,CAAE,CAC5BJ,MAAM,CAACM,IAAI,CAAC,+BAA+BJ,SAAS,IAAI,CAAC,CAC3D,CAEA;AACA,KAAM,CAAAK,YAAY,CAAGR,QAAQ,CAACS,kBAAkB,EAAI,CAAC,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,MAAM,CAAC,CACjF,KAAM,CAAAC,aAAa,EAAAX,oBAAA,CAAGT,IAAI,CAACqB,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,UAAAd,oBAAA,iBAA1BA,oBAAA,CAA4Be,WAAW,CAAC,CAAC,CAC/D,GAAI,CAACJ,aAAa,EAAI,CAACF,YAAY,CAACO,QAAQ,CAACL,aAAa,CAAC,CAAE,CAC3DT,MAAM,CAACM,IAAI,CAAC,cAAcG,aAAa,oCAAoCF,YAAY,CAACQ,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CACvG,CAEA;AACA,GAAI1B,IAAI,CAAC2B,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAIlB,QAAQ,CAACmB,2BAA2B,CAAE,CAC1E,GAAI,CACF,KAAM,CAAAC,UAAU,CAAG,KAAM,KAAI,CAACC,kBAAkB,CAAC/B,IAAI,CAAC,CACtD,GAAI8B,UAAU,CAAE,CACd,GAAIA,UAAU,CAACE,KAAK,CAAGtB,QAAQ,CAACuB,YAAY,EAAIH,UAAU,CAACI,MAAM,CAAGxB,QAAQ,CAACyB,aAAa,CAAE,CAC1FxB,MAAM,CAACM,IAAI,CAAC,qBAAqBa,UAAU,CAACE,KAAK,IAAIF,UAAU,CAACI,MAAM,uCAAuCxB,QAAQ,CAACuB,YAAY,IAAIvB,QAAQ,CAACyB,aAAa,KAAK,CAAC,CACpK,CACA,GAAIL,UAAU,CAACE,KAAK,CAAGtB,QAAQ,CAAC0B,YAAY,EAAIN,UAAU,CAACI,MAAM,CAAGxB,QAAQ,CAAC2B,aAAa,CAAE,CAC1F1B,MAAM,CAACM,IAAI,CAAC,qBAAqBa,UAAU,CAACE,KAAK,IAAIF,UAAU,CAACI,MAAM,oCAAoCxB,QAAQ,CAAC0B,YAAY,IAAI1B,QAAQ,CAAC2B,aAAa,KAAK,CAAC,CACjK,CACF,CACF,CAAE,MAAOC,CAAC,CAAE,CACV;AAAA,CAEJ,CAEA,MAAO,CACLC,KAAK,CAAE5B,MAAM,CAAC3B,MAAM,GAAK,CAAC,CAC1BwD,OAAO,CAAE7B,MAAM,CAAC3B,MAAM,CAAG,CAAC,CAAG2B,MAAM,CAACe,IAAI,CAAC,IAAI,CAAC,CAAGzC,SAAS,CAC1D2B,QAAQ,CAAEA,QAAQ,CAAC5B,MAAM,CAAG,CAAC,CAAG4B,QAAQ,CAAG3B,SAC7C,CAAC,CAEH,CAAE,MAAOwD,KAAK,CAAE,CACd;AACA,KAAM,CAAAC,OAAO,CAAG,EAAE,CAAG,IAAI,CAAG,IAAI,CAAE;AAClC,KAAM,CAAAxB,YAAY,CAAG,CAAC,WAAW,CAAE,YAAY,CAAE,WAAW,CAAE,eAAe,CAAE,iBAAiB,CAAC,CAEjG,GAAIlB,IAAI,CAACgB,IAAI,CAAG0B,OAAO,CAAE,CACvB,MAAO,CAAEH,KAAK,CAAE,KAAK,CAAEC,OAAO,CAAE,kCAAmC,CAAC,CACtE,CAEA,GAAI,CAACtB,YAAY,CAACO,QAAQ,CAACzB,IAAI,CAAC2B,IAAI,CAAC,CAAE,CACrC,MAAO,CAAEY,KAAK,CAAE,KAAK,CAAEC,OAAO,CAAE,2CAA4C,CAAC,CAC/E,CAEA,MAAO,CAAED,KAAK,CAAE,IAAK,CAAC,CACxB,CACF,CAEA;AACQR,kBAAkBA,CAAC/B,IAAU,CAAqD,CACxF,MAAO,IAAI,CAAA2C,OAAO,CAAEC,OAAO,EAAK,CAC9B,GAAI,CAAC5C,IAAI,CAAC2B,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAE,CACnCgB,OAAO,CAAC,IAAI,CAAC,CACb,OACF,CAEA,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,KAAK,CAAC,CAAC,CACvBD,GAAG,CAACE,MAAM,CAAG,IAAM,CACjBH,OAAO,CAAC,CAAEZ,KAAK,CAAEa,GAAG,CAACb,KAAK,CAAEE,MAAM,CAAEW,GAAG,CAACX,MAAO,CAAC,CAAC,CACnD,CAAC,CACDW,GAAG,CAACG,OAAO,CAAG,IAAM,CAClBJ,OAAO,CAAC,IAAI,CAAC,CACf,CAAC,CACDC,GAAG,CAACI,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACnD,IAAI,CAAC,CACrC,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAoD,gBAAgBA,CAACpD,IAAU,CAA6E,CAC5G,MAAO,IAAI,CAAA2C,OAAO,CAAEC,OAAO,EAAK,CAC9B,GAAI,CAAC5C,IAAI,CAAC2B,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAE,CACnCgB,OAAO,CAAC,CAAC,CAAC,CAAC,CACX,OACF,CAEA,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,KAAK,CAAC,CAAC,CACvBD,GAAG,CAACE,MAAM,CAAG,IAAM,CACjB,KAAM,CAAAjB,UAAU,CAAG,CACjBE,KAAK,CAAEa,GAAG,CAACQ,YAAY,CACvBnB,MAAM,CAAEW,GAAG,CAACS,aACd,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAG,IAAI,CAACC,WAAW,CAAC1B,UAAU,CAAC,CAEjDc,OAAO,CAAC,CAAEd,UAAU,CAAE2B,GAAG,CAAEF,YAAa,CAAC,CAAC,CAC5C,CAAC,CACDV,GAAG,CAACG,OAAO,CAAG,IAAMJ,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/BC,GAAG,CAACI,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACnD,IAAI,CAAC,CACrC,CAAC,CAAC,CACJ,CAEQwD,WAAWA,CAAC1B,UAA6C,CAAU,CACzE;AACA;AACA;AACA,KAAM,CAAEE,KAAK,CAAEE,MAAO,CAAC,CAAGJ,UAAU,CAEpC;AACA,KAAM,CAAA4B,UAAU,CAAG,CACjB,CAAE1B,KAAK,CAAE,IAAI,CAAEE,MAAM,CAAE,GAAG,CAAEuB,GAAG,CAAE,GAAI,CAAC,CAAE;AACxC,CAAEzB,KAAK,CAAE,IAAI,CAAEE,MAAM,CAAE,IAAI,CAAEuB,GAAG,CAAE,GAAI,CAAC,CAAE;AACzC,CAAEzB,KAAK,CAAE,IAAI,CAAEE,MAAM,CAAE,IAAI,CAAEuB,GAAG,CAAE,GAAI,CAAG;AAAA,CAC1C,CAED,IAAK,KAAM,CAAAzC,IAAI,GAAI,CAAA0C,UAAU,CAAE,CAC7B,KAAM,CAAAC,UAAU,CAAG3B,KAAK,CAAGhB,IAAI,CAACgB,KAAK,CACrC,KAAM,CAAA4B,WAAW,CAAG1B,MAAM,CAAGlB,IAAI,CAACkB,MAAM,CAExC,GAAI2B,IAAI,CAACC,GAAG,CAACH,UAAU,CAAGC,WAAW,CAAC,CAAG,GAAG,CAAE,CAC5C,MAAO,CAAAC,IAAI,CAACE,KAAK,CAAC/C,IAAI,CAACyC,GAAG,CAAGE,UAAU,CAAC,CAC1C,CACF,CAEA;AACA,MAAO,CAAAE,IAAI,CAACE,KAAK,CAAC,EAAE,CAAGF,IAAI,CAACG,GAAG,CAAChC,KAAK,CAAG,GAAG,CAAEE,MAAM,CAAG,GAAG,CAAC,CAAC,CAC7D,CACF,CAEA,cAAe,IAAI,CAAAtE,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}