{"ast": null, "code": "import React from'react';import{ThemeProvider}from'@mui/material/styles';import{CssBaseline}from'@mui/material';import materialDashboardTheme from'../../theme/materialDashboardTheme';import DashboardLayout from'./DashboardLayout';import ProtectedRoute from'../auth/ProtectedRoute';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DashboardRoute=_ref=>{let{children}=_ref;return/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsxs(ThemeProvider,{theme:materialDashboardTheme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(DashboardLayout,{children:children})]})});};export default DashboardRoute;", "map": {"version": 3, "names": ["React", "ThemeProvider", "CssBaseline", "materialDashboardTheme", "DashboardLayout", "ProtectedRoute", "jsx", "_jsx", "jsxs", "_jsxs", "DashboardRoute", "_ref", "children", "theme"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DashboardRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { ThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport materialDashboardTheme from '../../theme/materialDashboardTheme';\nimport DashboardLayout from './DashboardLayout';\nimport ProtectedRoute from '../auth/ProtectedRoute';\n\ninterface DashboardRouteProps {\n  children: React.ReactNode;\n}\n\nconst DashboardRoute: React.FC<DashboardRouteProps> = ({ children }) => {\n  return (\n    <ProtectedRoute>\n      <ThemeProvider theme={materialDashboardTheme}>\n        <CssBaseline />\n        <DashboardLayout>\n          {children}\n        </DashboardLayout>\n      </ThemeProvider>\n    </ProtectedRoute>\n  );\n};\n\nexport default DashboardRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,KAAQ,sBAAsB,CACpD,OAASC,WAAW,KAAQ,eAAe,CAC3C,MAAO,CAAAC,sBAAsB,KAAM,oCAAoC,CACvE,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMpD,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACjE,mBACEJ,IAAA,CAACF,cAAc,EAAAO,QAAA,cACbH,KAAA,CAACR,aAAa,EAACY,KAAK,CAAEV,sBAAuB,CAAAS,QAAA,eAC3CL,IAAA,CAACL,WAAW,GAAE,CAAC,cACfK,IAAA,CAACH,eAAe,EAAAQ,QAAA,CACbA,QAAQ,CACM,CAAC,EACL,CAAC,CACF,CAAC,CAErB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}