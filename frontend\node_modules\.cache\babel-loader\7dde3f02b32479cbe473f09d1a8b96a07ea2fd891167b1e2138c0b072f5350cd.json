{"ast": null, "code": "'use client';\n\nimport _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nvar _span;\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useId from '@mui/utils/useId';\nimport refType from '@mui/utils/refType';\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport Menu from \"../Menu/Menu.js\";\nimport { StyledSelectSelect, StyledSelectIcon } from \"../NativeSelect/NativeSelectInput.js\";\nimport { isFilled } from \"../InputBase/utils.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport slotShouldForwardProp from \"../styles/slotShouldForwardProp.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport useControlled from \"../utils/useControlled.js\";\nimport selectClasses, { getSelectUtilityClasses } from \"./selectClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SelectSelect = styled(StyledSelectSelect, {\n  name: 'MuiSelect',\n  slot: 'Select',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [\n    // Win specificity over the input base\n    {\n      [`&.${selectClasses.select}`]: styles.select\n    }, {\n      [`&.${selectClasses.select}`]: styles[ownerState.variant]\n    }, {\n      [`&.${selectClasses.error}`]: styles.error\n    }, {\n      [`&.${selectClasses.multiple}`]: styles.multiple\n    }];\n  }\n})({\n  // Win specificity over the input base\n  [`&.${selectClasses.select}`]: {\n    height: 'auto',\n    // Resets for multiple select with chips\n    minHeight: '1.4375em',\n    // Required for select\\text-field height consistency\n    textOverflow: 'ellipsis',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden'\n  }\n});\nconst SelectIcon = styled(StyledSelectIcon, {\n  name: 'MuiSelect',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.icon, ownerState.variant && styles[`icon${capitalize(ownerState.variant)}`], ownerState.open && styles.iconOpen];\n  }\n})({});\nconst SelectNativeInput = styled('input', {\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'classes',\n  name: 'MuiSelect',\n  slot: 'NativeInput'\n})({\n  bottom: 0,\n  left: 0,\n  position: 'absolute',\n  opacity: 0,\n  pointerEvents: 'none',\n  width: '100%',\n  boxSizing: 'border-box'\n});\nfunction areEqualValues(a, b) {\n  if (typeof b === 'object' && b !== null) {\n    return a === b;\n  }\n\n  // The value could be a number, the DOM will stringify it anyway.\n  return String(a) === String(b);\n}\nfunction isEmpty(display) {\n  return display == null || typeof display === 'string' && !display.trim();\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    disabled,\n    multiple,\n    open,\n    error\n  } = ownerState;\n  const slots = {\n    select: ['select', variant, disabled && 'disabled', multiple && 'multiple', error && 'error'],\n    icon: ['icon', `icon${capitalize(variant)}`, open && 'iconOpen', disabled && 'disabled'],\n    nativeInput: ['nativeInput']\n  };\n  return composeClasses(slots, getSelectUtilityClasses, classes);\n};\n\n/**\n * @ignore - internal component.\n */\nconst SelectInput = /*#__PURE__*/React.forwardRef(function SelectInput(props, ref) {\n  const {\n    'aria-describedby': ariaDescribedby,\n    'aria-label': ariaLabel,\n    autoFocus,\n    autoWidth,\n    children,\n    className,\n    defaultOpen,\n    defaultValue,\n    disabled,\n    displayEmpty,\n    error = false,\n    IconComponent,\n    inputRef: inputRefProp,\n    labelId,\n    MenuProps = {},\n    multiple,\n    name,\n    onBlur,\n    onChange,\n    onClose,\n    onFocus,\n    onOpen,\n    open: openProp,\n    readOnly,\n    renderValue,\n    required,\n    SelectDisplayProps = {},\n    tabIndex: tabIndexProp,\n    // catching `type` from Input which makes no sense for SelectInput\n    type,\n    value: valueProp,\n    variant = 'standard',\n    ...other\n  } = props;\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Select'\n  });\n  const [openState, setOpenState] = useControlled({\n    controlled: openProp,\n    default: defaultOpen,\n    name: 'Select'\n  });\n  const inputRef = React.useRef(null);\n  const displayRef = React.useRef(null);\n  const [displayNode, setDisplayNode] = React.useState(null);\n  const {\n    current: isOpenControlled\n  } = React.useRef(openProp != null);\n  const [menuMinWidthState, setMenuMinWidthState] = React.useState();\n  const handleRef = useForkRef(ref, inputRefProp);\n  const handleDisplayRef = React.useCallback(node => {\n    displayRef.current = node;\n    if (node) {\n      setDisplayNode(node);\n    }\n  }, []);\n  const anchorElement = displayNode?.parentNode;\n  React.useImperativeHandle(handleRef, () => ({\n    focus: () => {\n      displayRef.current.focus();\n    },\n    node: inputRef.current,\n    value\n  }), [value]);\n\n  // Resize menu on `defaultOpen` automatic toggle.\n  React.useEffect(() => {\n    if (defaultOpen && openState && displayNode && !isOpenControlled) {\n      setMenuMinWidthState(autoWidth ? null : anchorElement.clientWidth);\n      displayRef.current.focus();\n    }\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [displayNode, autoWidth]);\n  // `isOpenControlled` is ignored because the component should never switch between controlled and uncontrolled modes.\n  // `defaultOpen` and `openState` are ignored to avoid unnecessary callbacks.\n  React.useEffect(() => {\n    if (autoFocus) {\n      displayRef.current.focus();\n    }\n  }, [autoFocus]);\n  React.useEffect(() => {\n    if (!labelId) {\n      return undefined;\n    }\n    const label = ownerDocument(displayRef.current).getElementById(labelId);\n    if (label) {\n      const handler = () => {\n        if (getSelection().isCollapsed) {\n          displayRef.current.focus();\n        }\n      };\n      label.addEventListener('click', handler);\n      return () => {\n        label.removeEventListener('click', handler);\n      };\n    }\n    return undefined;\n  }, [labelId]);\n  const update = (open, event) => {\n    if (open) {\n      if (onOpen) {\n        onOpen(event);\n      }\n    } else if (onClose) {\n      onClose(event);\n    }\n    if (!isOpenControlled) {\n      setMenuMinWidthState(autoWidth ? null : anchorElement.clientWidth);\n      setOpenState(open);\n    }\n  };\n  const handleMouseDown = event => {\n    // Ignore everything but left-click\n    if (event.button !== 0) {\n      return;\n    }\n    // Hijack the default focus behavior.\n    event.preventDefault();\n    displayRef.current.focus();\n    update(true, event);\n  };\n  const handleClose = event => {\n    update(false, event);\n  };\n  const childrenArray = React.Children.toArray(children);\n\n  // Support autofill.\n  const handleChange = event => {\n    const child = childrenArray.find(childItem => childItem.props.value === event.target.value);\n    if (child === undefined) {\n      return;\n    }\n    setValueState(child.props.value);\n    if (onChange) {\n      onChange(event, child);\n    }\n  };\n  const handleItemClick = child => event => {\n    let newValue;\n\n    // We use the tabindex attribute to signal the available options.\n    if (!event.currentTarget.hasAttribute('tabindex')) {\n      return;\n    }\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      const itemIndex = value.indexOf(child.props.value);\n      if (itemIndex === -1) {\n        newValue.push(child.props.value);\n      } else {\n        newValue.splice(itemIndex, 1);\n      }\n    } else {\n      newValue = child.props.value;\n    }\n    if (child.props.onClick) {\n      child.props.onClick(event);\n    }\n    if (value !== newValue) {\n      setValueState(newValue);\n      if (onChange) {\n        // Redefine target to allow name and value to be read.\n        // This allows seamless integration with the most popular form libraries.\n        // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n        // Clone the event to not override `target` of the original event.\n        const nativeEvent = event.nativeEvent || event;\n        const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n        Object.defineProperty(clonedEvent, 'target', {\n          writable: true,\n          value: {\n            value: newValue,\n            name\n          }\n        });\n        onChange(clonedEvent, child);\n      }\n    }\n    if (!multiple) {\n      update(false, event);\n    }\n  };\n  const handleKeyDown = event => {\n    if (!readOnly) {\n      const validKeys = [' ', 'ArrowUp', 'ArrowDown',\n      // The native select doesn't respond to enter on macOS, but it's recommended by\n      // https://www.w3.org/WAI/ARIA/apg/patterns/combobox/examples/combobox-select-only/\n      'Enter'];\n      if (validKeys.includes(event.key)) {\n        event.preventDefault();\n        update(true, event);\n      }\n    }\n  };\n  const open = displayNode !== null && openState;\n  const handleBlur = event => {\n    // if open event.stopImmediatePropagation\n    if (!open && onBlur) {\n      // Preact support, target is read only property on a native event.\n      Object.defineProperty(event, 'target', {\n        writable: true,\n        value: {\n          value,\n          name\n        }\n      });\n      onBlur(event);\n    }\n  };\n  delete other['aria-invalid'];\n  let display;\n  let displaySingle;\n  const displayMultiple = [];\n  let computeDisplay = false;\n  let foundMatch = false;\n\n  // No need to display any value if the field is empty.\n  if (isFilled({\n    value\n  }) || displayEmpty) {\n    if (renderValue) {\n      display = renderValue(value);\n    } else {\n      computeDisplay = true;\n    }\n  }\n  const items = childrenArray.map(child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Select component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    let selected;\n    if (multiple) {\n      if (!Array.isArray(value)) {\n        throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: The `value` prop must be an array ' + 'when using the `Select` component with `multiple`.' : _formatErrorMessage(2));\n      }\n      selected = value.some(v => areEqualValues(v, child.props.value));\n      if (selected && computeDisplay) {\n        displayMultiple.push(child.props.children);\n      }\n    } else {\n      selected = areEqualValues(value, child.props.value);\n      if (selected && computeDisplay) {\n        displaySingle = child.props.children;\n      }\n    }\n    if (selected) {\n      foundMatch = true;\n    }\n    return /*#__PURE__*/React.cloneElement(child, {\n      'aria-selected': selected ? 'true' : 'false',\n      onClick: handleItemClick(child),\n      onKeyUp: event => {\n        if (event.key === ' ') {\n          // otherwise our MenuItems dispatches a click event\n          // it's not behavior of the native <option> and causes\n          // the select to close immediately since we open on space keydown\n          event.preventDefault();\n        }\n        if (child.props.onKeyUp) {\n          child.props.onKeyUp(event);\n        }\n      },\n      role: 'option',\n      selected,\n      value: undefined,\n      // The value is most likely not a valid HTML attribute.\n      'data-value': child.props.value // Instead, we provide it as a data attribute.\n    });\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!foundMatch && !multiple && value !== '') {\n        const values = childrenArray.map(child => child.props.value);\n        console.warn([`MUI: You have provided an out-of-range value \\`${value}\\` for the select ${name ? `(name=\"${name}\") ` : ''}component.`, \"Consider providing a value that matches one of the available options or ''.\", `The available values are ${values.filter(x => x != null).map(x => `\\`${x}\\``).join(', ') || '\"\"'}.`].join('\\n'));\n      }\n    }, [foundMatch, childrenArray, multiple, name, value]);\n  }\n  if (computeDisplay) {\n    if (multiple) {\n      if (displayMultiple.length === 0) {\n        display = null;\n      } else {\n        display = displayMultiple.reduce((output, child, index) => {\n          output.push(child);\n          if (index < displayMultiple.length - 1) {\n            output.push(', ');\n          }\n          return output;\n        }, []);\n      }\n    } else {\n      display = displaySingle;\n    }\n  }\n\n  // Avoid performing a layout computation in the render method.\n  let menuMinWidth = menuMinWidthState;\n  if (!autoWidth && isOpenControlled && displayNode) {\n    menuMinWidth = anchorElement.clientWidth;\n  }\n  let tabIndex;\n  if (typeof tabIndexProp !== 'undefined') {\n    tabIndex = tabIndexProp;\n  } else {\n    tabIndex = disabled ? null : 0;\n  }\n  const buttonId = SelectDisplayProps.id || (name ? `mui-component-select-${name}` : undefined);\n  const ownerState = {\n    ...props,\n    variant,\n    value,\n    open,\n    error\n  };\n  const classes = useUtilityClasses(ownerState);\n  const paperProps = {\n    ...MenuProps.PaperProps,\n    ...MenuProps.slotProps?.paper\n  };\n  const listProps = {\n    ...MenuProps.MenuListProps,\n    ...MenuProps.slotProps?.list\n  };\n  const listboxId = useId();\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(SelectSelect, {\n      as: \"div\",\n      ref: handleDisplayRef,\n      tabIndex: tabIndex,\n      role: \"combobox\",\n      \"aria-controls\": open ? listboxId : undefined,\n      \"aria-disabled\": disabled ? 'true' : undefined,\n      \"aria-expanded\": open ? 'true' : 'false',\n      \"aria-haspopup\": \"listbox\",\n      \"aria-label\": ariaLabel,\n      \"aria-labelledby\": [labelId, buttonId].filter(Boolean).join(' ') || undefined,\n      \"aria-describedby\": ariaDescribedby,\n      \"aria-required\": required ? 'true' : undefined,\n      \"aria-invalid\": error ? 'true' : undefined,\n      onKeyDown: handleKeyDown,\n      onMouseDown: disabled || readOnly ? null : handleMouseDown,\n      onBlur: handleBlur,\n      onFocus: onFocus,\n      ...SelectDisplayProps,\n      ownerState: ownerState,\n      className: clsx(SelectDisplayProps.className, classes.select, className)\n      // The id is required for proper a11y\n      ,\n\n      id: buttonId,\n      children: isEmpty(display) ?\n      // notranslate needed while Google Translate will not fix zero-width space issue\n      _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n        className: \"notranslate\",\n        \"aria-hidden\": true,\n        children: \"\\u200B\"\n      })) : display\n    }), /*#__PURE__*/_jsx(SelectNativeInput, {\n      \"aria-invalid\": error,\n      value: Array.isArray(value) ? value.join(',') : value,\n      name: name,\n      ref: inputRef,\n      \"aria-hidden\": true,\n      onChange: handleChange,\n      tabIndex: -1,\n      disabled: disabled,\n      className: classes.nativeInput,\n      autoFocus: autoFocus,\n      required: required,\n      ...other,\n      ownerState: ownerState\n    }), /*#__PURE__*/_jsx(SelectIcon, {\n      as: IconComponent,\n      className: classes.icon,\n      ownerState: ownerState\n    }), /*#__PURE__*/_jsx(Menu, {\n      id: `menu-${name || ''}`,\n      anchorEl: anchorElement,\n      open: open,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      transformOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      ...MenuProps,\n      slotProps: {\n        ...MenuProps.slotProps,\n        list: {\n          'aria-labelledby': labelId,\n          role: 'listbox',\n          'aria-multiselectable': multiple ? 'true' : undefined,\n          disableListWrap: true,\n          id: listboxId,\n          ...listProps\n        },\n        paper: {\n          ...paperProps,\n          style: {\n            minWidth: menuMinWidth,\n            ...(paperProps != null ? paperProps.style : null)\n          }\n        }\n      },\n      children: items\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SelectInput.propTypes = {\n  /**\n   * @ignore\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * @ignore\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * @ignore\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * If `true`, the width of the popover will automatically be set according to the items inside the\n   * menu, otherwise it will be at least the width of the select input.\n   */\n  autoWidth: PropTypes.bool,\n  /**\n   * The option elements to populate the select with.\n   * Can be some `<MenuItem>` elements.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The CSS class name of the select element.\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the component is toggled on mount. Use when the component open state is not controlled.\n   * You can only use it when the `native` prop is `false` (default).\n   */\n  defaultOpen: PropTypes.bool,\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the select is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the selected item is displayed even if its value is empty.\n   */\n  displayEmpty: PropTypes.bool,\n  /**\n   * If `true`, the `select input` will indicate an error.\n   */\n  error: PropTypes.bool,\n  /**\n   * The icon that displays the arrow.\n   */\n  IconComponent: PropTypes.elementType.isRequired,\n  /**\n   * Imperative handle implementing `{ value: T, node: HTMLElement, focus(): void }`\n   * Equivalent to `ref`\n   */\n  inputRef: refType,\n  /**\n   * The ID of an element that acts as an additional label. The Select will\n   * be labelled by the additional label and the selected value.\n   */\n  labelId: PropTypes.string,\n  /**\n   * Props applied to the [`Menu`](/material-ui/api/menu/) element.\n   */\n  MenuProps: PropTypes.object,\n  /**\n   * If `true`, `value` must be an array and the menu will support multiple selections.\n   */\n  multiple: PropTypes.bool,\n  /**\n   * Name attribute of the `select` or hidden `input` element.\n   */\n  name: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when a menu item is selected.\n   *\n   * @param {object} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (any).\n   * @param {object} [child] The react element that was selected.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   * Use in controlled mode (see open).\n   *\n   * @param {object} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be opened.\n   * Use in controlled mode (see open).\n   *\n   * @param {object} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Render the selected value.\n   *\n   * @param {any} value The `value` provided to the component.\n   * @returns {ReactNode}\n   */\n  renderValue: PropTypes.func,\n  /**\n   * If `true`, the component is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * Props applied to the clickable div element.\n   */\n  SelectDisplayProps: PropTypes.object,\n  /**\n   * @ignore\n   */\n  tabIndex: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  type: PropTypes.any,\n  /**\n   * The input value.\n   */\n  value: PropTypes.any,\n  /**\n   * The variant to use.\n   */\n  variant: PropTypes.oneOf(['standard', 'outlined', 'filled'])\n} : void 0;\nexport default SelectInput;", "map": {"version": 3, "names": ["_formatErrorMessage", "_span", "React", "isFragment", "PropTypes", "clsx", "composeClasses", "useId", "refType", "ownerDocument", "capitalize", "<PERSON><PERSON>", "StyledSelectSelect", "StyledSelectIcon", "isFilled", "styled", "slotShouldForwardProp", "useForkRef", "useControlled", "selectClasses", "getSelectUtilityClasses", "jsx", "_jsx", "jsxs", "_jsxs", "SelectSelect", "name", "slot", "overridesResolver", "props", "styles", "ownerState", "select", "variant", "error", "multiple", "height", "minHeight", "textOverflow", "whiteSpace", "overflow", "SelectIcon", "icon", "open", "iconOpen", "SelectNativeInput", "shouldForwardProp", "prop", "bottom", "left", "position", "opacity", "pointerEvents", "width", "boxSizing", "areEqualValues", "a", "b", "String", "isEmpty", "display", "trim", "useUtilityClasses", "classes", "disabled", "slots", "nativeInput", "SelectInput", "forwardRef", "ref", "aria<PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "autoFocus", "autoWidth", "children", "className", "defaultOpen", "defaultValue", "displayEmpty", "IconComponent", "inputRef", "inputRefProp", "labelId", "MenuProps", "onBlur", "onChange", "onClose", "onFocus", "onOpen", "openProp", "readOnly", "renderValue", "required", "SelectDisplayProps", "tabIndex", "tabIndexProp", "type", "value", "valueProp", "other", "setValueState", "controlled", "default", "openState", "setOpenState", "useRef", "displayRef", "displayNode", "setDisplayNode", "useState", "current", "isOpenControlled", "menuMinWidthState", "setMenuMinWidthState", "handleRef", "handleDisplayRef", "useCallback", "node", "anchorElement", "parentNode", "useImperativeHandle", "focus", "useEffect", "clientWidth", "undefined", "label", "getElementById", "handler", "getSelection", "isCollapsed", "addEventListener", "removeEventListener", "update", "event", "handleMouseDown", "button", "preventDefault", "handleClose", "childrenA<PERSON>y", "Children", "toArray", "handleChange", "child", "find", "childItem", "target", "handleItemClick", "newValue", "currentTarget", "hasAttribute", "Array", "isArray", "slice", "itemIndex", "indexOf", "push", "splice", "onClick", "nativeEvent", "clonedEvent", "constructor", "Object", "defineProperty", "writable", "handleKeyDown", "validKeys", "includes", "key", "handleBlur", "displaySingle", "displayMultiple", "computeDisplay", "foundMatch", "items", "map", "isValidElement", "process", "env", "NODE_ENV", "console", "join", "selected", "Error", "some", "v", "cloneElement", "onKeyUp", "role", "values", "warn", "filter", "x", "length", "reduce", "output", "index", "menu<PERSON>in<PERSON>idth", "buttonId", "id", "paperProps", "PaperProps", "slotProps", "paper", "listProps", "MenuListProps", "list", "listboxId", "Fragment", "as", "Boolean", "onKeyDown", "onMouseDown", "anchorEl", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "disableListWrap", "style", "min<PERSON><PERSON><PERSON>", "propTypes", "string", "bool", "object", "any", "elementType", "isRequired", "func", "oneOfType", "number", "oneOf"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Select/SelectInput.js"], "sourcesContent": ["'use client';\n\nimport _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nvar _span;\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useId from '@mui/utils/useId';\nimport refType from '@mui/utils/refType';\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport Menu from \"../Menu/Menu.js\";\nimport { StyledSelectSelect, StyledSelectIcon } from \"../NativeSelect/NativeSelectInput.js\";\nimport { isFilled } from \"../InputBase/utils.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport slotShouldForwardProp from \"../styles/slotShouldForwardProp.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport useControlled from \"../utils/useControlled.js\";\nimport selectClasses, { getSelectUtilityClasses } from \"./selectClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SelectSelect = styled(StyledSelectSelect, {\n  name: 'MuiSelect',\n  slot: 'Select',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [\n    // Win specificity over the input base\n    {\n      [`&.${selectClasses.select}`]: styles.select\n    }, {\n      [`&.${selectClasses.select}`]: styles[ownerState.variant]\n    }, {\n      [`&.${selectClasses.error}`]: styles.error\n    }, {\n      [`&.${selectClasses.multiple}`]: styles.multiple\n    }];\n  }\n})({\n  // Win specificity over the input base\n  [`&.${selectClasses.select}`]: {\n    height: 'auto',\n    // Resets for multiple select with chips\n    minHeight: '1.4375em',\n    // Required for select\\text-field height consistency\n    textOverflow: 'ellipsis',\n    whiteSpace: 'nowrap',\n    overflow: 'hidden'\n  }\n});\nconst SelectIcon = styled(StyledSelectIcon, {\n  name: 'MuiSelect',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.icon, ownerState.variant && styles[`icon${capitalize(ownerState.variant)}`], ownerState.open && styles.iconOpen];\n  }\n})({});\nconst SelectNativeInput = styled('input', {\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'classes',\n  name: 'MuiSelect',\n  slot: 'NativeInput'\n})({\n  bottom: 0,\n  left: 0,\n  position: 'absolute',\n  opacity: 0,\n  pointerEvents: 'none',\n  width: '100%',\n  boxSizing: 'border-box'\n});\nfunction areEqualValues(a, b) {\n  if (typeof b === 'object' && b !== null) {\n    return a === b;\n  }\n\n  // The value could be a number, the DOM will stringify it anyway.\n  return String(a) === String(b);\n}\nfunction isEmpty(display) {\n  return display == null || typeof display === 'string' && !display.trim();\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    disabled,\n    multiple,\n    open,\n    error\n  } = ownerState;\n  const slots = {\n    select: ['select', variant, disabled && 'disabled', multiple && 'multiple', error && 'error'],\n    icon: ['icon', `icon${capitalize(variant)}`, open && 'iconOpen', disabled && 'disabled'],\n    nativeInput: ['nativeInput']\n  };\n  return composeClasses(slots, getSelectUtilityClasses, classes);\n};\n\n/**\n * @ignore - internal component.\n */\nconst SelectInput = /*#__PURE__*/React.forwardRef(function SelectInput(props, ref) {\n  const {\n    'aria-describedby': ariaDescribedby,\n    'aria-label': ariaLabel,\n    autoFocus,\n    autoWidth,\n    children,\n    className,\n    defaultOpen,\n    defaultValue,\n    disabled,\n    displayEmpty,\n    error = false,\n    IconComponent,\n    inputRef: inputRefProp,\n    labelId,\n    MenuProps = {},\n    multiple,\n    name,\n    onBlur,\n    onChange,\n    onClose,\n    onFocus,\n    onOpen,\n    open: openProp,\n    readOnly,\n    renderValue,\n    required,\n    SelectDisplayProps = {},\n    tabIndex: tabIndexProp,\n    // catching `type` from Input which makes no sense for SelectInput\n    type,\n    value: valueProp,\n    variant = 'standard',\n    ...other\n  } = props;\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Select'\n  });\n  const [openState, setOpenState] = useControlled({\n    controlled: openProp,\n    default: defaultOpen,\n    name: 'Select'\n  });\n  const inputRef = React.useRef(null);\n  const displayRef = React.useRef(null);\n  const [displayNode, setDisplayNode] = React.useState(null);\n  const {\n    current: isOpenControlled\n  } = React.useRef(openProp != null);\n  const [menuMinWidthState, setMenuMinWidthState] = React.useState();\n  const handleRef = useForkRef(ref, inputRefProp);\n  const handleDisplayRef = React.useCallback(node => {\n    displayRef.current = node;\n    if (node) {\n      setDisplayNode(node);\n    }\n  }, []);\n  const anchorElement = displayNode?.parentNode;\n  React.useImperativeHandle(handleRef, () => ({\n    focus: () => {\n      displayRef.current.focus();\n    },\n    node: inputRef.current,\n    value\n  }), [value]);\n\n  // Resize menu on `defaultOpen` automatic toggle.\n  React.useEffect(() => {\n    if (defaultOpen && openState && displayNode && !isOpenControlled) {\n      setMenuMinWidthState(autoWidth ? null : anchorElement.clientWidth);\n      displayRef.current.focus();\n    }\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [displayNode, autoWidth]);\n  // `isOpenControlled` is ignored because the component should never switch between controlled and uncontrolled modes.\n  // `defaultOpen` and `openState` are ignored to avoid unnecessary callbacks.\n  React.useEffect(() => {\n    if (autoFocus) {\n      displayRef.current.focus();\n    }\n  }, [autoFocus]);\n  React.useEffect(() => {\n    if (!labelId) {\n      return undefined;\n    }\n    const label = ownerDocument(displayRef.current).getElementById(labelId);\n    if (label) {\n      const handler = () => {\n        if (getSelection().isCollapsed) {\n          displayRef.current.focus();\n        }\n      };\n      label.addEventListener('click', handler);\n      return () => {\n        label.removeEventListener('click', handler);\n      };\n    }\n    return undefined;\n  }, [labelId]);\n  const update = (open, event) => {\n    if (open) {\n      if (onOpen) {\n        onOpen(event);\n      }\n    } else if (onClose) {\n      onClose(event);\n    }\n    if (!isOpenControlled) {\n      setMenuMinWidthState(autoWidth ? null : anchorElement.clientWidth);\n      setOpenState(open);\n    }\n  };\n  const handleMouseDown = event => {\n    // Ignore everything but left-click\n    if (event.button !== 0) {\n      return;\n    }\n    // Hijack the default focus behavior.\n    event.preventDefault();\n    displayRef.current.focus();\n    update(true, event);\n  };\n  const handleClose = event => {\n    update(false, event);\n  };\n  const childrenArray = React.Children.toArray(children);\n\n  // Support autofill.\n  const handleChange = event => {\n    const child = childrenArray.find(childItem => childItem.props.value === event.target.value);\n    if (child === undefined) {\n      return;\n    }\n    setValueState(child.props.value);\n    if (onChange) {\n      onChange(event, child);\n    }\n  };\n  const handleItemClick = child => event => {\n    let newValue;\n\n    // We use the tabindex attribute to signal the available options.\n    if (!event.currentTarget.hasAttribute('tabindex')) {\n      return;\n    }\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      const itemIndex = value.indexOf(child.props.value);\n      if (itemIndex === -1) {\n        newValue.push(child.props.value);\n      } else {\n        newValue.splice(itemIndex, 1);\n      }\n    } else {\n      newValue = child.props.value;\n    }\n    if (child.props.onClick) {\n      child.props.onClick(event);\n    }\n    if (value !== newValue) {\n      setValueState(newValue);\n      if (onChange) {\n        // Redefine target to allow name and value to be read.\n        // This allows seamless integration with the most popular form libraries.\n        // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n        // Clone the event to not override `target` of the original event.\n        const nativeEvent = event.nativeEvent || event;\n        const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n        Object.defineProperty(clonedEvent, 'target', {\n          writable: true,\n          value: {\n            value: newValue,\n            name\n          }\n        });\n        onChange(clonedEvent, child);\n      }\n    }\n    if (!multiple) {\n      update(false, event);\n    }\n  };\n  const handleKeyDown = event => {\n    if (!readOnly) {\n      const validKeys = [' ', 'ArrowUp', 'ArrowDown',\n      // The native select doesn't respond to enter on macOS, but it's recommended by\n      // https://www.w3.org/WAI/ARIA/apg/patterns/combobox/examples/combobox-select-only/\n      'Enter'];\n      if (validKeys.includes(event.key)) {\n        event.preventDefault();\n        update(true, event);\n      }\n    }\n  };\n  const open = displayNode !== null && openState;\n  const handleBlur = event => {\n    // if open event.stopImmediatePropagation\n    if (!open && onBlur) {\n      // Preact support, target is read only property on a native event.\n      Object.defineProperty(event, 'target', {\n        writable: true,\n        value: {\n          value,\n          name\n        }\n      });\n      onBlur(event);\n    }\n  };\n  delete other['aria-invalid'];\n  let display;\n  let displaySingle;\n  const displayMultiple = [];\n  let computeDisplay = false;\n  let foundMatch = false;\n\n  // No need to display any value if the field is empty.\n  if (isFilled({\n    value\n  }) || displayEmpty) {\n    if (renderValue) {\n      display = renderValue(value);\n    } else {\n      computeDisplay = true;\n    }\n  }\n  const items = childrenArray.map(child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Select component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    let selected;\n    if (multiple) {\n      if (!Array.isArray(value)) {\n        throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: The `value` prop must be an array ' + 'when using the `Select` component with `multiple`.' : _formatErrorMessage(2));\n      }\n      selected = value.some(v => areEqualValues(v, child.props.value));\n      if (selected && computeDisplay) {\n        displayMultiple.push(child.props.children);\n      }\n    } else {\n      selected = areEqualValues(value, child.props.value);\n      if (selected && computeDisplay) {\n        displaySingle = child.props.children;\n      }\n    }\n    if (selected) {\n      foundMatch = true;\n    }\n    return /*#__PURE__*/React.cloneElement(child, {\n      'aria-selected': selected ? 'true' : 'false',\n      onClick: handleItemClick(child),\n      onKeyUp: event => {\n        if (event.key === ' ') {\n          // otherwise our MenuItems dispatches a click event\n          // it's not behavior of the native <option> and causes\n          // the select to close immediately since we open on space keydown\n          event.preventDefault();\n        }\n        if (child.props.onKeyUp) {\n          child.props.onKeyUp(event);\n        }\n      },\n      role: 'option',\n      selected,\n      value: undefined,\n      // The value is most likely not a valid HTML attribute.\n      'data-value': child.props.value // Instead, we provide it as a data attribute.\n    });\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!foundMatch && !multiple && value !== '') {\n        const values = childrenArray.map(child => child.props.value);\n        console.warn([`MUI: You have provided an out-of-range value \\`${value}\\` for the select ${name ? `(name=\"${name}\") ` : ''}component.`, \"Consider providing a value that matches one of the available options or ''.\", `The available values are ${values.filter(x => x != null).map(x => `\\`${x}\\``).join(', ') || '\"\"'}.`].join('\\n'));\n      }\n    }, [foundMatch, childrenArray, multiple, name, value]);\n  }\n  if (computeDisplay) {\n    if (multiple) {\n      if (displayMultiple.length === 0) {\n        display = null;\n      } else {\n        display = displayMultiple.reduce((output, child, index) => {\n          output.push(child);\n          if (index < displayMultiple.length - 1) {\n            output.push(', ');\n          }\n          return output;\n        }, []);\n      }\n    } else {\n      display = displaySingle;\n    }\n  }\n\n  // Avoid performing a layout computation in the render method.\n  let menuMinWidth = menuMinWidthState;\n  if (!autoWidth && isOpenControlled && displayNode) {\n    menuMinWidth = anchorElement.clientWidth;\n  }\n  let tabIndex;\n  if (typeof tabIndexProp !== 'undefined') {\n    tabIndex = tabIndexProp;\n  } else {\n    tabIndex = disabled ? null : 0;\n  }\n  const buttonId = SelectDisplayProps.id || (name ? `mui-component-select-${name}` : undefined);\n  const ownerState = {\n    ...props,\n    variant,\n    value,\n    open,\n    error\n  };\n  const classes = useUtilityClasses(ownerState);\n  const paperProps = {\n    ...MenuProps.PaperProps,\n    ...MenuProps.slotProps?.paper\n  };\n  const listProps = {\n    ...MenuProps.MenuListProps,\n    ...MenuProps.slotProps?.list\n  };\n  const listboxId = useId();\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(SelectSelect, {\n      as: \"div\",\n      ref: handleDisplayRef,\n      tabIndex: tabIndex,\n      role: \"combobox\",\n      \"aria-controls\": open ? listboxId : undefined,\n      \"aria-disabled\": disabled ? 'true' : undefined,\n      \"aria-expanded\": open ? 'true' : 'false',\n      \"aria-haspopup\": \"listbox\",\n      \"aria-label\": ariaLabel,\n      \"aria-labelledby\": [labelId, buttonId].filter(Boolean).join(' ') || undefined,\n      \"aria-describedby\": ariaDescribedby,\n      \"aria-required\": required ? 'true' : undefined,\n      \"aria-invalid\": error ? 'true' : undefined,\n      onKeyDown: handleKeyDown,\n      onMouseDown: disabled || readOnly ? null : handleMouseDown,\n      onBlur: handleBlur,\n      onFocus: onFocus,\n      ...SelectDisplayProps,\n      ownerState: ownerState,\n      className: clsx(SelectDisplayProps.className, classes.select, className)\n      // The id is required for proper a11y\n      ,\n      id: buttonId,\n      children: isEmpty(display) ? // notranslate needed while Google Translate will not fix zero-width space issue\n      _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n        className: \"notranslate\",\n        \"aria-hidden\": true,\n        children: \"\\u200B\"\n      })) : display\n    }), /*#__PURE__*/_jsx(SelectNativeInput, {\n      \"aria-invalid\": error,\n      value: Array.isArray(value) ? value.join(',') : value,\n      name: name,\n      ref: inputRef,\n      \"aria-hidden\": true,\n      onChange: handleChange,\n      tabIndex: -1,\n      disabled: disabled,\n      className: classes.nativeInput,\n      autoFocus: autoFocus,\n      required: required,\n      ...other,\n      ownerState: ownerState\n    }), /*#__PURE__*/_jsx(SelectIcon, {\n      as: IconComponent,\n      className: classes.icon,\n      ownerState: ownerState\n    }), /*#__PURE__*/_jsx(Menu, {\n      id: `menu-${name || ''}`,\n      anchorEl: anchorElement,\n      open: open,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      transformOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      ...MenuProps,\n      slotProps: {\n        ...MenuProps.slotProps,\n        list: {\n          'aria-labelledby': labelId,\n          role: 'listbox',\n          'aria-multiselectable': multiple ? 'true' : undefined,\n          disableListWrap: true,\n          id: listboxId,\n          ...listProps\n        },\n        paper: {\n          ...paperProps,\n          style: {\n            minWidth: menuMinWidth,\n            ...(paperProps != null ? paperProps.style : null)\n          }\n        }\n      },\n      children: items\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SelectInput.propTypes = {\n  /**\n   * @ignore\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * @ignore\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * @ignore\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * If `true`, the width of the popover will automatically be set according to the items inside the\n   * menu, otherwise it will be at least the width of the select input.\n   */\n  autoWidth: PropTypes.bool,\n  /**\n   * The option elements to populate the select with.\n   * Can be some `<MenuItem>` elements.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The CSS class name of the select element.\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the component is toggled on mount. Use when the component open state is not controlled.\n   * You can only use it when the `native` prop is `false` (default).\n   */\n  defaultOpen: PropTypes.bool,\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the select is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the selected item is displayed even if its value is empty.\n   */\n  displayEmpty: PropTypes.bool,\n  /**\n   * If `true`, the `select input` will indicate an error.\n   */\n  error: PropTypes.bool,\n  /**\n   * The icon that displays the arrow.\n   */\n  IconComponent: PropTypes.elementType.isRequired,\n  /**\n   * Imperative handle implementing `{ value: T, node: HTMLElement, focus(): void }`\n   * Equivalent to `ref`\n   */\n  inputRef: refType,\n  /**\n   * The ID of an element that acts as an additional label. The Select will\n   * be labelled by the additional label and the selected value.\n   */\n  labelId: PropTypes.string,\n  /**\n   * Props applied to the [`Menu`](/material-ui/api/menu/) element.\n   */\n  MenuProps: PropTypes.object,\n  /**\n   * If `true`, `value` must be an array and the menu will support multiple selections.\n   */\n  multiple: PropTypes.bool,\n  /**\n   * Name attribute of the `select` or hidden `input` element.\n   */\n  name: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when a menu item is selected.\n   *\n   * @param {object} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (any).\n   * @param {object} [child] The react element that was selected.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   * Use in controlled mode (see open).\n   *\n   * @param {object} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be opened.\n   * Use in controlled mode (see open).\n   *\n   * @param {object} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Render the selected value.\n   *\n   * @param {any} value The `value` provided to the component.\n   * @returns {ReactNode}\n   */\n  renderValue: PropTypes.func,\n  /**\n   * If `true`, the component is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * Props applied to the clickable div element.\n   */\n  SelectDisplayProps: PropTypes.object,\n  /**\n   * @ignore\n   */\n  tabIndex: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  type: PropTypes.any,\n  /**\n   * The input value.\n   */\n  value: PropTypes.any,\n  /**\n   * The variant to use.\n   */\n  variant: PropTypes.oneOf(['standard', 'outlined', 'filled'])\n} : void 0;\nexport default SelectInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,mBAAmB,MAAM,kCAAkC;AAClE,IAAIC,KAAK;AACT,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,sCAAsC;AAC3F,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,IAAIC,uBAAuB,QAAQ,oBAAoB;AAC3E,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,YAAY,GAAGV,MAAM,CAACH,kBAAkB,EAAE;EAC9Cc,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJC;IACF,CAAC,GAAGF,KAAK;IACT,OAAO;IACP;IACA;MACE,CAAC,KAAKV,aAAa,CAACa,MAAM,EAAE,GAAGF,MAAM,CAACE;IACxC,CAAC,EAAE;MACD,CAAC,KAAKb,aAAa,CAACa,MAAM,EAAE,GAAGF,MAAM,CAACC,UAAU,CAACE,OAAO;IAC1D,CAAC,EAAE;MACD,CAAC,KAAKd,aAAa,CAACe,KAAK,EAAE,GAAGJ,MAAM,CAACI;IACvC,CAAC,EAAE;MACD,CAAC,KAAKf,aAAa,CAACgB,QAAQ,EAAE,GAAGL,MAAM,CAACK;IAC1C,CAAC,CAAC;EACJ;AACF,CAAC,CAAC,CAAC;EACD;EACA,CAAC,KAAKhB,aAAa,CAACa,MAAM,EAAE,GAAG;IAC7BI,MAAM,EAAE,MAAM;IACd;IACAC,SAAS,EAAE,UAAU;IACrB;IACAC,YAAY,EAAE,UAAU;IACxBC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AACF,MAAMC,UAAU,GAAG1B,MAAM,CAACF,gBAAgB,EAAE;EAC1Ca,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJC;IACF,CAAC,GAAGF,KAAK;IACT,OAAO,CAACC,MAAM,CAACY,IAAI,EAAEX,UAAU,CAACE,OAAO,IAAIH,MAAM,CAAC,OAAOpB,UAAU,CAACqB,UAAU,CAACE,OAAO,CAAC,EAAE,CAAC,EAAEF,UAAU,CAACY,IAAI,IAAIb,MAAM,CAACc,QAAQ,CAAC;EACjI;AACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,MAAMC,iBAAiB,GAAG9B,MAAM,CAAC,OAAO,EAAE;EACxC+B,iBAAiB,EAAEC,IAAI,IAAI/B,qBAAqB,CAAC+B,IAAI,CAAC,IAAIA,IAAI,KAAK,SAAS;EAC5ErB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDqB,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,CAAC;EACVC,aAAa,EAAE,MAAM;EACrBC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,SAASC,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,EAAE;IACvC,OAAOD,CAAC,KAAKC,CAAC;EAChB;;EAEA;EACA,OAAOC,MAAM,CAACF,CAAC,CAAC,KAAKE,MAAM,CAACD,CAAC,CAAC;AAChC;AACA,SAASE,OAAOA,CAACC,OAAO,EAAE;EACxB,OAAOA,OAAO,IAAI,IAAI,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAACA,OAAO,CAACC,IAAI,CAAC,CAAC;AAC1E;AACA,MAAMC,iBAAiB,GAAG/B,UAAU,IAAI;EACtC,MAAM;IACJgC,OAAO;IACP9B,OAAO;IACP+B,QAAQ;IACR7B,QAAQ;IACRQ,IAAI;IACJT;EACF,CAAC,GAAGH,UAAU;EACd,MAAMkC,KAAK,GAAG;IACZjC,MAAM,EAAE,CAAC,QAAQ,EAAEC,OAAO,EAAE+B,QAAQ,IAAI,UAAU,EAAE7B,QAAQ,IAAI,UAAU,EAAED,KAAK,IAAI,OAAO,CAAC;IAC7FQ,IAAI,EAAE,CAAC,MAAM,EAAE,OAAOhC,UAAU,CAACuB,OAAO,CAAC,EAAE,EAAEU,IAAI,IAAI,UAAU,EAAEqB,QAAQ,IAAI,UAAU,CAAC;IACxFE,WAAW,EAAE,CAAC,aAAa;EAC7B,CAAC;EACD,OAAO5D,cAAc,CAAC2D,KAAK,EAAE7C,uBAAuB,EAAE2C,OAAO,CAAC;AAChE,CAAC;;AAED;AACA;AACA;AACA,MAAMI,WAAW,GAAG,aAAajE,KAAK,CAACkE,UAAU,CAAC,SAASD,WAAWA,CAACtC,KAAK,EAAEwC,GAAG,EAAE;EACjF,MAAM;IACJ,kBAAkB,EAAEC,eAAe;IACnC,YAAY,EAAEC,SAAS;IACvBC,SAAS;IACTC,SAAS;IACTC,QAAQ;IACRC,SAAS;IACTC,WAAW;IACXC,YAAY;IACZb,QAAQ;IACRc,YAAY;IACZ5C,KAAK,GAAG,KAAK;IACb6C,aAAa;IACbC,QAAQ,EAAEC,YAAY;IACtBC,OAAO;IACPC,SAAS,GAAG,CAAC,CAAC;IACdhD,QAAQ;IACRT,IAAI;IACJ0D,MAAM;IACNC,QAAQ;IACRC,OAAO;IACPC,OAAO;IACPC,MAAM;IACN7C,IAAI,EAAE8C,QAAQ;IACdC,QAAQ;IACRC,WAAW;IACXC,QAAQ;IACRC,kBAAkB,GAAG,CAAC,CAAC;IACvBC,QAAQ,EAAEC,YAAY;IACtB;IACAC,IAAI;IACJC,KAAK,EAAEC,SAAS;IAChBjE,OAAO,GAAG,UAAU;IACpB,GAAGkE;EACL,CAAC,GAAGtE,KAAK;EACT,MAAM,CAACoE,KAAK,EAAEG,aAAa,CAAC,GAAGlF,aAAa,CAAC;IAC3CmF,UAAU,EAAEH,SAAS;IACrBI,OAAO,EAAEzB,YAAY;IACrBnD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAAC6E,SAAS,EAAEC,YAAY,CAAC,GAAGtF,aAAa,CAAC;IAC9CmF,UAAU,EAAEZ,QAAQ;IACpBa,OAAO,EAAE1B,WAAW;IACpBlD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMsD,QAAQ,GAAG9E,KAAK,CAACuG,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,UAAU,GAAGxG,KAAK,CAACuG,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM,CAACE,WAAW,EAAEC,cAAc,CAAC,GAAG1G,KAAK,CAAC2G,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM;IACJC,OAAO,EAAEC;EACX,CAAC,GAAG7G,KAAK,CAACuG,MAAM,CAAChB,QAAQ,IAAI,IAAI,CAAC;EAClC,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/G,KAAK,CAAC2G,QAAQ,CAAC,CAAC;EAClE,MAAMK,SAAS,GAAGjG,UAAU,CAACoD,GAAG,EAAEY,YAAY,CAAC;EAC/C,MAAMkC,gBAAgB,GAAGjH,KAAK,CAACkH,WAAW,CAACC,IAAI,IAAI;IACjDX,UAAU,CAACI,OAAO,GAAGO,IAAI;IACzB,IAAIA,IAAI,EAAE;MACRT,cAAc,CAACS,IAAI,CAAC;IACtB;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,aAAa,GAAGX,WAAW,EAAEY,UAAU;EAC7CrH,KAAK,CAACsH,mBAAmB,CAACN,SAAS,EAAE,OAAO;IAC1CO,KAAK,EAAEA,CAAA,KAAM;MACXf,UAAU,CAACI,OAAO,CAACW,KAAK,CAAC,CAAC;IAC5B,CAAC;IACDJ,IAAI,EAAErC,QAAQ,CAAC8B,OAAO;IACtBb;EACF,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;;EAEZ;EACA/F,KAAK,CAACwH,SAAS,CAAC,MAAM;IACpB,IAAI9C,WAAW,IAAI2B,SAAS,IAAII,WAAW,IAAI,CAACI,gBAAgB,EAAE;MAChEE,oBAAoB,CAACxC,SAAS,GAAG,IAAI,GAAG6C,aAAa,CAACK,WAAW,CAAC;MAClEjB,UAAU,CAACI,OAAO,CAACW,KAAK,CAAC,CAAC;IAC5B;IACA;IACA;EACF,CAAC,EAAE,CAACd,WAAW,EAAElC,SAAS,CAAC,CAAC;EAC5B;EACA;EACAvE,KAAK,CAACwH,SAAS,CAAC,MAAM;IACpB,IAAIlD,SAAS,EAAE;MACbkC,UAAU,CAACI,OAAO,CAACW,KAAK,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACjD,SAAS,CAAC,CAAC;EACftE,KAAK,CAACwH,SAAS,CAAC,MAAM;IACpB,IAAI,CAACxC,OAAO,EAAE;MACZ,OAAO0C,SAAS;IAClB;IACA,MAAMC,KAAK,GAAGpH,aAAa,CAACiG,UAAU,CAACI,OAAO,CAAC,CAACgB,cAAc,CAAC5C,OAAO,CAAC;IACvE,IAAI2C,KAAK,EAAE;MACT,MAAME,OAAO,GAAGA,CAAA,KAAM;QACpB,IAAIC,YAAY,CAAC,CAAC,CAACC,WAAW,EAAE;UAC9BvB,UAAU,CAACI,OAAO,CAACW,KAAK,CAAC,CAAC;QAC5B;MACF,CAAC;MACDI,KAAK,CAACK,gBAAgB,CAAC,OAAO,EAAEH,OAAO,CAAC;MACxC,OAAO,MAAM;QACXF,KAAK,CAACM,mBAAmB,CAAC,OAAO,EAAEJ,OAAO,CAAC;MAC7C,CAAC;IACH;IACA,OAAOH,SAAS;EAClB,CAAC,EAAE,CAAC1C,OAAO,CAAC,CAAC;EACb,MAAMkD,MAAM,GAAGA,CAACzF,IAAI,EAAE0F,KAAK,KAAK;IAC9B,IAAI1F,IAAI,EAAE;MACR,IAAI6C,MAAM,EAAE;QACVA,MAAM,CAAC6C,KAAK,CAAC;MACf;IACF,CAAC,MAAM,IAAI/C,OAAO,EAAE;MAClBA,OAAO,CAAC+C,KAAK,CAAC;IAChB;IACA,IAAI,CAACtB,gBAAgB,EAAE;MACrBE,oBAAoB,CAACxC,SAAS,GAAG,IAAI,GAAG6C,aAAa,CAACK,WAAW,CAAC;MAClEnB,YAAY,CAAC7D,IAAI,CAAC;IACpB;EACF,CAAC;EACD,MAAM2F,eAAe,GAAGD,KAAK,IAAI;IAC/B;IACA,IAAIA,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;IACA;IACAF,KAAK,CAACG,cAAc,CAAC,CAAC;IACtB9B,UAAU,CAACI,OAAO,CAACW,KAAK,CAAC,CAAC;IAC1BW,MAAM,CAAC,IAAI,EAAEC,KAAK,CAAC;EACrB,CAAC;EACD,MAAMI,WAAW,GAAGJ,KAAK,IAAI;IAC3BD,MAAM,CAAC,KAAK,EAAEC,KAAK,CAAC;EACtB,CAAC;EACD,MAAMK,aAAa,GAAGxI,KAAK,CAACyI,QAAQ,CAACC,OAAO,CAAClE,QAAQ,CAAC;;EAEtD;EACA,MAAMmE,YAAY,GAAGR,KAAK,IAAI;IAC5B,MAAMS,KAAK,GAAGJ,aAAa,CAACK,IAAI,CAACC,SAAS,IAAIA,SAAS,CAACnH,KAAK,CAACoE,KAAK,KAAKoC,KAAK,CAACY,MAAM,CAAChD,KAAK,CAAC;IAC3F,IAAI6C,KAAK,KAAKlB,SAAS,EAAE;MACvB;IACF;IACAxB,aAAa,CAAC0C,KAAK,CAACjH,KAAK,CAACoE,KAAK,CAAC;IAChC,IAAIZ,QAAQ,EAAE;MACZA,QAAQ,CAACgD,KAAK,EAAES,KAAK,CAAC;IACxB;EACF,CAAC;EACD,MAAMI,eAAe,GAAGJ,KAAK,IAAIT,KAAK,IAAI;IACxC,IAAIc,QAAQ;;IAEZ;IACA,IAAI,CAACd,KAAK,CAACe,aAAa,CAACC,YAAY,CAAC,UAAU,CAAC,EAAE;MACjD;IACF;IACA,IAAIlH,QAAQ,EAAE;MACZgH,QAAQ,GAAGG,KAAK,CAACC,OAAO,CAACtD,KAAK,CAAC,GAAGA,KAAK,CAACuD,KAAK,CAAC,CAAC,GAAG,EAAE;MACpD,MAAMC,SAAS,GAAGxD,KAAK,CAACyD,OAAO,CAACZ,KAAK,CAACjH,KAAK,CAACoE,KAAK,CAAC;MAClD,IAAIwD,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBN,QAAQ,CAACQ,IAAI,CAACb,KAAK,CAACjH,KAAK,CAACoE,KAAK,CAAC;MAClC,CAAC,MAAM;QACLkD,QAAQ,CAACS,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC;MAC/B;IACF,CAAC,MAAM;MACLN,QAAQ,GAAGL,KAAK,CAACjH,KAAK,CAACoE,KAAK;IAC9B;IACA,IAAI6C,KAAK,CAACjH,KAAK,CAACgI,OAAO,EAAE;MACvBf,KAAK,CAACjH,KAAK,CAACgI,OAAO,CAACxB,KAAK,CAAC;IAC5B;IACA,IAAIpC,KAAK,KAAKkD,QAAQ,EAAE;MACtB/C,aAAa,CAAC+C,QAAQ,CAAC;MACvB,IAAI9D,QAAQ,EAAE;QACZ;QACA;QACA;QACA;QACA,MAAMyE,WAAW,GAAGzB,KAAK,CAACyB,WAAW,IAAIzB,KAAK;QAC9C,MAAM0B,WAAW,GAAG,IAAID,WAAW,CAACE,WAAW,CAACF,WAAW,CAAC9D,IAAI,EAAE8D,WAAW,CAAC;QAC9EG,MAAM,CAACC,cAAc,CAACH,WAAW,EAAE,QAAQ,EAAE;UAC3CI,QAAQ,EAAE,IAAI;UACdlE,KAAK,EAAE;YACLA,KAAK,EAAEkD,QAAQ;YACfzH;UACF;QACF,CAAC,CAAC;QACF2D,QAAQ,CAAC0E,WAAW,EAAEjB,KAAK,CAAC;MAC9B;IACF;IACA,IAAI,CAAC3G,QAAQ,EAAE;MACbiG,MAAM,CAAC,KAAK,EAAEC,KAAK,CAAC;IACtB;EACF,CAAC;EACD,MAAM+B,aAAa,GAAG/B,KAAK,IAAI;IAC7B,IAAI,CAAC3C,QAAQ,EAAE;MACb,MAAM2E,SAAS,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,WAAW;MAC9C;MACA;MACA,OAAO,CAAC;MACR,IAAIA,SAAS,CAACC,QAAQ,CAACjC,KAAK,CAACkC,GAAG,CAAC,EAAE;QACjClC,KAAK,CAACG,cAAc,CAAC,CAAC;QACtBJ,MAAM,CAAC,IAAI,EAAEC,KAAK,CAAC;MACrB;IACF;EACF,CAAC;EACD,MAAM1F,IAAI,GAAGgE,WAAW,KAAK,IAAI,IAAIJ,SAAS;EAC9C,MAAMiE,UAAU,GAAGnC,KAAK,IAAI;IAC1B;IACA,IAAI,CAAC1F,IAAI,IAAIyC,MAAM,EAAE;MACnB;MACA6E,MAAM,CAACC,cAAc,CAAC7B,KAAK,EAAE,QAAQ,EAAE;QACrC8B,QAAQ,EAAE,IAAI;QACdlE,KAAK,EAAE;UACLA,KAAK;UACLvE;QACF;MACF,CAAC,CAAC;MACF0D,MAAM,CAACiD,KAAK,CAAC;IACf;EACF,CAAC;EACD,OAAOlC,KAAK,CAAC,cAAc,CAAC;EAC5B,IAAIvC,OAAO;EACX,IAAI6G,aAAa;EACjB,MAAMC,eAAe,GAAG,EAAE;EAC1B,IAAIC,cAAc,GAAG,KAAK;EAC1B,IAAIC,UAAU,GAAG,KAAK;;EAEtB;EACA,IAAI9J,QAAQ,CAAC;IACXmF;EACF,CAAC,CAAC,IAAInB,YAAY,EAAE;IAClB,IAAIa,WAAW,EAAE;MACf/B,OAAO,GAAG+B,WAAW,CAACM,KAAK,CAAC;IAC9B,CAAC,MAAM;MACL0E,cAAc,GAAG,IAAI;IACvB;EACF;EACA,MAAME,KAAK,GAAGnC,aAAa,CAACoC,GAAG,CAAChC,KAAK,IAAI;IACvC,IAAI,EAAE,aAAa5I,KAAK,CAAC6K,cAAc,CAACjC,KAAK,CAAC,EAAE;MAC9C,OAAO,IAAI;IACb;IACA,IAAIkC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI/K,UAAU,CAAC2I,KAAK,CAAC,EAAE;QACrBqC,OAAO,CAACjJ,KAAK,CAAC,CAAC,iEAAiE,EAAE,sCAAsC,CAAC,CAACkJ,IAAI,CAAC,IAAI,CAAC,CAAC;MACvI;IACF;IACA,IAAIC,QAAQ;IACZ,IAAIlJ,QAAQ,EAAE;MACZ,IAAI,CAACmH,KAAK,CAACC,OAAO,CAACtD,KAAK,CAAC,EAAE;QACzB,MAAM,IAAIqF,KAAK,CAACN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,yCAAyC,GAAG,oDAAoD,GAAGlL,mBAAmB,CAAC,CAAC,CAAC,CAAC;MACpL;MACAqL,QAAQ,GAAGpF,KAAK,CAACsF,IAAI,CAACC,CAAC,IAAIjI,cAAc,CAACiI,CAAC,EAAE1C,KAAK,CAACjH,KAAK,CAACoE,KAAK,CAAC,CAAC;MAChE,IAAIoF,QAAQ,IAAIV,cAAc,EAAE;QAC9BD,eAAe,CAACf,IAAI,CAACb,KAAK,CAACjH,KAAK,CAAC6C,QAAQ,CAAC;MAC5C;IACF,CAAC,MAAM;MACL2G,QAAQ,GAAG9H,cAAc,CAAC0C,KAAK,EAAE6C,KAAK,CAACjH,KAAK,CAACoE,KAAK,CAAC;MACnD,IAAIoF,QAAQ,IAAIV,cAAc,EAAE;QAC9BF,aAAa,GAAG3B,KAAK,CAACjH,KAAK,CAAC6C,QAAQ;MACtC;IACF;IACA,IAAI2G,QAAQ,EAAE;MACZT,UAAU,GAAG,IAAI;IACnB;IACA,OAAO,aAAa1K,KAAK,CAACuL,YAAY,CAAC3C,KAAK,EAAE;MAC5C,eAAe,EAAEuC,QAAQ,GAAG,MAAM,GAAG,OAAO;MAC5CxB,OAAO,EAAEX,eAAe,CAACJ,KAAK,CAAC;MAC/B4C,OAAO,EAAErD,KAAK,IAAI;QAChB,IAAIA,KAAK,CAACkC,GAAG,KAAK,GAAG,EAAE;UACrB;UACA;UACA;UACAlC,KAAK,CAACG,cAAc,CAAC,CAAC;QACxB;QACA,IAAIM,KAAK,CAACjH,KAAK,CAAC6J,OAAO,EAAE;UACvB5C,KAAK,CAACjH,KAAK,CAAC6J,OAAO,CAACrD,KAAK,CAAC;QAC5B;MACF,CAAC;MACDsD,IAAI,EAAE,QAAQ;MACdN,QAAQ;MACRpF,KAAK,EAAE2B,SAAS;MAChB;MACA,YAAY,EAAEkB,KAAK,CAACjH,KAAK,CAACoE,KAAK,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI+E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA;IACAhL,KAAK,CAACwH,SAAS,CAAC,MAAM;MACpB,IAAI,CAACkD,UAAU,IAAI,CAACzI,QAAQ,IAAI8D,KAAK,KAAK,EAAE,EAAE;QAC5C,MAAM2F,MAAM,GAAGlD,aAAa,CAACoC,GAAG,CAAChC,KAAK,IAAIA,KAAK,CAACjH,KAAK,CAACoE,KAAK,CAAC;QAC5DkF,OAAO,CAACU,IAAI,CAAC,CAAC,kDAAkD5F,KAAK,qBAAqBvE,IAAI,GAAG,UAAUA,IAAI,KAAK,GAAG,EAAE,YAAY,EAAE,6EAA6E,EAAE,4BAA4BkK,MAAM,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,IAAI,IAAI,CAAC,CAACjB,GAAG,CAACiB,CAAC,IAAI,KAAKA,CAAC,IAAI,CAAC,CAACX,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAAC;MACzU;IACF,CAAC,EAAE,CAACR,UAAU,EAAElC,aAAa,EAAEvG,QAAQ,EAAET,IAAI,EAAEuE,KAAK,CAAC,CAAC;EACxD;EACA,IAAI0E,cAAc,EAAE;IAClB,IAAIxI,QAAQ,EAAE;MACZ,IAAIuI,eAAe,CAACsB,MAAM,KAAK,CAAC,EAAE;QAChCpI,OAAO,GAAG,IAAI;MAChB,CAAC,MAAM;QACLA,OAAO,GAAG8G,eAAe,CAACuB,MAAM,CAAC,CAACC,MAAM,EAAEpD,KAAK,EAAEqD,KAAK,KAAK;UACzDD,MAAM,CAACvC,IAAI,CAACb,KAAK,CAAC;UAClB,IAAIqD,KAAK,GAAGzB,eAAe,CAACsB,MAAM,GAAG,CAAC,EAAE;YACtCE,MAAM,CAACvC,IAAI,CAAC,IAAI,CAAC;UACnB;UACA,OAAOuC,MAAM;QACf,CAAC,EAAE,EAAE,CAAC;MACR;IACF,CAAC,MAAM;MACLtI,OAAO,GAAG6G,aAAa;IACzB;EACF;;EAEA;EACA,IAAI2B,YAAY,GAAGpF,iBAAiB;EACpC,IAAI,CAACvC,SAAS,IAAIsC,gBAAgB,IAAIJ,WAAW,EAAE;IACjDyF,YAAY,GAAG9E,aAAa,CAACK,WAAW;EAC1C;EACA,IAAI7B,QAAQ;EACZ,IAAI,OAAOC,YAAY,KAAK,WAAW,EAAE;IACvCD,QAAQ,GAAGC,YAAY;EACzB,CAAC,MAAM;IACLD,QAAQ,GAAG9B,QAAQ,GAAG,IAAI,GAAG,CAAC;EAChC;EACA,MAAMqI,QAAQ,GAAGxG,kBAAkB,CAACyG,EAAE,KAAK5K,IAAI,GAAG,wBAAwBA,IAAI,EAAE,GAAGkG,SAAS,CAAC;EAC7F,MAAM7F,UAAU,GAAG;IACjB,GAAGF,KAAK;IACRI,OAAO;IACPgE,KAAK;IACLtD,IAAI;IACJT;EACF,CAAC;EACD,MAAM6B,OAAO,GAAGD,iBAAiB,CAAC/B,UAAU,CAAC;EAC7C,MAAMwK,UAAU,GAAG;IACjB,GAAGpH,SAAS,CAACqH,UAAU;IACvB,GAAGrH,SAAS,CAACsH,SAAS,EAAEC;EAC1B,CAAC;EACD,MAAMC,SAAS,GAAG;IAChB,GAAGxH,SAAS,CAACyH,aAAa;IAC1B,GAAGzH,SAAS,CAACsH,SAAS,EAAEI;EAC1B,CAAC;EACD,MAAMC,SAAS,GAAGvM,KAAK,CAAC,CAAC;EACzB,OAAO,aAAaiB,KAAK,CAACtB,KAAK,CAAC6M,QAAQ,EAAE;IACxCrI,QAAQ,EAAE,CAAC,aAAapD,IAAI,CAACG,YAAY,EAAE;MACzCuL,EAAE,EAAE,KAAK;MACT3I,GAAG,EAAE8C,gBAAgB;MACrBrB,QAAQ,EAAEA,QAAQ;MAClB6F,IAAI,EAAE,UAAU;MAChB,eAAe,EAAEhJ,IAAI,GAAGmK,SAAS,GAAGlF,SAAS;MAC7C,eAAe,EAAE5D,QAAQ,GAAG,MAAM,GAAG4D,SAAS;MAC9C,eAAe,EAAEjF,IAAI,GAAG,MAAM,GAAG,OAAO;MACxC,eAAe,EAAE,SAAS;MAC1B,YAAY,EAAE4B,SAAS;MACvB,iBAAiB,EAAE,CAACW,OAAO,EAAEmH,QAAQ,CAAC,CAACP,MAAM,CAACmB,OAAO,CAAC,CAAC7B,IAAI,CAAC,GAAG,CAAC,IAAIxD,SAAS;MAC7E,kBAAkB,EAAEtD,eAAe;MACnC,eAAe,EAAEsB,QAAQ,GAAG,MAAM,GAAGgC,SAAS;MAC9C,cAAc,EAAE1F,KAAK,GAAG,MAAM,GAAG0F,SAAS;MAC1CsF,SAAS,EAAE9C,aAAa;MACxB+C,WAAW,EAAEnJ,QAAQ,IAAI0B,QAAQ,GAAG,IAAI,GAAG4C,eAAe;MAC1DlD,MAAM,EAAEoF,UAAU;MAClBjF,OAAO,EAAEA,OAAO;MAChB,GAAGM,kBAAkB;MACrB9D,UAAU,EAAEA,UAAU;MACtB4C,SAAS,EAAEtE,IAAI,CAACwF,kBAAkB,CAAClB,SAAS,EAAEZ,OAAO,CAAC/B,MAAM,EAAE2C,SAAS;MACvE;MAAA;;MAEA2H,EAAE,EAAED,QAAQ;MACZ3H,QAAQ,EAAEf,OAAO,CAACC,OAAO,CAAC;MAAG;MAC7B3D,KAAK,KAAKA,KAAK,GAAG,aAAaqB,IAAI,CAAC,MAAM,EAAE;QAC1CqD,SAAS,EAAE,aAAa;QACxB,aAAa,EAAE,IAAI;QACnBD,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC,GAAGd;IACR,CAAC,CAAC,EAAE,aAAatC,IAAI,CAACuB,iBAAiB,EAAE;MACvC,cAAc,EAAEX,KAAK;MACrB+D,KAAK,EAAEqD,KAAK,CAACC,OAAO,CAACtD,KAAK,CAAC,GAAGA,KAAK,CAACmF,IAAI,CAAC,GAAG,CAAC,GAAGnF,KAAK;MACrDvE,IAAI,EAAEA,IAAI;MACV2C,GAAG,EAAEW,QAAQ;MACb,aAAa,EAAE,IAAI;MACnBK,QAAQ,EAAEwD,YAAY;MACtB/C,QAAQ,EAAE,CAAC,CAAC;MACZ9B,QAAQ,EAAEA,QAAQ;MAClBW,SAAS,EAAEZ,OAAO,CAACG,WAAW;MAC9BM,SAAS,EAAEA,SAAS;MACpBoB,QAAQ,EAAEA,QAAQ;MAClB,GAAGO,KAAK;MACRpE,UAAU,EAAEA;IACd,CAAC,CAAC,EAAE,aAAaT,IAAI,CAACmB,UAAU,EAAE;MAChCuK,EAAE,EAAEjI,aAAa;MACjBJ,SAAS,EAAEZ,OAAO,CAACrB,IAAI;MACvBX,UAAU,EAAEA;IACd,CAAC,CAAC,EAAE,aAAaT,IAAI,CAACX,IAAI,EAAE;MAC1B2L,EAAE,EAAE,QAAQ5K,IAAI,IAAI,EAAE,EAAE;MACxB0L,QAAQ,EAAE9F,aAAa;MACvB3E,IAAI,EAAEA,IAAI;MACV2C,OAAO,EAAEmD,WAAW;MACpB4E,YAAY,EAAE;QACZC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACd,CAAC;MACDC,eAAe,EAAE;QACfF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACd,CAAC;MACD,GAAGpI,SAAS;MACZsH,SAAS,EAAE;QACT,GAAGtH,SAAS,CAACsH,SAAS;QACtBI,IAAI,EAAE;UACJ,iBAAiB,EAAE3H,OAAO;UAC1ByG,IAAI,EAAE,SAAS;UACf,sBAAsB,EAAExJ,QAAQ,GAAG,MAAM,GAAGyF,SAAS;UACrD6F,eAAe,EAAE,IAAI;UACrBnB,EAAE,EAAEQ,SAAS;UACb,GAAGH;QACL,CAAC;QACDD,KAAK,EAAE;UACL,GAAGH,UAAU;UACbmB,KAAK,EAAE;YACLC,QAAQ,EAAEvB,YAAY;YACtB,IAAIG,UAAU,IAAI,IAAI,GAAGA,UAAU,CAACmB,KAAK,GAAG,IAAI;UAClD;QACF;MACF,CAAC;MACDhJ,QAAQ,EAAEmG;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/G,WAAW,CAACyJ,SAAS,GAAG;EAC9D;AACF;AACA;EACE,kBAAkB,EAAExN,SAAS,CAACyN,MAAM;EACpC;AACF;AACA;EACE,YAAY,EAAEzN,SAAS,CAACyN,MAAM;EAC9B;AACF;AACA;EACErJ,SAAS,EAAEpE,SAAS,CAAC0N,IAAI;EACzB;AACF;AACA;AACA;EACErJ,SAAS,EAAErE,SAAS,CAAC0N,IAAI;EACzB;AACF;AACA;AACA;EACEpJ,QAAQ,EAAEtE,SAAS,CAACiH,IAAI;EACxB;AACF;AACA;EACEtD,OAAO,EAAE3D,SAAS,CAAC2N,MAAM;EACzB;AACF;AACA;EACEpJ,SAAS,EAAEvE,SAAS,CAACyN,MAAM;EAC3B;AACF;AACA;AACA;EACEjJ,WAAW,EAAExE,SAAS,CAAC0N,IAAI;EAC3B;AACF;AACA;EACEjJ,YAAY,EAAEzE,SAAS,CAAC4N,GAAG;EAC3B;AACF;AACA;EACEhK,QAAQ,EAAE5D,SAAS,CAAC0N,IAAI;EACxB;AACF;AACA;EACEhJ,YAAY,EAAE1E,SAAS,CAAC0N,IAAI;EAC5B;AACF;AACA;EACE5L,KAAK,EAAE9B,SAAS,CAAC0N,IAAI;EACrB;AACF;AACA;EACE/I,aAAa,EAAE3E,SAAS,CAAC6N,WAAW,CAACC,UAAU;EAC/C;AACF;AACA;AACA;EACElJ,QAAQ,EAAExE,OAAO;EACjB;AACF;AACA;AACA;EACE0E,OAAO,EAAE9E,SAAS,CAACyN,MAAM;EACzB;AACF;AACA;EACE1I,SAAS,EAAE/E,SAAS,CAAC2N,MAAM;EAC3B;AACF;AACA;EACE5L,QAAQ,EAAE/B,SAAS,CAAC0N,IAAI;EACxB;AACF;AACA;EACEpM,IAAI,EAAEtB,SAAS,CAACyN,MAAM;EACtB;AACF;AACA;EACEzI,MAAM,EAAEhF,SAAS,CAAC+N,IAAI;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;EACE9I,QAAQ,EAAEjF,SAAS,CAAC+N,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACE7I,OAAO,EAAElF,SAAS,CAAC+N,IAAI;EACvB;AACF;AACA;EACE5I,OAAO,EAAEnF,SAAS,CAAC+N,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;EACE3I,MAAM,EAAEpF,SAAS,CAAC+N,IAAI;EACtB;AACF;AACA;EACExL,IAAI,EAAEvC,SAAS,CAAC0N,IAAI;EACpB;AACF;AACA;EACEpI,QAAQ,EAAEtF,SAAS,CAAC0N,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACEnI,WAAW,EAAEvF,SAAS,CAAC+N,IAAI;EAC3B;AACF;AACA;EACEvI,QAAQ,EAAExF,SAAS,CAAC0N,IAAI;EACxB;AACF;AACA;EACEjI,kBAAkB,EAAEzF,SAAS,CAAC2N,MAAM;EACpC;AACF;AACA;EACEjI,QAAQ,EAAE1F,SAAS,CAACgO,SAAS,CAAC,CAAChO,SAAS,CAACiO,MAAM,EAAEjO,SAAS,CAACyN,MAAM,CAAC,CAAC;EACnE;AACF;AACA;EACE7H,IAAI,EAAE5F,SAAS,CAAC4N,GAAG;EACnB;AACF;AACA;EACE/H,KAAK,EAAE7F,SAAS,CAAC4N,GAAG;EACpB;AACF;AACA;EACE/L,OAAO,EAAE7B,SAAS,CAACkO,KAAK,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;AAC7D,CAAC,GAAG,KAAK,CAAC;AACV,eAAenK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}