{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{Container}from'react-bootstrap';import{AuthProvider}from'./contexts/AuthContext';import Navbar from'./components/layout/Navbar';import Home from'./pages/Home';import Login from'./pages/auth/Login';import Register from'./pages/auth/Register';import ForgotPassword from'./pages/auth/ForgotPassword';import ResetPassword from'./pages/auth/ResetPassword';import Profile from'./pages/user/Profile';import EditProfile from'./pages/user/EditProfile';import PageView from'./pages/cms/PageView';import ProtectedRoute from'./components/auth/ProtectedRoute';import EmailVerificationNotice from'./components/auth/EmailVerificationNotice';// Material Dashboard imports\nimport DashboardRoute from'./components/dashboard/DashboardRoute';import Dashboard from'./pages/dashboard/Dashboard';import Credit from'./pages/dashboard/Credit';import Order from'./pages/dashboard/Order';import Orders from'./pages/dashboard/Orders';import'bootstrap/dist/css/bootstrap.min.css';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(DashboardRoute,{children:/*#__PURE__*/_jsx(Dashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard/credit\",element:/*#__PURE__*/_jsx(DashboardRoute,{children:/*#__PURE__*/_jsx(Credit,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard/order\",element:/*#__PURE__*/_jsx(DashboardRoute,{children:/*#__PURE__*/_jsx(Order,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard/orders\",element:/*#__PURE__*/_jsx(DashboardRoute,{children:/*#__PURE__*/_jsx(Orders,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/*\",element:/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[/*#__PURE__*/_jsx(Navbar,{}),/*#__PURE__*/_jsx(Container,{className:\"mt-4\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:/*#__PURE__*/_jsx(Register,{})}),/*#__PURE__*/_jsx(Route,{path:\"/forgot-password\",element:/*#__PURE__*/_jsx(ForgotPassword,{})}),/*#__PURE__*/_jsx(Route,{path:\"/reset-password\",element:/*#__PURE__*/_jsx(ResetPassword,{})}),/*#__PURE__*/_jsx(Route,{path:\"/pages/:slug\",element:/*#__PURE__*/_jsx(PageView,{})}),/*#__PURE__*/_jsx(Route,{path:\"/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Profile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/profile/edit\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(EditProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/email-verification\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(EmailVerificationNotice,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})]})})]})})]})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Container", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Home", "<PERSON><PERSON>", "Register", "ForgotPassword", "ResetPassword", "Profile", "EditProfile", "<PERSON><PERSON><PERSON><PERSON>", "ProtectedRoute", "EmailVerificationNotice", "DashboardRoute", "Dashboard", "Credit", "Order", "Orders", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "path", "element", "className", "to", "replace"], "sources": ["C:/laragon/www/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Container } from 'react-bootstrap';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Navbar from './components/layout/Navbar';\nimport Home from './pages/Home';\nimport Login from './pages/auth/Login';\nimport Register from './pages/auth/Register';\nimport ForgotPassword from './pages/auth/ForgotPassword';\nimport ResetPassword from './pages/auth/ResetPassword';\nimport Profile from './pages/user/Profile';\nimport EditProfile from './pages/user/EditProfile';\nimport PageView from './pages/cms/PageView';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\nimport EmailVerificationNotice from './components/auth/EmailVerificationNotice';\n// Material Dashboard imports\nimport DashboardRoute from './components/dashboard/DashboardRoute';\nimport Dashboard from './pages/dashboard/Dashboard';\nimport Credit from './pages/dashboard/Credit';\nimport Order from './pages/dashboard/Order';\nimport Orders from './pages/dashboard/Orders';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './App.css';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <Routes>\n          {/* Dashboard routes - these don't use the main layout */}\n          <Route\n            path=\"/dashboard\"\n            element={\n              <DashboardRoute>\n                <Dashboard />\n              </DashboardRoute>\n            }\n          />\n          <Route\n            path=\"/dashboard/credit\"\n            element={\n              <DashboardRoute>\n                <Credit />\n              </DashboardRoute>\n            }\n          />\n          <Route\n            path=\"/dashboard/order\"\n            element={\n              <DashboardRoute>\n                <Order />\n              </DashboardRoute>\n            }\n          />\n          <Route\n            path=\"/dashboard/orders\"\n            element={\n              <DashboardRoute>\n                <Orders />\n              </DashboardRoute>\n            }\n          />\n\n\n          {/* Main application routes with Bootstrap layout */}\n          <Route\n            path=\"/*\"\n            element={\n              <div className=\"App\">\n                <Navbar />\n                <Container className=\"mt-4\">\n                  <Routes>\n                    {/* Public routes */}\n                    <Route path=\"/\" element={<Home />} />\n                    <Route path=\"/login\" element={<Login />} />\n                    <Route path=\"/register\" element={<Register />} />\n                    <Route path=\"/forgot-password\" element={<ForgotPassword />} />\n                    <Route path=\"/reset-password\" element={<ResetPassword />} />\n                    <Route path=\"/pages/:slug\" element={<PageView />} />\n\n                    {/* Protected routes */}\n                    <Route\n                      path=\"/profile\"\n                      element={\n                        <ProtectedRoute>\n                          <Profile />\n                        </ProtectedRoute>\n                      }\n                    />\n                    <Route\n                      path=\"/profile/edit\"\n                      element={\n                        <ProtectedRoute>\n                          <EditProfile />\n                        </ProtectedRoute>\n                      }\n                    />\n\n                    {/* Email verification notice */}\n                    <Route\n                      path=\"/email-verification\"\n                      element={\n                        <ProtectedRoute>\n                          <EmailVerificationNotice />\n                        </ProtectedRoute>\n                      }\n                    />\n\n                    {/* Catch all route */}\n                    <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n                  </Routes>\n                </Container>\n              </div>\n            }\n          />\n        </Routes>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OAASC,SAAS,KAAQ,iBAAiB,CAC3C,OAASC,YAAY,KAAQ,wBAAwB,CACrD,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,KAAK,KAAM,oBAAoB,CACtC,MAAO,CAAAC,QAAQ,KAAM,uBAAuB,CAC5C,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,OAAO,KAAM,sBAAsB,CAC1C,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,QAAQ,KAAM,sBAAsB,CAC3C,MAAO,CAAAC,cAAc,KAAM,kCAAkC,CAC7D,MAAO,CAAAC,uBAAuB,KAAM,2CAA2C,CAC/E;AACA,MAAO,CAAAC,cAAc,KAAM,uCAAuC,CAClE,MAAO,CAAAC,SAAS,KAAM,6BAA6B,CACnD,MAAO,CAAAC,MAAM,KAAM,0BAA0B,CAC7C,MAAO,CAAAC,KAAK,KAAM,yBAAyB,CAC3C,MAAO,CAAAC,MAAM,KAAM,0BAA0B,CAC7C,MAAO,sCAAsC,CAC7C,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAAClB,YAAY,EAAAsB,QAAA,cACXJ,IAAA,CAACvB,MAAM,EAAA2B,QAAA,cACLF,KAAA,CAACxB,MAAM,EAAA0B,QAAA,eAELJ,IAAA,CAACrB,KAAK,EACJ0B,IAAI,CAAC,YAAY,CACjBC,OAAO,cACLN,IAAA,CAACN,cAAc,EAAAU,QAAA,cACbJ,IAAA,CAACL,SAAS,GAAE,CAAC,CACC,CACjB,CACF,CAAC,cACFK,IAAA,CAACrB,KAAK,EACJ0B,IAAI,CAAC,mBAAmB,CACxBC,OAAO,cACLN,IAAA,CAACN,cAAc,EAAAU,QAAA,cACbJ,IAAA,CAACJ,MAAM,GAAE,CAAC,CACI,CACjB,CACF,CAAC,cACFI,IAAA,CAACrB,KAAK,EACJ0B,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACLN,IAAA,CAACN,cAAc,EAAAU,QAAA,cACbJ,IAAA,CAACH,KAAK,GAAE,CAAC,CACK,CACjB,CACF,CAAC,cACFG,IAAA,CAACrB,KAAK,EACJ0B,IAAI,CAAC,mBAAmB,CACxBC,OAAO,cACLN,IAAA,CAACN,cAAc,EAAAU,QAAA,cACbJ,IAAA,CAACF,MAAM,GAAE,CAAC,CACI,CACjB,CACF,CAAC,cAIFE,IAAA,CAACrB,KAAK,EACJ0B,IAAI,CAAC,IAAI,CACTC,OAAO,cACLJ,KAAA,QAAKK,SAAS,CAAC,KAAK,CAAAH,QAAA,eAClBJ,IAAA,CAACjB,MAAM,GAAE,CAAC,cACViB,IAAA,CAACnB,SAAS,EAAC0B,SAAS,CAAC,MAAM,CAAAH,QAAA,cACzBF,KAAA,CAACxB,MAAM,EAAA0B,QAAA,eAELJ,IAAA,CAACrB,KAAK,EAAC0B,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEN,IAAA,CAAChB,IAAI,GAAE,CAAE,CAAE,CAAC,cACrCgB,IAAA,CAACrB,KAAK,EAAC0B,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEN,IAAA,CAACf,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3Ce,IAAA,CAACrB,KAAK,EAAC0B,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEN,IAAA,CAACd,QAAQ,GAAE,CAAE,CAAE,CAAC,cACjDc,IAAA,CAACrB,KAAK,EAAC0B,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEN,IAAA,CAACb,cAAc,GAAE,CAAE,CAAE,CAAC,cAC9Da,IAAA,CAACrB,KAAK,EAAC0B,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEN,IAAA,CAACZ,aAAa,GAAE,CAAE,CAAE,CAAC,cAC5DY,IAAA,CAACrB,KAAK,EAAC0B,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEN,IAAA,CAACT,QAAQ,GAAE,CAAE,CAAE,CAAC,cAGpDS,IAAA,CAACrB,KAAK,EACJ0B,IAAI,CAAC,UAAU,CACfC,OAAO,cACLN,IAAA,CAACR,cAAc,EAAAY,QAAA,cACbJ,IAAA,CAACX,OAAO,GAAE,CAAC,CACG,CACjB,CACF,CAAC,cACFW,IAAA,CAACrB,KAAK,EACJ0B,IAAI,CAAC,eAAe,CACpBC,OAAO,cACLN,IAAA,CAACR,cAAc,EAAAY,QAAA,cACbJ,IAAA,CAACV,WAAW,GAAE,CAAC,CACD,CACjB,CACF,CAAC,cAGFU,IAAA,CAACrB,KAAK,EACJ0B,IAAI,CAAC,qBAAqB,CAC1BC,OAAO,cACLN,IAAA,CAACR,cAAc,EAAAY,QAAA,cACbJ,IAAA,CAACP,uBAAuB,GAAE,CAAC,CACb,CACjB,CACF,CAAC,cAGFO,IAAA,CAACrB,KAAK,EAAC0B,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEN,IAAA,CAACpB,QAAQ,EAAC4B,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EAClD,CAAC,CACA,CAAC,EACT,CACN,CACF,CAAC,EACI,CAAC,CACH,CAAC,CACG,CAAC,CAEnB,CAEA,cAAe,CAAAN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}