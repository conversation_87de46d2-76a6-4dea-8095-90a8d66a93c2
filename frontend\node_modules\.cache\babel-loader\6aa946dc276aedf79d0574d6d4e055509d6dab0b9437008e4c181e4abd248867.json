{"ast": null, "code": "import React from'react';import{Card,CardContent,Typography,Box,useTheme}from'@mui/material';// This is a placeholder component showing how to integrate charts\n// To use real charts, install a charting library like:\n// npm install react-chartjs-2 chart.js\n// or\n// npm install recharts\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ChartExample=_ref=>{let{title,data,type='line',height=300}=_ref;const theme=useTheme();// Mock chart visualization - replace with actual chart library\nconst renderMockChart=()=>{const maxValue=Math.max(...data.datasets[0].data);return/*#__PURE__*/_jsx(Box,{sx:{height,display:'flex',alignItems:'end',gap:1,p:2},children:data.labels.map((label,index)=>{const value=data.datasets[0].data[index];const barHeight=value/maxValue*(height-60);return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',flex:1},children:[/*#__PURE__*/_jsx(Box,{sx:{width:'100%',maxWidth:40,height:barHeight,backgroundColor:theme.palette.primary.main,borderRadius:1,mb:1,transition:'all 0.3s ease','&:hover':{backgroundColor:theme.palette.primary.dark,transform:'scale(1.05)'}}}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"textSecondary\",children:label}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"bold\",children:value})]},label);})});};return/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:title}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",sx:{mb:2},children:\"This is a mock chart. Replace with actual chart library implementation.\"}),renderMockChart()]})});};// Example usage component\nexport const DashboardCharts=()=>{const sampleData={labels:['Jan','Feb','Mar','Apr','May','Jun'],datasets:[{label:'Monthly Sales',data:[65,59,80,81,56,55],backgroundColor:'#1976d2',borderColor:'#1976d2',borderWidth:2}]};const userGrowthData={labels:['Week 1','Week 2','Week 3','Week 4'],datasets:[{label:'New Users',data:[12,19,15,25],backgroundColor:'#4caf50',borderColor:'#4caf50',borderWidth:2}]};return/*#__PURE__*/_jsxs(Box,{sx:{display:'grid',gridTemplateColumns:{xs:'1fr',md:'1fr 1fr'},gap:3},children:[/*#__PURE__*/_jsx(ChartExample,{title:\"Monthly Sales Overview\",data:sampleData,type:\"bar\"}),/*#__PURE__*/_jsx(ChartExample,{title:\"User Growth\",data:userGrowthData,type:\"line\"})]});};// Instructions for integrating real charts:\n/*\n1. Install a charting library:\n   npm install react-chartjs-2 chart.js\n\n2. Import the chart components:\n   import {\n     Chart as ChartJS,\n     CategoryScale,\n     LinearScale,\n     BarElement,\n     LineElement,\n     PointElement,\n     Title,\n     Tooltip,\n     Legend,\n   } from 'chart.js';\n   import { Bar, Line } from 'react-chartjs-2';\n\n3. Register the components:\n   ChartJS.register(\n     CategoryScale,\n     LinearScale,\n     BarElement,\n     LineElement,\n     PointElement,\n     Title,\n     Tooltip,\n     Legend\n   );\n\n4. Replace the mock chart with real chart:\n   <Bar data={data} options={options} />\n\n5. For Recharts alternative:\n   npm install recharts\n   \n   import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';\n   \n   <BarChart width={400} height={300} data={data}>\n     <CartesianGrid strokeDasharray=\"3 3\" />\n     <XAxis dataKey=\"name\" />\n     <YAxis />\n     <Tooltip />\n     <Legend />\n     <Bar dataKey=\"value\" fill=\"#1976d2\" />\n   </BarChart>\n*/export default ChartExample;", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Box", "useTheme", "jsx", "_jsx", "jsxs", "_jsxs", "Chart<PERSON>xample", "_ref", "title", "data", "type", "height", "theme", "renderMockChart", "maxValue", "Math", "max", "datasets", "sx", "display", "alignItems", "gap", "p", "children", "labels", "map", "label", "index", "value", "barHeight", "flexDirection", "flex", "width", "max<PERSON><PERSON><PERSON>", "backgroundColor", "palette", "primary", "main", "borderRadius", "mb", "transition", "dark", "transform", "variant", "color", "fontWeight", "gutterBottom", "DashboardCharts", "sampleData", "borderColor", "borderWidth", "userGrowthData", "gridTemplateColumns", "xs", "md"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/ChartExample.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Card,\n  CardContent,\n  Typography,\n  Box,\n  useTheme,\n} from '@mui/material';\n\n// This is a placeholder component showing how to integrate charts\n// To use real charts, install a charting library like:\n// npm install react-chartjs-2 chart.js\n// or\n// npm install recharts\n\ninterface ChartData {\n  labels: string[];\n  datasets: {\n    label: string;\n    data: number[];\n    backgroundColor?: string;\n    borderColor?: string;\n    borderWidth?: number;\n  }[];\n}\n\ninterface ChartExampleProps {\n  title: string;\n  data: ChartData;\n  type?: 'line' | 'bar' | 'pie' | 'doughnut';\n  height?: number;\n}\n\nconst ChartExample: React.FC<ChartExampleProps> = ({\n  title,\n  data,\n  type = 'line',\n  height = 300,\n}) => {\n  const theme = useTheme();\n\n  // Mock chart visualization - replace with actual chart library\n  const renderMockChart = () => {\n    const maxValue = Math.max(...data.datasets[0].data);\n    \n    return (\n      <Box sx={{ height, display: 'flex', alignItems: 'end', gap: 1, p: 2 }}>\n        {data.labels.map((label, index) => {\n          const value = data.datasets[0].data[index];\n          const barHeight = (value / maxValue) * (height - 60);\n          \n          return (\n            <Box\n              key={label}\n              sx={{\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                flex: 1,\n              }}\n            >\n              <Box\n                sx={{\n                  width: '100%',\n                  maxWidth: 40,\n                  height: barHeight,\n                  backgroundColor: theme.palette.primary.main,\n                  borderRadius: 1,\n                  mb: 1,\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    backgroundColor: theme.palette.primary.dark,\n                    transform: 'scale(1.05)',\n                  },\n                }}\n              />\n              <Typography variant=\"caption\" color=\"textSecondary\">\n                {label}\n              </Typography>\n              <Typography variant=\"body2\" fontWeight=\"bold\">\n                {value}\n              </Typography>\n            </Box>\n          );\n        })}\n      </Box>\n    );\n  };\n\n  return (\n    <Card>\n      <CardContent>\n        <Typography variant=\"h6\" gutterBottom>\n          {title}\n        </Typography>\n        <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mb: 2 }}>\n          This is a mock chart. Replace with actual chart library implementation.\n        </Typography>\n        {renderMockChart()}\n      </CardContent>\n    </Card>\n  );\n};\n\n// Example usage component\nexport const DashboardCharts: React.FC = () => {\n  const sampleData: ChartData = {\n    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n    datasets: [\n      {\n        label: 'Monthly Sales',\n        data: [65, 59, 80, 81, 56, 55],\n        backgroundColor: '#1976d2',\n        borderColor: '#1976d2',\n        borderWidth: 2,\n      },\n    ],\n  };\n\n  const userGrowthData: ChartData = {\n    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n    datasets: [\n      {\n        label: 'New Users',\n        data: [12, 19, 15, 25],\n        backgroundColor: '#4caf50',\n        borderColor: '#4caf50',\n        borderWidth: 2,\n      },\n    ],\n  };\n\n  return (\n    <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>\n      <ChartExample\n        title=\"Monthly Sales Overview\"\n        data={sampleData}\n        type=\"bar\"\n      />\n      <ChartExample\n        title=\"User Growth\"\n        data={userGrowthData}\n        type=\"line\"\n      />\n    </Box>\n  );\n};\n\n// Instructions for integrating real charts:\n/*\n1. Install a charting library:\n   npm install react-chartjs-2 chart.js\n\n2. Import the chart components:\n   import {\n     Chart as ChartJS,\n     CategoryScale,\n     LinearScale,\n     BarElement,\n     LineElement,\n     PointElement,\n     Title,\n     Tooltip,\n     Legend,\n   } from 'chart.js';\n   import { Bar, Line } from 'react-chartjs-2';\n\n3. Register the components:\n   ChartJS.register(\n     CategoryScale,\n     LinearScale,\n     BarElement,\n     LineElement,\n     PointElement,\n     Title,\n     Tooltip,\n     Legend\n   );\n\n4. Replace the mock chart with real chart:\n   <Bar data={data} options={options} />\n\n5. For Recharts alternative:\n   npm install recharts\n   \n   import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';\n   \n   <BarChart width={400} height={300} data={data}>\n     <CartesianGrid strokeDasharray=\"3 3\" />\n     <XAxis dataKey=\"name\" />\n     <YAxis />\n     <Tooltip />\n     <Legend />\n     <Bar dataKey=\"value\" fill=\"#1976d2\" />\n   </BarChart>\n*/\n\nexport default ChartExample;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,IAAI,CACJC,WAAW,CACXC,UAAU,CACVC,GAAG,CACHC,QAAQ,KACH,eAAe,CAEtB;AACA;AACA;AACA;AACA;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAoBA,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAK5C,IAL6C,CACjDC,KAAK,CACLC,IAAI,CACJC,IAAI,CAAG,MAAM,CACbC,MAAM,CAAG,GACX,CAAC,CAAAJ,IAAA,CACC,KAAM,CAAAK,KAAK,CAAGX,QAAQ,CAAC,CAAC,CAExB;AACA,KAAM,CAAAY,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,CAEnD,mBACEN,IAAA,CAACH,GAAG,EAACkB,EAAE,CAAE,CAAEP,MAAM,CAAEQ,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,KAAK,CAAEC,GAAG,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAC,QAAA,CACnEd,IAAI,CAACe,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,GAAK,CACjC,KAAM,CAAAC,KAAK,CAAGnB,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACR,IAAI,CAACkB,KAAK,CAAC,CAC1C,KAAM,CAAAE,SAAS,CAAID,KAAK,CAAGd,QAAQ,EAAKH,MAAM,CAAG,EAAE,CAAC,CAEpD,mBACEN,KAAA,CAACL,GAAG,EAEFkB,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfW,aAAa,CAAE,QAAQ,CACvBV,UAAU,CAAE,QAAQ,CACpBW,IAAI,CAAE,CACR,CAAE,CAAAR,QAAA,eAEFpB,IAAA,CAACH,GAAG,EACFkB,EAAE,CAAE,CACFc,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,EAAE,CACZtB,MAAM,CAAEkB,SAAS,CACjBK,eAAe,CAAEtB,KAAK,CAACuB,OAAO,CAACC,OAAO,CAACC,IAAI,CAC3CC,YAAY,CAAE,CAAC,CACfC,EAAE,CAAE,CAAC,CACLC,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTN,eAAe,CAAEtB,KAAK,CAACuB,OAAO,CAACC,OAAO,CAACK,IAAI,CAC3CC,SAAS,CAAE,aACb,CACF,CAAE,CACH,CAAC,cACFvC,IAAA,CAACJ,UAAU,EAAC4C,OAAO,CAAC,SAAS,CAACC,KAAK,CAAC,eAAe,CAAArB,QAAA,CAChDG,KAAK,CACI,CAAC,cACbvB,IAAA,CAACJ,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAACE,UAAU,CAAC,MAAM,CAAAtB,QAAA,CAC1CK,KAAK,CACI,CAAC,GA5BRF,KA6BF,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,CAEV,CAAC,CAED,mBACEvB,IAAA,CAACN,IAAI,EAAA0B,QAAA,cACHlB,KAAA,CAACP,WAAW,EAAAyB,QAAA,eACVpB,IAAA,CAACJ,UAAU,EAAC4C,OAAO,CAAC,IAAI,CAACG,YAAY,MAAAvB,QAAA,CAClCf,KAAK,CACI,CAAC,cACbL,IAAA,CAACJ,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,eAAe,CAAC1B,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAAhB,QAAA,CAAC,yEAEjE,CAAY,CAAC,CACZV,eAAe,CAAC,CAAC,EACP,CAAC,CACV,CAAC,CAEX,CAAC,CAED;AACA,MAAO,MAAM,CAAAkC,eAAyB,CAAGA,CAAA,GAAM,CAC7C,KAAM,CAAAC,UAAqB,CAAG,CAC5BxB,MAAM,CAAE,CAAC,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CAClDP,QAAQ,CAAE,CACR,CACES,KAAK,CAAE,eAAe,CACtBjB,IAAI,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAC9ByB,eAAe,CAAE,SAAS,CAC1Be,WAAW,CAAE,SAAS,CACtBC,WAAW,CAAE,CACf,CAAC,CAEL,CAAC,CAED,KAAM,CAAAC,cAAyB,CAAG,CAChC3B,MAAM,CAAE,CAAC,QAAQ,CAAE,QAAQ,CAAE,QAAQ,CAAE,QAAQ,CAAC,CAChDP,QAAQ,CAAE,CACR,CACES,KAAK,CAAE,WAAW,CAClBjB,IAAI,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CACtByB,eAAe,CAAE,SAAS,CAC1Be,WAAW,CAAE,SAAS,CACtBC,WAAW,CAAE,CACf,CAAC,CAEL,CAAC,CAED,mBACE7C,KAAA,CAACL,GAAG,EAACkB,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEiC,mBAAmB,CAAE,CAAEC,EAAE,CAAE,KAAK,CAAEC,EAAE,CAAE,SAAU,CAAC,CAAEjC,GAAG,CAAE,CAAE,CAAE,CAAAE,QAAA,eACtFpB,IAAA,CAACG,YAAY,EACXE,KAAK,CAAC,wBAAwB,CAC9BC,IAAI,CAAEuC,UAAW,CACjBtC,IAAI,CAAC,KAAK,CACX,CAAC,cACFP,IAAA,CAACG,YAAY,EACXE,KAAK,CAAC,aAAa,CACnBC,IAAI,CAAE0C,cAAe,CACrBzC,IAAI,CAAC,MAAM,CACZ,CAAC,EACC,CAAC,CAEV,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAEA,cAAe,CAAAJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}