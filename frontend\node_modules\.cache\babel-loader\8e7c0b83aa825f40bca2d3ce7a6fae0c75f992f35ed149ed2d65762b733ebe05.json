{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport PageItem, { Ellipsis, First, Last, Next, Prev } from './PageItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Pagination = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    size,\n    ...props\n  } = _ref;\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'pagination');\n  return /*#__PURE__*/_jsx(\"ul\", {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, size && `${decoratedBsPrefix}-${size}`)\n  });\n});\nPagination.displayName = 'Pagination';\nexport default Object.assign(Pagination, {\n  First,\n  Prev,\n  Ellipsis,\n  Item: PageItem,\n  Next,\n  Last\n});", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "PageItem", "El<PERSON><PERSON>", "First", "Last", "Next", "Prev", "jsx", "_jsx", "Pagination", "forwardRef", "_ref", "ref", "bsPrefix", "className", "size", "props", "decoratedBsPrefix", "displayName", "Object", "assign", "<PERSON><PERSON>"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/Pagination.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport PageItem, { Ellipsis, First, Last, Next, Prev } from './PageItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Pagination = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  size,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'pagination');\n  return /*#__PURE__*/_jsx(\"ul\", {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, size && `${decoratedBsPrefix}-${size}`)\n  });\n});\nPagination.displayName = 'Pagination';\nexport default Object.assign(Pagination, {\n  First,\n  Prev,\n  Ellipsis,\n  Item: PageItem,\n  Next,\n  Last\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,QAAQ,IAAIC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,QAAQ,YAAY;AACxE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG,aAAaV,KAAK,CAACW,UAAU,CAAC,CAAAC,IAAA,EAK9CC,GAAG,KAAK;EAAA,IALuC;IAChDC,QAAQ;IACRC,SAAS;IACTC,IAAI;IACJ,GAAGC;EACL,CAAC,GAAAL,IAAA;EACC,MAAMM,iBAAiB,GAAGjB,kBAAkB,CAACa,QAAQ,EAAE,YAAY,CAAC;EACpE,OAAO,aAAaL,IAAI,CAAC,IAAI,EAAE;IAC7BI,GAAG,EAAEA,GAAG;IACR,GAAGI,KAAK;IACRF,SAAS,EAAEhB,UAAU,CAACgB,SAAS,EAAEG,iBAAiB,EAAEF,IAAI,IAAI,GAAGE,iBAAiB,IAAIF,IAAI,EAAE;EAC5F,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,UAAU,CAACS,WAAW,GAAG,YAAY;AACrC,eAAeC,MAAM,CAACC,MAAM,CAACX,UAAU,EAAE;EACvCN,KAAK;EACLG,IAAI;EACJJ,QAAQ;EACRmB,IAAI,EAAEpB,QAAQ;EACdI,IAAI;EACJD;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}