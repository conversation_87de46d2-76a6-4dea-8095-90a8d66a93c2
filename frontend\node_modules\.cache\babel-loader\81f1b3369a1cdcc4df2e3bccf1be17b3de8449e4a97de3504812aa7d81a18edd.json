{"ast": null, "code": "'use client';\n\nimport { createBreakpoints, createSpacing } from '@mui/system';\nexport default function adaptV4Theme(inputTheme) {\n  if (process.env.NODE_ENV !== 'production') {\n    console.warn(['MUI: adaptV4Theme() is deprecated.', 'Follow the upgrade guide on https://mui.com/r/migration-v4#theme.'].join('\\n'));\n  }\n  const {\n    defaultProps = {},\n    mixins = {},\n    overrides = {},\n    palette = {},\n    props = {},\n    styleOverrides = {},\n    ...other\n  } = inputTheme;\n  const theme = {\n    ...other,\n    components: {}\n  };\n\n  // default props\n  Object.keys(defaultProps).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = defaultProps[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(props).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = props[component];\n    theme.components[component] = componentValue;\n  });\n\n  // CSS overrides\n  Object.keys(styleOverrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = styleOverrides[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(overrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = overrides[component];\n    theme.components[component] = componentValue;\n  });\n\n  // theme.spacing\n  theme.spacing = createSpacing(inputTheme.spacing);\n\n  // theme.mixins.gutters\n  const breakpoints = createBreakpoints(inputTheme.breakpoints || {});\n  const spacing = theme.spacing;\n  theme.mixins = {\n    gutters: (styles = {}) => {\n      return {\n        paddingLeft: spacing(2),\n        paddingRight: spacing(2),\n        ...styles,\n        [breakpoints.up('sm')]: {\n          paddingLeft: spacing(3),\n          paddingRight: spacing(3),\n          ...styles[breakpoints.up('sm')]\n        }\n      };\n    },\n    ...mixins\n  };\n  const {\n    type: typeInput,\n    mode: modeInput,\n    ...paletteRest\n  } = palette;\n  const finalMode = modeInput || typeInput || 'light';\n  theme.palette = {\n    // theme.palette.text.hint\n    text: {\n      hint: finalMode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.38)'\n    },\n    mode: finalMode,\n    type: finalMode,\n    ...paletteRest\n  };\n  return theme;\n}", "map": {"version": 3, "names": ["createBreakpoints", "createSpacing", "adaptV4Theme", "inputTheme", "process", "env", "NODE_ENV", "console", "warn", "join", "defaultProps", "mixins", "overrides", "palette", "props", "styleOverrides", "other", "theme", "components", "Object", "keys", "for<PERSON>ach", "component", "componentValue", "spacing", "breakpoints", "gutters", "styles", "paddingLeft", "paddingRight", "up", "type", "typeInput", "mode", "modeInput", "paletteRest", "finalMode", "text", "hint"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/styles/adaptV4Theme.js"], "sourcesContent": ["'use client';\n\nimport { createBreakpoints, createSpacing } from '@mui/system';\nexport default function adaptV4Theme(inputTheme) {\n  if (process.env.NODE_ENV !== 'production') {\n    console.warn(['MUI: adaptV4Theme() is deprecated.', 'Follow the upgrade guide on https://mui.com/r/migration-v4#theme.'].join('\\n'));\n  }\n  const {\n    defaultProps = {},\n    mixins = {},\n    overrides = {},\n    palette = {},\n    props = {},\n    styleOverrides = {},\n    ...other\n  } = inputTheme;\n  const theme = {\n    ...other,\n    components: {}\n  };\n\n  // default props\n  Object.keys(defaultProps).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = defaultProps[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(props).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.defaultProps = props[component];\n    theme.components[component] = componentValue;\n  });\n\n  // CSS overrides\n  Object.keys(styleOverrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = styleOverrides[component];\n    theme.components[component] = componentValue;\n  });\n  Object.keys(overrides).forEach(component => {\n    const componentValue = theme.components[component] || {};\n    componentValue.styleOverrides = overrides[component];\n    theme.components[component] = componentValue;\n  });\n\n  // theme.spacing\n  theme.spacing = createSpacing(inputTheme.spacing);\n\n  // theme.mixins.gutters\n  const breakpoints = createBreakpoints(inputTheme.breakpoints || {});\n  const spacing = theme.spacing;\n  theme.mixins = {\n    gutters: (styles = {}) => {\n      return {\n        paddingLeft: spacing(2),\n        paddingRight: spacing(2),\n        ...styles,\n        [breakpoints.up('sm')]: {\n          paddingLeft: spacing(3),\n          paddingRight: spacing(3),\n          ...styles[breakpoints.up('sm')]\n        }\n      };\n    },\n    ...mixins\n  };\n  const {\n    type: typeInput,\n    mode: modeInput,\n    ...paletteRest\n  } = palette;\n  const finalMode = modeInput || typeInput || 'light';\n  theme.palette = {\n    // theme.palette.text.hint\n    text: {\n      hint: finalMode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.38)'\n    },\n    mode: finalMode,\n    type: finalMode,\n    ...paletteRest\n  };\n  return theme;\n}"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,iBAAiB,EAAEC,aAAa,QAAQ,aAAa;AAC9D,eAAe,SAASC,YAAYA,CAACC,UAAU,EAAE;EAC/C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCC,OAAO,CAACC,IAAI,CAAC,CAAC,oCAAoC,EAAE,mEAAmE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACtI;EACA,MAAM;IACJC,YAAY,GAAG,CAAC,CAAC;IACjBC,MAAM,GAAG,CAAC,CAAC;IACXC,SAAS,GAAG,CAAC,CAAC;IACdC,OAAO,GAAG,CAAC,CAAC;IACZC,KAAK,GAAG,CAAC,CAAC;IACVC,cAAc,GAAG,CAAC,CAAC;IACnB,GAAGC;EACL,CAAC,GAAGb,UAAU;EACd,MAAMc,KAAK,GAAG;IACZ,GAAGD,KAAK;IACRE,UAAU,EAAE,CAAC;EACf,CAAC;;EAED;EACAC,MAAM,CAACC,IAAI,CAACV,YAAY,CAAC,CAACW,OAAO,CAACC,SAAS,IAAI;IAC7C,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACb,YAAY,GAAGA,YAAY,CAACY,SAAS,CAAC;IACrDL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;EACFJ,MAAM,CAACC,IAAI,CAACN,KAAK,CAAC,CAACO,OAAO,CAACC,SAAS,IAAI;IACtC,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACb,YAAY,GAAGI,KAAK,CAACQ,SAAS,CAAC;IAC9CL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;;EAEF;EACAJ,MAAM,CAACC,IAAI,CAACL,cAAc,CAAC,CAACM,OAAO,CAACC,SAAS,IAAI;IAC/C,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACR,cAAc,GAAGA,cAAc,CAACO,SAAS,CAAC;IACzDL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;EACFJ,MAAM,CAACC,IAAI,CAACR,SAAS,CAAC,CAACS,OAAO,CAACC,SAAS,IAAI;IAC1C,MAAMC,cAAc,GAAGN,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IACxDC,cAAc,CAACR,cAAc,GAAGH,SAAS,CAACU,SAAS,CAAC;IACpDL,KAAK,CAACC,UAAU,CAACI,SAAS,CAAC,GAAGC,cAAc;EAC9C,CAAC,CAAC;;EAEF;EACAN,KAAK,CAACO,OAAO,GAAGvB,aAAa,CAACE,UAAU,CAACqB,OAAO,CAAC;;EAEjD;EACA,MAAMC,WAAW,GAAGzB,iBAAiB,CAACG,UAAU,CAACsB,WAAW,IAAI,CAAC,CAAC,CAAC;EACnE,MAAMD,OAAO,GAAGP,KAAK,CAACO,OAAO;EAC7BP,KAAK,CAACN,MAAM,GAAG;IACbe,OAAO,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;MACxB,OAAO;QACLC,WAAW,EAAEJ,OAAO,CAAC,CAAC,CAAC;QACvBK,YAAY,EAAEL,OAAO,CAAC,CAAC,CAAC;QACxB,GAAGG,MAAM;QACT,CAACF,WAAW,CAACK,EAAE,CAAC,IAAI,CAAC,GAAG;UACtBF,WAAW,EAAEJ,OAAO,CAAC,CAAC,CAAC;UACvBK,YAAY,EAAEL,OAAO,CAAC,CAAC,CAAC;UACxB,GAAGG,MAAM,CAACF,WAAW,CAACK,EAAE,CAAC,IAAI,CAAC;QAChC;MACF,CAAC;IACH,CAAC;IACD,GAAGnB;EACL,CAAC;EACD,MAAM;IACJoB,IAAI,EAAEC,SAAS;IACfC,IAAI,EAAEC,SAAS;IACf,GAAGC;EACL,CAAC,GAAGtB,OAAO;EACX,MAAMuB,SAAS,GAAGF,SAAS,IAAIF,SAAS,IAAI,OAAO;EACnDf,KAAK,CAACJ,OAAO,GAAG;IACd;IACAwB,IAAI,EAAE;MACJC,IAAI,EAAEF,SAAS,KAAK,MAAM,GAAG,0BAA0B,GAAG;IAC5D,CAAC;IACDH,IAAI,EAAEG,SAAS;IACfL,IAAI,EAAEK,SAAS;IACf,GAAGD;EACL,CAAC;EACD,OAAOlB,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}