{"ast": null, "code": "export { default } from \"./FormControlLabel.js\";\nexport { default as formControlLabelClasses } from \"./formControlLabelClasses.js\";\nexport * from \"./formControlLabelClasses.js\";", "map": {"version": 3, "names": ["default", "formControlLabelClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/FormControlLabel/index.js"], "sourcesContent": ["export { default } from \"./FormControlLabel.js\";\nexport { default as formControlLabelClasses } from \"./formControlLabelClasses.js\";\nexport * from \"./formControlLabelClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASA,OAAO,IAAIC,uBAAuB,QAAQ,8BAA8B;AACjF,cAAc,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}