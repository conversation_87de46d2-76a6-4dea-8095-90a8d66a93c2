{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\pages\\\\auth\\\\Register.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst schema = yup.object({\n  name: yup.string().required('Name is required').min(2, 'Name must be at least 2 characters'),\n  email: yup.string().email('Invalid email').required('Email is required'),\n  password: yup.string().required('Password is required').min(8, 'Password must be at least 8 characters'),\n  password_confirmation: yup.string().required('Password confirmation is required').oneOf([yup.ref('password')], 'Passwords must match'),\n  phone: yup.string().optional(),\n  date_of_birth: yup.string().optional()\n});\nconst Register = () => {\n  _s();\n  var _errors$name, _errors$email, _errors$password, _errors$password_conf, _errors$phone, _errors$date_of_birth;\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [success, setSuccess] = useState('');\n  const {\n    register: registerUser,\n    isAuthenticated\n  } = useAuth();\n  const navigate = useNavigate();\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm({\n    resolver: yupResolver(schema)\n  });\n\n  // Redirect if already authenticated\n  React.useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/', {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, navigate]);\n  const onSubmit = async data => {\n    try {\n      setError('');\n      setSuccess('');\n      setLoading(true);\n      await registerUser(data);\n      setSuccess('Registration successful! Please check your email for verification.');\n\n      // Redirect after a short delay\n      setTimeout(() => {\n        navigate('/email-verification');\n      }, 2000);\n    } catch (err) {\n      var _err$response, _err$response$data, _err$response2, _err$response2$data;\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Registration failed. Please try again.';\n      const validationErrors = (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.errors;\n      if (validationErrors) {\n        const errorMessages = Object.values(validationErrors).flat().join(', ');\n        setError(errorMessages);\n      } else {\n        setError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: 8,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(Card.Title, {\n              className: \"text-center mb-4\",\n              children: \"Register\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 25\n            }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"success\",\n              children: success\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 27\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              onSubmit: handleSubmit(onSubmit),\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Full Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 86,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      ...register('name'),\n                      isInvalid: !!errors.name,\n                      placeholder: \"Enter your full name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 87,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$name = errors.name) === null || _errors$name === void 0 ? void 0 : _errors$name.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 93,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 101,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"email\",\n                      ...register('email'),\n                      isInvalid: !!errors.email,\n                      placeholder: \"Enter your email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 102,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$email = errors.email) === null || _errors$email === void 0 ? void 0 : _errors$email.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 108,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 118,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"password\",\n                      ...register('password'),\n                      isInvalid: !!errors.password,\n                      placeholder: \"Enter your password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$password = errors.password) === null || _errors$password === void 0 ? void 0 : _errors$password.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 125,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Confirm Password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"password\",\n                      ...register('password_confirmation'),\n                      isInvalid: !!errors.password_confirmation,\n                      placeholder: \"Confirm your password\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$password_conf = errors.password_confirmation) === null || _errors$password_conf === void 0 ? void 0 : _errors$password_conf.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Phone (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 150,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"tel\",\n                      ...register('phone'),\n                      isInvalid: !!errors.phone,\n                      placeholder: \"Enter your phone number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 151,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$phone = errors.phone) === null || _errors$phone === void 0 ? void 0 : _errors$phone.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Date of Birth (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"date\",\n                      ...register('date_of_birth'),\n                      isInvalid: !!errors.date_of_birth\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                      type: \"invalid\",\n                      children: (_errors$date_of_birth = errors.date_of_birth) === null || _errors$date_of_birth === void 0 ? void 0 : _errors$date_of_birth.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                type: \"submit\",\n                className: \"w-100 mb-3\",\n                disabled: loading,\n                children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 23\n                  }, this), \"Creating account...\"]\n                }, void 0, true) : 'Register'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Already have an account? \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"text-decoration-none\",\n                children: \"Login here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"RqsASo1fVHuXyvtNLIRFQnnmzxg=\", false, function () {\n  return [useAuth, useNavigate, useForm];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Link", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "useForm", "yupResolver", "yup", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "schema", "object", "name", "string", "required", "min", "email", "password", "password_confirmation", "oneOf", "ref", "phone", "optional", "date_of_birth", "Register", "_s", "_errors$name", "_errors$email", "_errors$password", "_errors$password_conf", "_errors$phone", "_errors$date_of_birth", "error", "setError", "loading", "setLoading", "success", "setSuccess", "register", "registerUser", "isAuthenticated", "navigate", "handleSubmit", "formState", "errors", "resolver", "useEffect", "replace", "onSubmit", "data", "setTimeout", "err", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "errorMessage", "response", "message", "validationErrors", "errorMessages", "Object", "values", "flat", "join", "children", "className", "md", "lg", "Body", "Title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "Group", "Label", "Control", "type", "isInvalid", "placeholder", "<PERSON><PERSON><PERSON>", "disabled", "as", "animation", "size", "role", "to", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/pages/auth/Register.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavi<PERSON>, <PERSON> } from 'react-router-dom';\nimport { Container, Row, Col, Card, Form, <PERSON><PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { RegisterData } from '../../services/authService';\n\nconst schema = yup.object({\n  name: yup.string().required('Name is required').min(2, 'Name must be at least 2 characters'),\n  email: yup.string().email('Invalid email').required('Email is required'),\n  password: yup.string().required('Password is required').min(8, 'Password must be at least 8 characters'),\n  password_confirmation: yup.string()\n    .required('Password confirmation is required')\n    .oneOf([yup.ref('password')], 'Passwords must match'),\n  phone: yup.string().optional(),\n  date_of_birth: yup.string().optional(),\n});\n\nconst Register: React.FC = () => {\n  const [error, setError] = useState<string>('');\n  const [loading, setLoading] = useState(false);\n  const [success, setSuccess] = useState<string>('');\n  const { register: registerUser, isAuthenticated } = useAuth();\n  const navigate = useNavigate();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<RegisterData>({\n    resolver: yupResolver(schema),\n  });\n\n  // Redirect if already authenticated\n  React.useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/', { replace: true });\n    }\n  }, [isAuthenticated, navigate]);\n\n  const onSubmit = async (data: RegisterData) => {\n    try {\n      setError('');\n      setSuccess('');\n      setLoading(true);\n\n      await registerUser(data);\n      setSuccess('Registration successful! Please check your email for verification.');\n\n      // Redirect after a short delay\n      setTimeout(() => {\n        navigate('/email-verification');\n      }, 2000);\n    } catch (err: any) {\n      const errorMessage = err.response?.data?.message || 'Registration failed. Please try again.';\n      const validationErrors = err.response?.data?.errors;\n\n      if (validationErrors) {\n        const errorMessages = Object.values(validationErrors).flat().join(', ');\n        setError(errorMessages);\n      } else {\n        setError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container>\n      <Row className=\"justify-content-center\">\n        <Col md={8} lg={6}>\n          <Card>\n            <Card.Body>\n              <Card.Title className=\"text-center mb-4\">Register</Card.Title>\n\n              {error && <Alert variant=\"danger\">{error}</Alert>}\n              {success && <Alert variant=\"success\">{success}</Alert>}\n\n              <Form onSubmit={handleSubmit(onSubmit)}>\n                <Row>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Full Name</Form.Label>\n                      <Form.Control\n                        type=\"text\"\n                        {...register('name')}\n                        isInvalid={!!errors.name}\n                        placeholder=\"Enter your full name\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.name?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Email</Form.Label>\n                      <Form.Control\n                        type=\"email\"\n                        {...register('email')}\n                        isInvalid={!!errors.email}\n                        placeholder=\"Enter your email\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.email?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n                </Row>\n\n                <Row>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Password</Form.Label>\n                      <Form.Control\n                        type=\"password\"\n                        {...register('password')}\n                        isInvalid={!!errors.password}\n                        placeholder=\"Enter your password\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.password?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Confirm Password</Form.Label>\n                      <Form.Control\n                        type=\"password\"\n                        {...register('password_confirmation')}\n                        isInvalid={!!errors.password_confirmation}\n                        placeholder=\"Confirm your password\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.password_confirmation?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n                </Row>\n\n                <Row>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Phone (Optional)</Form.Label>\n                      <Form.Control\n                        type=\"tel\"\n                        {...register('phone')}\n                        isInvalid={!!errors.phone}\n                        placeholder=\"Enter your phone number\"\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.phone?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Date of Birth (Optional)</Form.Label>\n                      <Form.Control\n                        type=\"date\"\n                        {...register('date_of_birth')}\n                        isInvalid={!!errors.date_of_birth}\n                      />\n                      <Form.Control.Feedback type=\"invalid\">\n                        {errors.date_of_birth?.message}\n                      </Form.Control.Feedback>\n                    </Form.Group>\n                  </Col>\n                </Row>\n\n                <Button\n                  variant=\"primary\"\n                  type=\"submit\"\n                  className=\"w-100 mb-3\"\n                  disabled={loading}\n                >\n                  {loading ? (\n                    <>\n                      <Spinner\n                        as=\"span\"\n                        animation=\"border\"\n                        size=\"sm\"\n                        role=\"status\"\n                        aria-hidden=\"true\"\n                        className=\"me-2\"\n                      />\n                      Creating account...\n                    </>\n                  ) : (\n                    'Register'\n                  )}\n                </Button>\n              </Form>\n\n              <div className=\"text-center\">\n                <span>Already have an account? </span>\n                <Link to=\"/login\" className=\"text-decoration-none\">\n                  Login here\n                </Link>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACzF,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGrD,MAAMC,MAAM,GAAGN,GAAG,CAACO,MAAM,CAAC;EACxBC,IAAI,EAAER,GAAG,CAACS,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,kBAAkB,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,oCAAoC,CAAC;EAC5FC,KAAK,EAAEZ,GAAG,CAACS,MAAM,CAAC,CAAC,CAACG,KAAK,CAAC,eAAe,CAAC,CAACF,QAAQ,CAAC,mBAAmB,CAAC;EACxEG,QAAQ,EAAEb,GAAG,CAACS,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,sBAAsB,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;EACxGG,qBAAqB,EAAEd,GAAG,CAACS,MAAM,CAAC,CAAC,CAChCC,QAAQ,CAAC,mCAAmC,CAAC,CAC7CK,KAAK,CAAC,CAACf,GAAG,CAACgB,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,sBAAsB,CAAC;EACvDC,KAAK,EAAEjB,GAAG,CAACS,MAAM,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC;EAC9BC,aAAa,EAAEnB,GAAG,CAACS,MAAM,CAAC,CAAC,CAACS,QAAQ,CAAC;AACvC,CAAC,CAAC;AAEF,MAAME,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,qBAAA;EAC/B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAS,EAAE,CAAC;EAClD,MAAM;IAAE+C,QAAQ,EAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGnC,OAAO,CAAC,CAAC;EAC7D,MAAMoC,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAE9B,MAAM;IACJ8C,QAAQ;IACRI,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO;EACtB,CAAC,GAAG1C,OAAO,CAAe;IACxB2C,QAAQ,EAAE1C,WAAW,CAACO,MAAM;EAC9B,CAAC,CAAC;;EAEF;EACApB,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpB,IAAIN,eAAe,EAAE;MACnBC,QAAQ,CAAC,GAAG,EAAE;QAAEM,OAAO,EAAE;MAAK,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,CAACP,eAAe,EAAEC,QAAQ,CAAC,CAAC;EAE/B,MAAMO,QAAQ,GAAG,MAAOC,IAAkB,IAAK;IAC7C,IAAI;MACFhB,QAAQ,CAAC,EAAE,CAAC;MACZI,UAAU,CAAC,EAAE,CAAC;MACdF,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMI,YAAY,CAACU,IAAI,CAAC;MACxBZ,UAAU,CAAC,oEAAoE,CAAC;;MAEhF;MACAa,UAAU,CAAC,MAAM;QACfT,QAAQ,CAAC,qBAAqB,CAAC;MACjC,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOU,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACjB,MAAMC,YAAY,GAAG,EAAAJ,aAAA,GAAAD,GAAG,CAACM,QAAQ,cAAAL,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoBK,OAAO,KAAI,wCAAwC;MAC5F,MAAMC,gBAAgB,IAAAL,cAAA,GAAGH,GAAG,CAACM,QAAQ,cAAAH,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcL,IAAI,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoBX,MAAM;MAEnD,IAAIe,gBAAgB,EAAE;QACpB,MAAMC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACH,gBAAgB,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QACvE/B,QAAQ,CAAC2B,aAAa,CAAC;MACzB,CAAC,MAAM;QACL3B,QAAQ,CAACuB,YAAY,CAAC;MACxB;IACF,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACE5B,OAAA,CAACb,SAAS;IAAAuE,QAAA,eACR1D,OAAA,CAACZ,GAAG;MAACuE,SAAS,EAAC,wBAAwB;MAAAD,QAAA,eACrC1D,OAAA,CAACX,GAAG;QAACuE,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAH,QAAA,eAChB1D,OAAA,CAACV,IAAI;UAAAoE,QAAA,eACH1D,OAAA,CAACV,IAAI,CAACwE,IAAI;YAAAJ,QAAA,gBACR1D,OAAA,CAACV,IAAI,CAACyE,KAAK;cAACJ,SAAS,EAAC,kBAAkB;cAAAD,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAE7D1C,KAAK,iBAAIzB,OAAA,CAACP,KAAK;cAAC2E,OAAO,EAAC,QAAQ;cAAAV,QAAA,EAAEjC;YAAK;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAChDtC,OAAO,iBAAI7B,OAAA,CAACP,KAAK;cAAC2E,OAAO,EAAC,SAAS;cAAAV,QAAA,EAAE7B;YAAO;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEtDnE,OAAA,CAACT,IAAI;cAACkD,QAAQ,EAAEN,YAAY,CAACM,QAAQ,CAAE;cAAAiB,QAAA,gBACrC1D,OAAA,CAACZ,GAAG;gBAAAsE,QAAA,gBACF1D,OAAA,CAACX,GAAG;kBAACuE,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT1D,OAAA,CAACT,IAAI,CAAC8E,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B1D,OAAA,CAACT,IAAI,CAAC+E,KAAK;sBAAAZ,QAAA,EAAC;oBAAS;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAClCnE,OAAA,CAACT,IAAI,CAACgF,OAAO;sBACXC,IAAI,EAAC,MAAM;sBAAA,GACPzC,QAAQ,CAAC,MAAM,CAAC;sBACpB0C,SAAS,EAAE,CAAC,CAACpC,MAAM,CAAChC,IAAK;sBACzBqE,WAAW,EAAC;oBAAsB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACFnE,OAAA,CAACT,IAAI,CAACgF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAAvC,YAAA,GAClCkB,MAAM,CAAChC,IAAI,cAAAc,YAAA,uBAAXA,YAAA,CAAagC;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENnE,OAAA,CAACX,GAAG;kBAACuE,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT1D,OAAA,CAACT,IAAI,CAAC8E,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B1D,OAAA,CAACT,IAAI,CAAC+E,KAAK;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC9BnE,OAAA,CAACT,IAAI,CAACgF,OAAO;sBACXC,IAAI,EAAC,OAAO;sBAAA,GACRzC,QAAQ,CAAC,OAAO,CAAC;sBACrB0C,SAAS,EAAE,CAAC,CAACpC,MAAM,CAAC5B,KAAM;sBAC1BiE,WAAW,EAAC;oBAAkB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACFnE,OAAA,CAACT,IAAI,CAACgF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAAtC,aAAA,GAClCiB,MAAM,CAAC5B,KAAK,cAAAW,aAAA,uBAAZA,aAAA,CAAc+B;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnE,OAAA,CAACZ,GAAG;gBAAAsE,QAAA,gBACF1D,OAAA,CAACX,GAAG;kBAACuE,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT1D,OAAA,CAACT,IAAI,CAAC8E,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B1D,OAAA,CAACT,IAAI,CAAC+E,KAAK;sBAAAZ,QAAA,EAAC;oBAAQ;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjCnE,OAAA,CAACT,IAAI,CAACgF,OAAO;sBACXC,IAAI,EAAC,UAAU;sBAAA,GACXzC,QAAQ,CAAC,UAAU,CAAC;sBACxB0C,SAAS,EAAE,CAAC,CAACpC,MAAM,CAAC3B,QAAS;sBAC7BgE,WAAW,EAAC;oBAAqB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC,eACFnE,OAAA,CAACT,IAAI,CAACgF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAArC,gBAAA,GAClCgB,MAAM,CAAC3B,QAAQ,cAAAW,gBAAA,uBAAfA,gBAAA,CAAiB8B;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENnE,OAAA,CAACX,GAAG;kBAACuE,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT1D,OAAA,CAACT,IAAI,CAAC8E,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B1D,OAAA,CAACT,IAAI,CAAC+E,KAAK;sBAAAZ,QAAA,EAAC;oBAAgB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzCnE,OAAA,CAACT,IAAI,CAACgF,OAAO;sBACXC,IAAI,EAAC,UAAU;sBAAA,GACXzC,QAAQ,CAAC,uBAAuB,CAAC;sBACrC0C,SAAS,EAAE,CAAC,CAACpC,MAAM,CAAC1B,qBAAsB;sBAC1C+D,WAAW,EAAC;oBAAuB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eACFnE,OAAA,CAACT,IAAI,CAACgF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAApC,qBAAA,GAClCe,MAAM,CAAC1B,qBAAqB,cAAAW,qBAAA,uBAA5BA,qBAAA,CAA8B6B;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnE,OAAA,CAACZ,GAAG;gBAAAsE,QAAA,gBACF1D,OAAA,CAACX,GAAG;kBAACuE,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT1D,OAAA,CAACT,IAAI,CAAC8E,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B1D,OAAA,CAACT,IAAI,CAAC+E,KAAK;sBAAAZ,QAAA,EAAC;oBAAgB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzCnE,OAAA,CAACT,IAAI,CAACgF,OAAO;sBACXC,IAAI,EAAC,KAAK;sBAAA,GACNzC,QAAQ,CAAC,OAAO,CAAC;sBACrB0C,SAAS,EAAE,CAAC,CAACpC,MAAM,CAACvB,KAAM;sBAC1B4D,WAAW,EAAC;oBAAyB;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC,eACFnE,OAAA,CAACT,IAAI,CAACgF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAAnC,aAAA,GAClCc,MAAM,CAACvB,KAAK,cAAAS,aAAA,uBAAZA,aAAA,CAAc4B;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENnE,OAAA,CAACX,GAAG;kBAACuE,EAAE,EAAE,CAAE;kBAAAF,QAAA,eACT1D,OAAA,CAACT,IAAI,CAAC8E,KAAK;oBAACV,SAAS,EAAC,MAAM;oBAAAD,QAAA,gBAC1B1D,OAAA,CAACT,IAAI,CAAC+E,KAAK;sBAAAZ,QAAA,EAAC;oBAAwB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjDnE,OAAA,CAACT,IAAI,CAACgF,OAAO;sBACXC,IAAI,EAAC,MAAM;sBAAA,GACPzC,QAAQ,CAAC,eAAe,CAAC;sBAC7B0C,SAAS,EAAE,CAAC,CAACpC,MAAM,CAACrB;oBAAc;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACFnE,OAAA,CAACT,IAAI,CAACgF,OAAO,CAACI,QAAQ;sBAACH,IAAI,EAAC,SAAS;sBAAAd,QAAA,GAAAlC,qBAAA,GAClCa,MAAM,CAACrB,aAAa,cAAAQ,qBAAA,uBAApBA,qBAAA,CAAsB2B;oBAAO;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnE,OAAA,CAACR,MAAM;gBACL4E,OAAO,EAAC,SAAS;gBACjBI,IAAI,EAAC,QAAQ;gBACbb,SAAS,EAAC,YAAY;gBACtBiB,QAAQ,EAAEjD,OAAQ;gBAAA+B,QAAA,EAEjB/B,OAAO,gBACN3B,OAAA,CAAAE,SAAA;kBAAAwD,QAAA,gBACE1D,OAAA,CAACN,OAAO;oBACNmF,EAAE,EAAC,MAAM;oBACTC,SAAS,EAAC,QAAQ;oBAClBC,IAAI,EAAC,IAAI;oBACTC,IAAI,EAAC,QAAQ;oBACb,eAAY,MAAM;oBAClBrB,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,uBAEJ;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEPnE,OAAA;cAAK2D,SAAS,EAAC,aAAa;cAAAD,QAAA,gBAC1B1D,OAAA;gBAAA0D,QAAA,EAAM;cAAyB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCnE,OAAA,CAACd,IAAI;gBAAC+F,EAAE,EAAC,QAAQ;gBAACtB,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EAAC;cAEnD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACjD,EAAA,CAjMID,QAAkB;EAAA,QAI8BnB,OAAO,EAC1Cb,WAAW,EAMxBU,OAAO;AAAA;AAAAuF,EAAA,GAXPjE,QAAkB;AAmMxB,eAAeA,QAAQ;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}