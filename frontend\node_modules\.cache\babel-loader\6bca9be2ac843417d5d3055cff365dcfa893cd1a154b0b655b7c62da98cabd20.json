{"ast": null, "code": "'use client';\n\n// @inheritedComponent IconButton\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, darken, lighten } from '@mui/system/colorManipulator';\nimport capitalize from \"../utils/capitalize.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport SwitchBase from \"../internal/SwitchBase.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport switchClasses, { getSwitchUtilityClass } from \"./switchClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return {\n    ...classes,\n    // forward the disabled and checked classes to the SwitchBase\n    ...composedClasses\n  };\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  },\n  variants: [{\n    props: {\n      edge: 'start'\n    },\n    style: {\n      marginLeft: -8\n    }\n  }, {\n    props: {\n      edge: 'end'\n    },\n    style: {\n      marginRight: -8\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      width: 40,\n      height: 24,\n      padding: 7,\n      [`& .${switchClasses.thumb}`]: {\n        width: 16,\n        height: 16\n      },\n      [`& .${switchClasses.switchBase}`]: {\n        padding: 4,\n        [`&.${switchClasses.checked}`]: {\n          transform: 'translateX(16px)'\n        }\n      }\n    }\n  }]\n});\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(memoTheme(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    zIndex: 1,\n    // Render above the focus ripple.\n    color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n    transition: theme.transitions.create(['left', 'transform'], {\n      duration: theme.transitions.duration.shortest\n    }),\n    [`&.${switchClasses.checked}`]: {\n      transform: 'translateX(20px)'\n    },\n    [`&.${switchClasses.disabled}`]: {\n      color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n    },\n    [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n      opacity: 0.5\n    },\n    [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n      opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n    },\n    [`& .${switchClasses.input}`]: {\n      left: '-100%',\n      width: '300%'\n    }\n  };\n}), memoTheme(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    '&:hover': {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: 'transparent'\n      }\n    },\n    variants: [...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['light'])) // check all the used fields in the style below\n    .map(_ref3 => {\n      let [color] = _ref3;\n      return {\n        props: {\n          color\n        },\n        style: {\n          [`&.${switchClasses.checked}`]: {\n            color: (theme.vars || theme).palette[color].main,\n            '&:hover': {\n              backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity),\n              '@media (hover: none)': {\n                backgroundColor: 'transparent'\n              }\n            },\n            [`&.${switchClasses.disabled}`]: {\n              color: theme.vars ? theme.vars.palette.Switch[`${color}DisabledColor`] : `${theme.palette.mode === 'light' ? lighten(theme.palette[color].main, 0.62) : darken(theme.palette[color].main, 0.55)}`\n            }\n          },\n          [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n            backgroundColor: (theme.vars || theme).palette[color].main\n          }\n        }\n      };\n    })]\n  };\n}));\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track'\n})(memoTheme(_ref4 => {\n  let {\n    theme\n  } = _ref4;\n  return {\n    height: '100%',\n    width: '100%',\n    borderRadius: 14 / 2,\n    zIndex: -1,\n    transition: theme.transitions.create(['opacity', 'background-color'], {\n      duration: theme.transitions.duration.shortest\n    }),\n    backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n    opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n  };\n}));\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb'\n})(memoTheme(_ref5 => {\n  let {\n    theme\n  } = _ref5;\n  return {\n    boxShadow: (theme.vars || theme).shadows[1],\n    backgroundColor: 'currentColor',\n    width: 20,\n    height: 20,\n    borderRadius: '50%'\n  };\n}));\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n    className,\n    color = 'primary',\n    edge = false,\n    size = 'medium',\n    sx,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    color,\n    edge,\n    size\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: SwitchRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      sx\n    }\n  });\n  const [ThumbSlot, thumbSlotProps] = useSlot('thumb', {\n    className: classes.thumb,\n    elementType: SwitchThumb,\n    externalForwardedProps,\n    ownerState\n  });\n  const icon = /*#__PURE__*/_jsx(ThumbSlot, {\n    ...thumbSlotProps\n  });\n  const [TrackSlot, trackSlotProps] = useSlot('track', {\n    className: classes.track,\n    elementType: SwitchTrack,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, {\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState,\n      ...other,\n      classes: {\n        ...classes,\n        root: classes.switchBase\n      },\n      slots: {\n        ...(slots.switchBase && {\n          root: slots.switchBase\n        }),\n        ...(slots.input && {\n          input: slots.input\n        })\n      },\n      slotProps: {\n        ...(slotProps.switchBase && {\n          root: typeof slotProps.switchBase === 'function' ? slotProps.switchBase(ownerState) : slotProps.switchBase\n        }),\n        ...(slotProps.input && {\n          input: typeof slotProps.input === 'function' ? slotProps.input(ownerState) : slotProps.input\n        })\n      }\n    }), /*#__PURE__*/_jsx(TrackSlot, {\n      ...trackSlotProps\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Elements/input#attributes) applied to the `input` element.\n   * @deprecated Use `slotProps.input` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   * @deprecated Use `slotProps.input.ref` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    switchBase: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    input: PropTypes.elementType,\n    root: PropTypes.elementType,\n    switchBase: PropTypes.elementType,\n    thumb: PropTypes.elementType,\n    track: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "refType", "composeClasses", "alpha", "darken", "lighten", "capitalize", "createSimplePaletteValueFilter", "SwitchBase", "styled", "memoTheme", "useDefaultProps", "switchClasses", "getSwitchUtilityClass", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "edge", "size", "color", "checked", "disabled", "slots", "root", "switchBase", "thumb", "track", "input", "composedClasses", "SwitchRoot", "name", "slot", "overridesResolver", "props", "styles", "display", "width", "height", "overflow", "padding", "boxSizing", "position", "flexShrink", "zIndex", "verticalAlign", "colorAdjust", "variants", "style", "marginLeft", "marginRight", "transform", "SwitchSwitchBase", "_ref", "theme", "top", "left", "vars", "palette", "Switch", "defaultColor", "mode", "common", "white", "grey", "transition", "transitions", "create", "duration", "shortest", "defaultDisabledColor", "opacity", "switchTrackDisabled", "_ref2", "backgroundColor", "action", "activeChannel", "hoverOpacity", "active", "Object", "entries", "filter", "map", "_ref3", "main", "mainChannel", "SwitchTrack", "_ref4", "borderRadius", "onBackground", "black", "switchTrack", "SwitchThumb", "_ref5", "boxShadow", "shadows", "forwardRef", "inProps", "ref", "className", "sx", "slotProps", "other", "externalForwardedProps", "RootSlot", "rootSlotProps", "elementType", "additionalProps", "ThumbSlot", "thumbSlotProps", "icon", "TrackSlot", "trackSlotProps", "children", "type", "checkedIcon", "process", "env", "NODE_ENV", "propTypes", "bool", "node", "object", "string", "oneOfType", "oneOf", "defaultChecked", "disable<PERSON><PERSON><PERSON>", "id", "inputProps", "inputRef", "onChange", "func", "required", "shape", "arrayOf", "value", "any"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Switch/Switch.js"], "sourcesContent": ["'use client';\n\n// @inheritedComponent IconButton\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, darken, lighten } from '@mui/system/colorManipulator';\nimport capitalize from \"../utils/capitalize.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport SwitchBase from \"../internal/SwitchBase.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport switchClasses, { getSwitchUtilityClass } from \"./switchClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return {\n    ...classes,\n    // forward the disabled and checked classes to the SwitchBase\n    ...composedClasses\n  };\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  },\n  variants: [{\n    props: {\n      edge: 'start'\n    },\n    style: {\n      marginLeft: -8\n    }\n  }, {\n    props: {\n      edge: 'end'\n    },\n    style: {\n      marginRight: -8\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      width: 40,\n      height: 24,\n      padding: 7,\n      [`& .${switchClasses.thumb}`]: {\n        width: 16,\n        height: 16\n      },\n      [`& .${switchClasses.switchBase}`]: {\n        padding: 4,\n        [`&.${switchClasses.checked}`]: {\n          transform: 'translateX(16px)'\n        }\n      }\n    }\n  }]\n});\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  zIndex: 1,\n  // Render above the focus ripple.\n  color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n  transition: theme.transitions.create(['left', 'transform'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  [`&.${switchClasses.checked}`]: {\n    transform: 'translateX(20px)'\n  },\n  [`&.${switchClasses.disabled}`]: {\n    color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    opacity: 0.5\n  },\n  [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n    opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n  },\n  [`& .${switchClasses.input}`]: {\n    left: '-100%',\n    width: '300%'\n  }\n})), memoTheme(({\n  theme\n}) => ({\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  },\n  variants: [...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['light'])) // check all the used fields in the style below\n  .map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      [`&.${switchClasses.checked}`]: {\n        color: (theme.vars || theme).palette[color].main,\n        '&:hover': {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity),\n          '@media (hover: none)': {\n            backgroundColor: 'transparent'\n          }\n        },\n        [`&.${switchClasses.disabled}`]: {\n          color: theme.vars ? theme.vars.palette.Switch[`${color}DisabledColor`] : `${theme.palette.mode === 'light' ? lighten(theme.palette[color].main, 0.62) : darken(theme.palette[color].main, 0.55)}`\n        }\n      },\n      [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n        backgroundColor: (theme.vars || theme).palette[color].main\n      }\n    }\n  }))]\n})));\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track'\n})(memoTheme(({\n  theme\n}) => ({\n  height: '100%',\n  width: '100%',\n  borderRadius: 14 / 2,\n  zIndex: -1,\n  transition: theme.transitions.create(['opacity', 'background-color'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n  opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n})));\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb'\n})(memoTheme(({\n  theme\n}) => ({\n  boxShadow: (theme.vars || theme).shadows[1],\n  backgroundColor: 'currentColor',\n  width: 20,\n  height: 20,\n  borderRadius: '50%'\n})));\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n    className,\n    color = 'primary',\n    edge = false,\n    size = 'medium',\n    sx,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    color,\n    edge,\n    size\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: SwitchRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      sx\n    }\n  });\n  const [ThumbSlot, thumbSlotProps] = useSlot('thumb', {\n    className: classes.thumb,\n    elementType: SwitchThumb,\n    externalForwardedProps,\n    ownerState\n  });\n  const icon = /*#__PURE__*/_jsx(ThumbSlot, {\n    ...thumbSlotProps\n  });\n  const [TrackSlot, trackSlotProps] = useSlot('track', {\n    className: classes.track,\n    elementType: SwitchTrack,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, {\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState,\n      ...other,\n      classes: {\n        ...classes,\n        root: classes.switchBase\n      },\n      slots: {\n        ...(slots.switchBase && {\n          root: slots.switchBase\n        }),\n        ...(slots.input && {\n          input: slots.input\n        })\n      },\n      slotProps: {\n        ...(slotProps.switchBase && {\n          root: typeof slotProps.switchBase === 'function' ? slotProps.switchBase(ownerState) : slotProps.switchBase\n        }),\n        ...(slotProps.input && {\n          input: typeof slotProps.input === 'function' ? slotProps.input(ownerState) : slotProps.input\n        })\n      }\n    }), /*#__PURE__*/_jsx(TrackSlot, {\n      ...trackSlotProps\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Elements/input#attributes) applied to the `input` element.\n   * @deprecated Use `slotProps.input` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   * @deprecated Use `slotProps.input.ref` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    switchBase: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    input: PropTypes.elementType,\n    root: PropTypes.elementType,\n    switchBase: PropTypes.elementType,\n    thumb: PropTypes.elementType,\n    track: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,EAAEC,MAAM,EAAEC,OAAO,QAAQ,8BAA8B;AACrE,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,8BAA8B,MAAM,4CAA4C;AACvF,OAAOC,UAAU,MAAM,2BAA2B;AAClD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,aAAa,IAAIC,qBAAqB,QAAQ,oBAAoB;AACzE,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC,IAAI;IACJC,KAAK;IACLC,OAAO;IACPC;EACF,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEN,IAAI,IAAI,OAAOhB,UAAU,CAACgB,IAAI,CAAC,EAAE,EAAE,OAAOhB,UAAU,CAACiB,IAAI,CAAC,EAAE,CAAC;IAC5EM,UAAU,EAAE,CAAC,YAAY,EAAE,QAAQvB,UAAU,CAACkB,KAAK,CAAC,EAAE,EAAEC,OAAO,IAAI,SAAS,EAAEC,QAAQ,IAAI,UAAU,CAAC;IACrGI,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,MAAMC,eAAe,GAAG/B,cAAc,CAACyB,KAAK,EAAEd,qBAAqB,EAAEQ,OAAO,CAAC;EAC7E,OAAO;IACL,GAAGA,OAAO;IACV;IACA,GAAGY;EACL,CAAC;AACH,CAAC;AACD,MAAMC,UAAU,GAAGzB,MAAM,CAAC,MAAM,EAAE;EAChC0B,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACX,IAAI,EAAER,UAAU,CAACE,IAAI,IAAIiB,MAAM,CAAC,OAAOjC,UAAU,CAACc,UAAU,CAACE,IAAI,CAAC,EAAE,CAAC,EAAEiB,MAAM,CAAC,OAAOjC,UAAU,CAACc,UAAU,CAACG,IAAI,CAAC,EAAE,CAAC,CAAC;EACrI;AACF,CAAC,CAAC,CAAC;EACDiB,OAAO,EAAE,aAAa;EACtBC,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAClBC,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EACnBC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,YAAY;EACvBC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,CAAC;EACT;EACAC,aAAa,EAAE,QAAQ;EACvB;EACA,cAAc,EAAE;IACdC,WAAW,EAAE;EACf,CAAC;EACDC,QAAQ,EAAE,CAAC;IACTb,KAAK,EAAE;MACLhB,IAAI,EAAE;IACR,CAAC;IACD8B,KAAK,EAAE;MACLC,UAAU,EAAE,CAAC;IACf;EACF,CAAC,EAAE;IACDf,KAAK,EAAE;MACLhB,IAAI,EAAE;IACR,CAAC;IACD8B,KAAK,EAAE;MACLE,WAAW,EAAE,CAAC;IAChB;EACF,CAAC,EAAE;IACDhB,KAAK,EAAE;MACLf,IAAI,EAAE;IACR,CAAC;IACD6B,KAAK,EAAE;MACLX,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVE,OAAO,EAAE,CAAC;MACV,CAAC,MAAMhC,aAAa,CAACkB,KAAK,EAAE,GAAG;QAC7BW,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE;MACV,CAAC;MACD,CAAC,MAAM9B,aAAa,CAACiB,UAAU,EAAE,GAAG;QAClCe,OAAO,EAAE,CAAC;QACV,CAAC,KAAKhC,aAAa,CAACa,OAAO,EAAE,GAAG;UAC9B8B,SAAS,EAAE;QACb;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,gBAAgB,GAAG/C,MAAM,CAACD,UAAU,EAAE;EAC1C2B,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,YAAY;EAClBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACV,UAAU,EAAE;MACzB,CAAC,MAAMjB,aAAa,CAACoB,KAAK,EAAE,GAAGO,MAAM,CAACP;IACxC,CAAC,EAAEZ,UAAU,CAACI,KAAK,KAAK,SAAS,IAAIe,MAAM,CAAC,QAAQjC,UAAU,CAACc,UAAU,CAACI,KAAK,CAAC,EAAE,CAAC,CAAC;EACtF;AACF,CAAC,CAAC,CAACd,SAAS,CAAC+C,IAAA;EAAA,IAAC;IACZC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLX,QAAQ,EAAE,UAAU;IACpBa,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPZ,MAAM,EAAE,CAAC;IACT;IACAxB,KAAK,EAAEkC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAACC,YAAY,GAAG,GAAGN,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACI,MAAM,CAACC,KAAK,GAAGT,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE;IACvJC,UAAU,EAAEX,KAAK,CAACY,WAAW,CAACC,MAAM,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;MAC1DC,QAAQ,EAAEd,KAAK,CAACY,WAAW,CAACE,QAAQ,CAACC;IACvC,CAAC,CAAC;IACF,CAAC,KAAK7D,aAAa,CAACa,OAAO,EAAE,GAAG;MAC9B8B,SAAS,EAAE;IACb,CAAC;IACD,CAAC,KAAK3C,aAAa,CAACc,QAAQ,EAAE,GAAG;MAC/BF,KAAK,EAAEkC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAACW,oBAAoB,GAAG,GAAGhB,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,GAAGV,KAAK,CAACI,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC;IAC5J,CAAC;IACD,CAAC,KAAKxD,aAAa,CAACa,OAAO,OAAOb,aAAa,CAACmB,KAAK,EAAE,GAAG;MACxD4C,OAAO,EAAE;IACX,CAAC;IACD,CAAC,KAAK/D,aAAa,CAACc,QAAQ,OAAOd,aAAa,CAACmB,KAAK,EAAE,GAAG;MACzD4C,OAAO,EAAEjB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACc,OAAO,CAACC,mBAAmB,GAAG,GAAGlB,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,GAAG;IAC/G,CAAC;IACD,CAAC,MAAMrD,aAAa,CAACoB,KAAK,EAAE,GAAG;MAC7B4B,IAAI,EAAE,OAAO;MACbnB,KAAK,EAAE;IACT;EACF,CAAC;AAAA,CAAC,CAAC,EAAE/B,SAAS,CAACmE,KAAA;EAAA,IAAC;IACdnB;EACF,CAAC,GAAAmB,KAAA;EAAA,OAAM;IACL,SAAS,EAAE;MACTC,eAAe,EAAEpB,KAAK,CAACG,IAAI,GAAG,QAAQH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACiB,MAAM,CAACC,aAAa,MAAMtB,KAAK,CAACG,IAAI,CAACC,OAAO,CAACiB,MAAM,CAACE,YAAY,GAAG,GAAG9E,KAAK,CAACuD,KAAK,CAACI,OAAO,CAACiB,MAAM,CAACG,MAAM,EAAExB,KAAK,CAACI,OAAO,CAACiB,MAAM,CAACE,YAAY,CAAC;MACpM;MACA,sBAAsB,EAAE;QACtBH,eAAe,EAAE;MACnB;IACF,CAAC;IACD3B,QAAQ,EAAE,CAAC,GAAGgC,MAAM,CAACC,OAAO,CAAC1B,KAAK,CAACI,OAAO,CAAC,CAACuB,MAAM,CAAC9E,8BAA8B,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAA,CAC7F+E,GAAG,CAACC,KAAA;MAAA,IAAC,CAAC/D,KAAK,CAAC,GAAA+D,KAAA;MAAA,OAAM;QACjBjD,KAAK,EAAE;UACLd;QACF,CAAC;QACD4B,KAAK,EAAE;UACL,CAAC,KAAKxC,aAAa,CAACa,OAAO,EAAE,GAAG;YAC9BD,KAAK,EAAE,CAACkC,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACtC,KAAK,CAAC,CAACgE,IAAI;YAChD,SAAS,EAAE;cACTV,eAAe,EAAEpB,KAAK,CAACG,IAAI,GAAG,QAAQH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACtC,KAAK,CAAC,CAACiE,WAAW,MAAM/B,KAAK,CAACG,IAAI,CAACC,OAAO,CAACiB,MAAM,CAACE,YAAY,GAAG,GAAG9E,KAAK,CAACuD,KAAK,CAACI,OAAO,CAACtC,KAAK,CAAC,CAACgE,IAAI,EAAE9B,KAAK,CAACI,OAAO,CAACiB,MAAM,CAACE,YAAY,CAAC;cAChM,sBAAsB,EAAE;gBACtBH,eAAe,EAAE;cACnB;YACF,CAAC;YACD,CAAC,KAAKlE,aAAa,CAACc,QAAQ,EAAE,GAAG;cAC/BF,KAAK,EAAEkC,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACC,MAAM,CAAC,GAAGvC,KAAK,eAAe,CAAC,GAAG,GAAGkC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG5D,OAAO,CAACqD,KAAK,CAACI,OAAO,CAACtC,KAAK,CAAC,CAACgE,IAAI,EAAE,IAAI,CAAC,GAAGpF,MAAM,CAACsD,KAAK,CAACI,OAAO,CAACtC,KAAK,CAAC,CAACgE,IAAI,EAAE,IAAI,CAAC;YACjM;UACF,CAAC;UACD,CAAC,KAAK5E,aAAa,CAACa,OAAO,OAAOb,aAAa,CAACmB,KAAK,EAAE,GAAG;YACxD+C,eAAe,EAAE,CAACpB,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACtC,KAAK,CAAC,CAACgE;UACxD;QACF;MACF,CAAC;IAAA,CAAC,CAAC;EACL,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAME,WAAW,GAAGjF,MAAM,CAAC,MAAM,EAAE;EACjC0B,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC1B,SAAS,CAACiF,KAAA;EAAA,IAAC;IACZjC;EACF,CAAC,GAAAiC,KAAA;EAAA,OAAM;IACLjD,MAAM,EAAE,MAAM;IACdD,KAAK,EAAE,MAAM;IACbmD,YAAY,EAAE,EAAE,GAAG,CAAC;IACpB5C,MAAM,EAAE,CAAC,CAAC;IACVqB,UAAU,EAAEX,KAAK,CAACY,WAAW,CAACC,MAAM,CAAC,CAAC,SAAS,EAAE,kBAAkB,CAAC,EAAE;MACpEC,QAAQ,EAAEd,KAAK,CAACY,WAAW,CAACE,QAAQ,CAACC;IACvC,CAAC,CAAC;IACFK,eAAe,EAAEpB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACI,MAAM,CAAC2B,YAAY,GAAG,GAAGnC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGP,KAAK,CAACI,OAAO,CAACI,MAAM,CAAC4B,KAAK,GAAGpC,KAAK,CAACI,OAAO,CAACI,MAAM,CAACC,KAAK,EAAE;IACpKQ,OAAO,EAAEjB,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACc,OAAO,CAACoB,WAAW,GAAG,GAAGrC,KAAK,CAACI,OAAO,CAACG,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,GAAG;EACvG,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAM+B,WAAW,GAAGvF,MAAM,CAAC,MAAM,EAAE;EACjC0B,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC1B,SAAS,CAACuF,KAAA;EAAA,IAAC;IACZvC;EACF,CAAC,GAAAuC,KAAA;EAAA,OAAM;IACLC,SAAS,EAAE,CAACxC,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEyC,OAAO,CAAC,CAAC,CAAC;IAC3CrB,eAAe,EAAE,cAAc;IAC/BrC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVkD,YAAY,EAAE;EAChB,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAM7B,MAAM,GAAG,aAAajE,KAAK,CAACsG,UAAU,CAAC,SAASrC,MAAMA,CAACsC,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAMhE,KAAK,GAAG3B,eAAe,CAAC;IAC5B2B,KAAK,EAAE+D,OAAO;IACdlE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJoE,SAAS;IACT/E,KAAK,GAAG,SAAS;IACjBF,IAAI,GAAG,KAAK;IACZC,IAAI,GAAG,QAAQ;IACfiF,EAAE;IACF7E,KAAK,GAAG,CAAC,CAAC;IACV8E,SAAS,GAAG,CAAC,CAAC;IACd,GAAGC;EACL,CAAC,GAAGpE,KAAK;EACT,MAAMlB,UAAU,GAAG;IACjB,GAAGkB,KAAK;IACRd,KAAK;IACLF,IAAI;IACJC;EACF,CAAC;EACD,MAAMF,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMuF,sBAAsB,GAAG;IAC7BhF,KAAK;IACL8E;EACF,CAAC;EACD,MAAM,CAACG,QAAQ,EAAEC,aAAa,CAAC,GAAG/F,OAAO,CAAC,MAAM,EAAE;IAChDyF,SAAS,EAAEvG,IAAI,CAACqB,OAAO,CAACO,IAAI,EAAE2E,SAAS,CAAC;IACxCO,WAAW,EAAE5E,UAAU;IACvByE,sBAAsB;IACtBvF,UAAU;IACV2F,eAAe,EAAE;MACfP;IACF;EACF,CAAC,CAAC;EACF,MAAM,CAACQ,SAAS,EAAEC,cAAc,CAAC,GAAGnG,OAAO,CAAC,OAAO,EAAE;IACnDyF,SAAS,EAAElF,OAAO,CAACS,KAAK;IACxBgF,WAAW,EAAEd,WAAW;IACxBW,sBAAsB;IACtBvF;EACF,CAAC,CAAC;EACF,MAAM8F,IAAI,GAAG,aAAalG,IAAI,CAACgG,SAAS,EAAE;IACxC,GAAGC;EACL,CAAC,CAAC;EACF,MAAM,CAACE,SAAS,EAAEC,cAAc,CAAC,GAAGtG,OAAO,CAAC,OAAO,EAAE;IACnDyF,SAAS,EAAElF,OAAO,CAACU,KAAK;IACxB+E,WAAW,EAAEpB,WAAW;IACxBiB,sBAAsB;IACtBvF;EACF,CAAC,CAAC;EACF,OAAO,aAAaF,KAAK,CAAC0F,QAAQ,EAAE;IAClC,GAAGC,aAAa;IAChBQ,QAAQ,EAAE,CAAC,aAAarG,IAAI,CAACwC,gBAAgB,EAAE;MAC7C8D,IAAI,EAAE,UAAU;MAChBJ,IAAI,EAAEA,IAAI;MACVK,WAAW,EAAEL,IAAI;MACjBZ,GAAG,EAAEA,GAAG;MACRlF,UAAU,EAAEA,UAAU;MACtB,GAAGsF,KAAK;MACRrF,OAAO,EAAE;QACP,GAAGA,OAAO;QACVO,IAAI,EAAEP,OAAO,CAACQ;MAChB,CAAC;MACDF,KAAK,EAAE;QACL,IAAIA,KAAK,CAACE,UAAU,IAAI;UACtBD,IAAI,EAAED,KAAK,CAACE;QACd,CAAC,CAAC;QACF,IAAIF,KAAK,CAACK,KAAK,IAAI;UACjBA,KAAK,EAAEL,KAAK,CAACK;QACf,CAAC;MACH,CAAC;MACDyE,SAAS,EAAE;QACT,IAAIA,SAAS,CAAC5E,UAAU,IAAI;UAC1BD,IAAI,EAAE,OAAO6E,SAAS,CAAC5E,UAAU,KAAK,UAAU,GAAG4E,SAAS,CAAC5E,UAAU,CAACT,UAAU,CAAC,GAAGqF,SAAS,CAAC5E;QAClG,CAAC,CAAC;QACF,IAAI4E,SAAS,CAACzE,KAAK,IAAI;UACrBA,KAAK,EAAE,OAAOyE,SAAS,CAACzE,KAAK,KAAK,UAAU,GAAGyE,SAAS,CAACzE,KAAK,CAACZ,UAAU,CAAC,GAAGqF,SAAS,CAACzE;QACzF,CAAC;MACH;IACF,CAAC,CAAC,EAAE,aAAahB,IAAI,CAACmG,SAAS,EAAE;MAC/B,GAAGC;IACL,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3D,MAAM,CAAC4D,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACElG,OAAO,EAAE1B,SAAS,CAAC6H,IAAI;EACvB;AACF;AACA;EACEL,WAAW,EAAExH,SAAS,CAAC8H,IAAI;EAC3B;AACF;AACA;EACExG,OAAO,EAAEtB,SAAS,CAAC+H,MAAM;EACzB;AACF;AACA;EACEvB,SAAS,EAAExG,SAAS,CAACgI,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEvG,KAAK,EAAEzB,SAAS,CAAC,sCAAsCiI,SAAS,CAAC,CAACjI,SAAS,CAACkI,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAElI,SAAS,CAACgI,MAAM,CAAC,CAAC;EACjL;AACF;AACA;EACEG,cAAc,EAAEnI,SAAS,CAAC6H,IAAI;EAC9B;AACF;AACA;EACElG,QAAQ,EAAE3B,SAAS,CAAC6H,IAAI;EACxB;AACF;AACA;AACA;EACEO,aAAa,EAAEpI,SAAS,CAAC6H,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;EACEtG,IAAI,EAAEvB,SAAS,CAACkI,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC9C;AACF;AACA;EACEf,IAAI,EAAEnH,SAAS,CAAC8H,IAAI;EACpB;AACF;AACA;EACEO,EAAE,EAAErI,SAAS,CAACgI,MAAM;EACpB;AACF;AACA;AACA;EACEM,UAAU,EAAEtI,SAAS,CAAC+H,MAAM;EAC5B;AACF;AACA;AACA;EACEQ,QAAQ,EAAErI,OAAO;EACjB;AACF;AACA;AACA;AACA;AACA;AACA;EACEsI,QAAQ,EAAExI,SAAS,CAACyI,IAAI;EACxB;AACF;AACA;AACA;EACEC,QAAQ,EAAE1I,SAAS,CAAC6H,IAAI;EACxB;AACF;AACA;AACA;AACA;EACErG,IAAI,EAAExB,SAAS,CAAC,sCAAsCiI,SAAS,CAAC,CAACjI,SAAS,CAACkI,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAElI,SAAS,CAACgI,MAAM,CAAC,CAAC;EACzH;AACF;AACA;AACA;EACEtB,SAAS,EAAE1G,SAAS,CAAC2I,KAAK,CAAC;IACzB1G,KAAK,EAAEjC,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACyI,IAAI,EAAEzI,SAAS,CAAC+H,MAAM,CAAC,CAAC;IAC9DlG,IAAI,EAAE7B,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACyI,IAAI,EAAEzI,SAAS,CAAC+H,MAAM,CAAC,CAAC;IAC7DjG,UAAU,EAAE9B,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACyI,IAAI,EAAEzI,SAAS,CAAC+H,MAAM,CAAC,CAAC;IACnEhG,KAAK,EAAE/B,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACyI,IAAI,EAAEzI,SAAS,CAAC+H,MAAM,CAAC,CAAC;IAC9D/F,KAAK,EAAEhC,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACyI,IAAI,EAAEzI,SAAS,CAAC+H,MAAM,CAAC;EAC/D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEnG,KAAK,EAAE5B,SAAS,CAAC2I,KAAK,CAAC;IACrB1G,KAAK,EAAEjC,SAAS,CAAC+G,WAAW;IAC5BlF,IAAI,EAAE7B,SAAS,CAAC+G,WAAW;IAC3BjF,UAAU,EAAE9B,SAAS,CAAC+G,WAAW;IACjChF,KAAK,EAAE/B,SAAS,CAAC+G,WAAW;IAC5B/E,KAAK,EAAEhC,SAAS,CAAC+G;EACnB,CAAC,CAAC;EACF;AACF;AACA;EACEN,EAAE,EAAEzG,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAAC4I,OAAO,CAAC5I,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACyI,IAAI,EAAEzI,SAAS,CAAC+H,MAAM,EAAE/H,SAAS,CAAC6H,IAAI,CAAC,CAAC,CAAC,EAAE7H,SAAS,CAACyI,IAAI,EAAEzI,SAAS,CAAC+H,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEc,KAAK,EAAE7I,SAAS,CAAC8I;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe9E,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}