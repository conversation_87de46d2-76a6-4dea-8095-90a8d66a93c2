{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormText = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    as: Component = 'small',\n    muted,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    className: classNames(className, bsPrefix, muted && 'text-muted')\n  });\n});\nFormText.displayName = 'FormText';\nexport default FormText;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "FormText", "forwardRef", "_ref", "ref", "bsPrefix", "className", "as", "Component", "muted", "props", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/FormText.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormText = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  as: Component = 'small',\n  muted,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    className: classNames(className, bsPrefix, muted && 'text-muted')\n  });\n});\nFormText.displayName = 'FormText';\nexport default FormText;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,QAAQ,GAAG,aAAaJ,KAAK,CAACK,UAAU;AAC9C;AACA,CAAAC,IAAA,EAMGC,GAAG,KAAK;EAAA,IANV;IACCC,QAAQ;IACRC,SAAS;IACTC,EAAE,EAAEC,SAAS,GAAG,OAAO;IACvBC,KAAK;IACL,GAAGC;EACL,CAAC,GAAAP,IAAA;EACCE,QAAQ,GAAGP,kBAAkB,CAACO,QAAQ,EAAE,WAAW,CAAC;EACpD,OAAO,aAAaL,IAAI,CAACQ,SAAS,EAAE;IAClC,GAAGE,KAAK;IACRN,GAAG,EAAEA,GAAG;IACRE,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAED,QAAQ,EAAEI,KAAK,IAAI,YAAY;EAClE,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,QAAQ,CAACU,WAAW,GAAG,UAAU;AACjC,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}