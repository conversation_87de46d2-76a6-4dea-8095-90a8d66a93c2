{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardRoute.tsx\";\nimport React from 'react';\nimport { ThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport materialDashboardTheme from '../../theme/materialDashboardTheme';\nimport DashboardLayout from './DashboardLayout';\nimport ProtectedRoute from '../auth/ProtectedRoute';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardRoute = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: materialDashboardTheme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DashboardLayout, {\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = DashboardRoute;\nexport default DashboardRoute;\nvar _c;\n$RefreshReg$(_c, \"DashboardRoute\");", "map": {"version": 3, "names": ["React", "ThemeProvider", "CssBaseline", "materialDashboardTheme", "DashboardLayout", "ProtectedRoute", "jsxDEV", "_jsxDEV", "DashboardRoute", "children", "theme", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/DashboardRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { ThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport materialDashboardTheme from '../../theme/materialDashboardTheme';\nimport DashboardLayout from './DashboardLayout';\nimport ProtectedRoute from '../auth/ProtectedRoute';\n\ninterface DashboardRouteProps {\n  children: React.ReactNode;\n}\n\nconst DashboardRoute: React.FC<DashboardRouteProps> = ({ children }) => {\n  return (\n    <ProtectedRoute>\n      <ThemeProvider theme={materialDashboardTheme}>\n        <CssBaseline />\n        <DashboardLayout>\n          {children}\n        </DashboardLayout>\n      </ThemeProvider>\n    </ProtectedRoute>\n  );\n};\n\nexport default DashboardRoute;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,WAAW,QAAQ,eAAe;AAC3C,OAAOC,sBAAsB,MAAM,oCAAoC;AACvE,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMpD,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACtE,oBACEF,OAAA,CAACF,cAAc;IAAAI,QAAA,eACbF,OAAA,CAACN,aAAa;MAACS,KAAK,EAAEP,sBAAuB;MAAAM,QAAA,gBAC3CF,OAAA,CAACL,WAAW;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfP,OAAA,CAACH,eAAe;QAAAK,QAAA,EACbA;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAErB,CAAC;AAACC,EAAA,GAXIP,cAA6C;AAanD,eAAeA,cAAc;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}