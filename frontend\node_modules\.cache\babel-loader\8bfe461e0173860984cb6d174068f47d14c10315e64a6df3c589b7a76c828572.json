{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Spinner = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  variant,\n  animation = 'border',\n  size,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  className,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'spinner');\n  const bsSpinnerPrefix = `${bsPrefix}-${animation}`;\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsSpinnerPrefix, size && `${bsSpinnerPrefix}-${size}`, variant && `text-${variant}`)\n  });\n});\nSpinner.displayName = 'Spinner';\nexport default Spinner;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "Spinner", "forwardRef", "bsPrefix", "variant", "animation", "size", "as", "Component", "className", "props", "ref", "bsSpinnerPrefix", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/Spinner.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Spinner = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  variant,\n  animation = 'border',\n  size,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  className,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'spinner');\n  const bsSpinnerPrefix = `${bsPrefix}-${animation}`;\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsSpinnerPrefix, size && `${bsSpinnerPrefix}-${size}`, variant && `text-${variant}`)\n  });\n});\nSpinner.displayName = 'Spinner';\nexport default Spinner;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,OAAO,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EAC7CC,QAAQ;EACRC,OAAO;EACPC,SAAS,GAAG,QAAQ;EACpBC,IAAI;EACJ;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrBC,SAAS;EACT,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTR,QAAQ,GAAGL,kBAAkB,CAACK,QAAQ,EAAE,SAAS,CAAC;EAClD,MAAMS,eAAe,GAAG,GAAGT,QAAQ,IAAIE,SAAS,EAAE;EAClD,OAAO,aAAaL,IAAI,CAACQ,SAAS,EAAE;IAClCG,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRD,SAAS,EAAEb,UAAU,CAACa,SAAS,EAAEG,eAAe,EAAEN,IAAI,IAAI,GAAGM,eAAe,IAAIN,IAAI,EAAE,EAAEF,OAAO,IAAI,QAAQA,OAAO,EAAE;EACtH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFH,OAAO,CAACY,WAAW,GAAG,SAAS;AAC/B,eAAeZ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}