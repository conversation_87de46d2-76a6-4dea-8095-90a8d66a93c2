{"ast": null, "code": "export { default } from \"./TableHead.js\";\nexport { default as tableHeadClasses } from \"./tableHeadClasses.js\";\nexport * from \"./tableHeadClasses.js\";", "map": {"version": 3, "names": ["default", "tableHeadClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/TableHead/index.js"], "sourcesContent": ["export { default } from \"./TableHead.js\";\nexport { default as tableHeadClasses } from \"./tableHeadClasses.js\";\nexport * from \"./tableHeadClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AACxC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,uBAAuB;AACnE,cAAc,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}