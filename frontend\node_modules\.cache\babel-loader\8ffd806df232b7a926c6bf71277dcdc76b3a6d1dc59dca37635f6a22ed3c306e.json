{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport FocusTrap from \"../Unstable_TrapFocus/index.js\";\nimport Portal from \"../Portal/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Backdrop from \"../Backdrop/index.js\";\nimport useModal from \"./useModal.js\";\nimport { getModalUtilityClass } from \"./modalClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    exited,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', !open && exited && 'hidden'],\n    backdrop: ['backdrop']\n  };\n  return composeClasses(slots, getModalUtilityClass, classes);\n};\nconst ModalRoot = styled('div', {\n  name: 'MuiModal',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.open && ownerState.exited && styles.hidden];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'fixed',\n  zIndex: (theme.vars || theme).zIndex.modal,\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0,\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.open && ownerState.exited,\n    style: {\n      visibility: 'hidden'\n    }\n  }]\n})));\nconst ModalBackdrop = styled(Backdrop, {\n  name: 'MuiModal',\n  slot: 'Backdrop'\n})({\n  zIndex: -1\n});\n\n/**\n * Modal is a lower-level construct that is leveraged by the following components:\n *\n * - [Dialog](/material-ui/api/dialog/)\n * - [Drawer](/material-ui/api/drawer/)\n * - [Menu](/material-ui/api/menu/)\n * - [Popover](/material-ui/api/popover/)\n *\n * If you are creating a modal dialog, you probably want to use the [Dialog](/material-ui/api/dialog/) component\n * rather than directly using Modal.\n *\n * This component shares many concepts with [react-overlays](https://react-bootstrap.github.io/react-overlays/#modals).\n */\nconst Modal = /*#__PURE__*/React.forwardRef(function Modal(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiModal',\n    props: inProps\n  });\n  const {\n    BackdropComponent = ModalBackdrop,\n    BackdropProps,\n    classes: classesProp,\n    className,\n    closeAfterTransition = false,\n    children,\n    container,\n    component,\n    components = {},\n    componentsProps = {},\n    disableAutoFocus = false,\n    disableEnforceFocus = false,\n    disableEscapeKeyDown = false,\n    disablePortal = false,\n    disableRestoreFocus = false,\n    disableScrollLock = false,\n    hideBackdrop = false,\n    keepMounted = false,\n    onClose,\n    onTransitionEnter,\n    onTransitionExited,\n    open,\n    slotProps = {},\n    slots = {},\n    // eslint-disable-next-line react/prop-types\n    theme,\n    ...other\n  } = props;\n  const propsWithDefaults = {\n    ...props,\n    closeAfterTransition,\n    disableAutoFocus,\n    disableEnforceFocus,\n    disableEscapeKeyDown,\n    disablePortal,\n    disableRestoreFocus,\n    disableScrollLock,\n    hideBackdrop,\n    keepMounted\n  };\n  const {\n    getRootProps,\n    getBackdropProps,\n    getTransitionProps,\n    portalRef,\n    isTopModal,\n    exited,\n    hasTransition\n  } = useModal({\n    ...propsWithDefaults,\n    rootRef: ref\n  });\n  const ownerState = {\n    ...propsWithDefaults,\n    exited\n  };\n  const classes = useUtilityClasses(ownerState);\n  const childProps = {};\n  if (children.props.tabIndex === undefined) {\n    childProps.tabIndex = '-1';\n  }\n\n  // It's a Transition like component\n  if (hasTransition) {\n    const {\n      onEnter,\n      onExited\n    } = getTransitionProps();\n    childProps.onEnter = onEnter;\n    childProps.onExited = onExited;\n  }\n  const externalForwardedProps = {\n    slots: {\n      root: components.Root,\n      backdrop: components.Backdrop,\n      ...slots\n    },\n    slotProps: {\n      ...componentsProps,\n      ...slotProps\n    }\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    ref,\n    elementType: ModalRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other,\n      component\n    },\n    getSlotProps: getRootProps,\n    ownerState,\n    className: clsx(className, classes?.root, !ownerState.open && ownerState.exited && classes?.hidden)\n  });\n  const [BackdropSlot, backdropProps] = useSlot('backdrop', {\n    ref: BackdropProps?.ref,\n    elementType: BackdropComponent,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    additionalProps: BackdropProps,\n    getSlotProps: otherHandlers => {\n      return getBackdropProps({\n        ...otherHandlers,\n        onClick: event => {\n          if (otherHandlers?.onClick) {\n            otherHandlers.onClick(event);\n          }\n        }\n      });\n    },\n    className: clsx(BackdropProps?.className, classes?.backdrop),\n    ownerState\n  });\n  if (!keepMounted && !open && (!hasTransition || exited)) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Portal, {\n    ref: portalRef,\n    container: container,\n    disablePortal: disablePortal,\n    children: /*#__PURE__*/_jsxs(RootSlot, {\n      ...rootProps,\n      children: [!hideBackdrop && BackdropComponent ? /*#__PURE__*/_jsx(BackdropSlot, {\n        ...backdropProps\n      }) : null, /*#__PURE__*/_jsx(FocusTrap, {\n        disableEnforceFocus: disableEnforceFocus,\n        disableAutoFocus: disableAutoFocus,\n        disableRestoreFocus: disableRestoreFocus,\n        isEnabled: isTopModal,\n        open: open,\n        children: /*#__PURE__*/React.cloneElement(children, childProps)\n      })]\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Modal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](https://mui.com/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * When set to true the Modal waits until a nested Transition is completed before closing.\n   * @default false\n   */\n  closeAfterTransition: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Backdrop: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true`, the modal will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any modal children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the modal will not prevent focus from leaving the modal while open.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * If `true`, the modal will not restore focus to previously focused element once\n   * modal is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Modal.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * A function called when a transition enters.\n   */\n  onTransitionEnter: PropTypes.func,\n  /**\n   * A function called when a transition has exited.\n   */\n  onTransitionExited: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside the Modal.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Modal.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Modal;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "HTMLElementType", "elementAcceptingRef", "composeClasses", "FocusTrap", "Portal", "styled", "memoTheme", "useDefaultProps", "Backdrop", "useModal", "getModalUtilityClass", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "open", "exited", "classes", "slots", "root", "backdrop", "ModalRoot", "name", "slot", "overridesResolver", "props", "styles", "hidden", "theme", "position", "zIndex", "vars", "modal", "right", "bottom", "top", "left", "variants", "style", "visibility", "ModalBackdrop", "Modal", "forwardRef", "inProps", "ref", "BackdropComponent", "BackdropProps", "classesProp", "className", "closeAfterTransition", "children", "container", "component", "components", "componentsProps", "disableAutoFocus", "disableEnforceFocus", "disableEscapeKeyDown", "disable<PERSON><PERSON><PERSON>", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableScrollLock", "hideBackdrop", "keepMounted", "onClose", "onTransitionEnter", "onTransitionExited", "slotProps", "other", "propsWithDefaults", "getRootProps", "getBackdropProps", "getTransitionProps", "portalRef", "isTopModal", "hasTransition", "rootRef", "childProps", "tabIndex", "undefined", "onEnter", "onExited", "externalForwardedProps", "Root", "RootSlot", "rootProps", "elementType", "getSlotProps", "BackdropSlot", "backdropProps", "shouldForwardComponentProp", "additionalProps", "otherHandlers", "onClick", "event", "isEnabled", "cloneElement", "process", "env", "NODE_ENV", "propTypes", "object", "isRequired", "string", "bool", "shape", "oneOfType", "func", "sx", "arrayOf"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Modal/Modal.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport FocusTrap from \"../Unstable_TrapFocus/index.js\";\nimport Portal from \"../Portal/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Backdrop from \"../Backdrop/index.js\";\nimport useModal from \"./useModal.js\";\nimport { getModalUtilityClass } from \"./modalClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    exited,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', !open && exited && 'hidden'],\n    backdrop: ['backdrop']\n  };\n  return composeClasses(slots, getModalUtilityClass, classes);\n};\nconst ModalRoot = styled('div', {\n  name: 'MuiModal',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.open && ownerState.exited && styles.hidden];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'fixed',\n  zIndex: (theme.vars || theme).zIndex.modal,\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0,\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.open && ownerState.exited,\n    style: {\n      visibility: 'hidden'\n    }\n  }]\n})));\nconst ModalBackdrop = styled(Backdrop, {\n  name: 'MuiModal',\n  slot: 'Backdrop'\n})({\n  zIndex: -1\n});\n\n/**\n * Modal is a lower-level construct that is leveraged by the following components:\n *\n * - [Dialog](/material-ui/api/dialog/)\n * - [Drawer](/material-ui/api/drawer/)\n * - [Menu](/material-ui/api/menu/)\n * - [Popover](/material-ui/api/popover/)\n *\n * If you are creating a modal dialog, you probably want to use the [Dialog](/material-ui/api/dialog/) component\n * rather than directly using Modal.\n *\n * This component shares many concepts with [react-overlays](https://react-bootstrap.github.io/react-overlays/#modals).\n */\nconst Modal = /*#__PURE__*/React.forwardRef(function Modal(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiModal',\n    props: inProps\n  });\n  const {\n    BackdropComponent = ModalBackdrop,\n    BackdropProps,\n    classes: classesProp,\n    className,\n    closeAfterTransition = false,\n    children,\n    container,\n    component,\n    components = {},\n    componentsProps = {},\n    disableAutoFocus = false,\n    disableEnforceFocus = false,\n    disableEscapeKeyDown = false,\n    disablePortal = false,\n    disableRestoreFocus = false,\n    disableScrollLock = false,\n    hideBackdrop = false,\n    keepMounted = false,\n    onClose,\n    onTransitionEnter,\n    onTransitionExited,\n    open,\n    slotProps = {},\n    slots = {},\n    // eslint-disable-next-line react/prop-types\n    theme,\n    ...other\n  } = props;\n  const propsWithDefaults = {\n    ...props,\n    closeAfterTransition,\n    disableAutoFocus,\n    disableEnforceFocus,\n    disableEscapeKeyDown,\n    disablePortal,\n    disableRestoreFocus,\n    disableScrollLock,\n    hideBackdrop,\n    keepMounted\n  };\n  const {\n    getRootProps,\n    getBackdropProps,\n    getTransitionProps,\n    portalRef,\n    isTopModal,\n    exited,\n    hasTransition\n  } = useModal({\n    ...propsWithDefaults,\n    rootRef: ref\n  });\n  const ownerState = {\n    ...propsWithDefaults,\n    exited\n  };\n  const classes = useUtilityClasses(ownerState);\n  const childProps = {};\n  if (children.props.tabIndex === undefined) {\n    childProps.tabIndex = '-1';\n  }\n\n  // It's a Transition like component\n  if (hasTransition) {\n    const {\n      onEnter,\n      onExited\n    } = getTransitionProps();\n    childProps.onEnter = onEnter;\n    childProps.onExited = onExited;\n  }\n  const externalForwardedProps = {\n    slots: {\n      root: components.Root,\n      backdrop: components.Backdrop,\n      ...slots\n    },\n    slotProps: {\n      ...componentsProps,\n      ...slotProps\n    }\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    ref,\n    elementType: ModalRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other,\n      component\n    },\n    getSlotProps: getRootProps,\n    ownerState,\n    className: clsx(className, classes?.root, !ownerState.open && ownerState.exited && classes?.hidden)\n  });\n  const [BackdropSlot, backdropProps] = useSlot('backdrop', {\n    ref: BackdropProps?.ref,\n    elementType: BackdropComponent,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    additionalProps: BackdropProps,\n    getSlotProps: otherHandlers => {\n      return getBackdropProps({\n        ...otherHandlers,\n        onClick: event => {\n          if (otherHandlers?.onClick) {\n            otherHandlers.onClick(event);\n          }\n        }\n      });\n    },\n    className: clsx(BackdropProps?.className, classes?.backdrop),\n    ownerState\n  });\n  if (!keepMounted && !open && (!hasTransition || exited)) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Portal, {\n    ref: portalRef,\n    container: container,\n    disablePortal: disablePortal,\n    children: /*#__PURE__*/_jsxs(RootSlot, {\n      ...rootProps,\n      children: [!hideBackdrop && BackdropComponent ? /*#__PURE__*/_jsx(BackdropSlot, {\n        ...backdropProps\n      }) : null, /*#__PURE__*/_jsx(FocusTrap, {\n        disableEnforceFocus: disableEnforceFocus,\n        disableAutoFocus: disableAutoFocus,\n        disableRestoreFocus: disableRestoreFocus,\n        isEnabled: isTopModal,\n        open: open,\n        children: /*#__PURE__*/React.cloneElement(children, childProps)\n      })]\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Modal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](https://mui.com/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * When set to true the Modal waits until a nested Transition is completed before closing.\n   * @default false\n   */\n  closeAfterTransition: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Backdrop: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true`, the modal will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any modal children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the modal will not prevent focus from leaving the modal while open.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * If `true`, the modal will not restore focus to previously focused element once\n   * modal is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Modal.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * A function called when a transition enters.\n   */\n  onTransitionEnter: PropTypes.func,\n  /**\n   * A function called when a transition has exited.\n   */\n  onTransitionExited: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside the Modal.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Modal.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Modal;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,mBAAmB,MAAM,gCAAgC;AAChE,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,SAAS,MAAM,gCAAgC;AACtD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,QAAQ,MAAM,eAAe;AACpC,SAASC,oBAAoB,QAAQ,mBAAmB;AACxD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,IAAI;IACJC,MAAM;IACNC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,CAACJ,IAAI,IAAIC,MAAM,IAAI,QAAQ,CAAC;IAC3CI,QAAQ,EAAE,CAAC,UAAU;EACvB,CAAC;EACD,OAAOrB,cAAc,CAACmB,KAAK,EAAEX,oBAAoB,EAAEU,OAAO,CAAC;AAC7D,CAAC;AACD,MAAMI,SAAS,GAAGnB,MAAM,CAAC,KAAK,EAAE;EAC9BoB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACP,IAAI,EAAE,CAACL,UAAU,CAACC,IAAI,IAAID,UAAU,CAACE,MAAM,IAAIU,MAAM,CAACC,MAAM,CAAC;EAC9E;AACF,CAAC,CAAC,CAACxB,SAAS,CAAC,CAAC;EACZyB;AACF,CAAC,MAAM;EACLC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAACF,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEE,MAAM,CAACE,KAAK;EAC1CC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,QAAQ,EAAE,CAAC;IACTZ,KAAK,EAAEA,CAAC;MACNX;IACF,CAAC,KAAK,CAACA,UAAU,CAACC,IAAI,IAAID,UAAU,CAACE,MAAM;IAC3CsB,KAAK,EAAE;MACLC,UAAU,EAAE;IACd;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMC,aAAa,GAAGtC,MAAM,CAACG,QAAQ,EAAE;EACrCiB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDO,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,KAAK,GAAG,aAAa/C,KAAK,CAACgD,UAAU,CAAC,SAASD,KAAKA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvE,MAAMnB,KAAK,GAAGrB,eAAe,CAAC;IAC5BkB,IAAI,EAAE,UAAU;IAChBG,KAAK,EAAEkB;EACT,CAAC,CAAC;EACF,MAAM;IACJE,iBAAiB,GAAGL,aAAa;IACjCM,aAAa;IACb7B,OAAO,EAAE8B,WAAW;IACpBC,SAAS;IACTC,oBAAoB,GAAG,KAAK;IAC5BC,QAAQ;IACRC,SAAS;IACTC,SAAS;IACTC,UAAU,GAAG,CAAC,CAAC;IACfC,eAAe,GAAG,CAAC,CAAC;IACpBC,gBAAgB,GAAG,KAAK;IACxBC,mBAAmB,GAAG,KAAK;IAC3BC,oBAAoB,GAAG,KAAK;IAC5BC,aAAa,GAAG,KAAK;IACrBC,mBAAmB,GAAG,KAAK;IAC3BC,iBAAiB,GAAG,KAAK;IACzBC,YAAY,GAAG,KAAK;IACpBC,WAAW,GAAG,KAAK;IACnBC,OAAO;IACPC,iBAAiB;IACjBC,kBAAkB;IAClBlD,IAAI;IACJmD,SAAS,GAAG,CAAC,CAAC;IACdhD,KAAK,GAAG,CAAC,CAAC;IACV;IACAU,KAAK;IACL,GAAGuC;EACL,CAAC,GAAG1C,KAAK;EACT,MAAM2C,iBAAiB,GAAG;IACxB,GAAG3C,KAAK;IACRwB,oBAAoB;IACpBM,gBAAgB;IAChBC,mBAAmB;IACnBC,oBAAoB;IACpBC,aAAa;IACbC,mBAAmB;IACnBC,iBAAiB;IACjBC,YAAY;IACZC;EACF,CAAC;EACD,MAAM;IACJO,YAAY;IACZC,gBAAgB;IAChBC,kBAAkB;IAClBC,SAAS;IACTC,UAAU;IACVzD,MAAM;IACN0D;EACF,CAAC,GAAGpE,QAAQ,CAAC;IACX,GAAG8D,iBAAiB;IACpBO,OAAO,EAAE/B;EACX,CAAC,CAAC;EACF,MAAM9B,UAAU,GAAG;IACjB,GAAGsD,iBAAiB;IACpBpD;EACF,CAAC;EACD,MAAMC,OAAO,GAAGJ,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM8D,UAAU,GAAG,CAAC,CAAC;EACrB,IAAI1B,QAAQ,CAACzB,KAAK,CAACoD,QAAQ,KAAKC,SAAS,EAAE;IACzCF,UAAU,CAACC,QAAQ,GAAG,IAAI;EAC5B;;EAEA;EACA,IAAIH,aAAa,EAAE;IACjB,MAAM;MACJK,OAAO;MACPC;IACF,CAAC,GAAGT,kBAAkB,CAAC,CAAC;IACxBK,UAAU,CAACG,OAAO,GAAGA,OAAO;IAC5BH,UAAU,CAACI,QAAQ,GAAGA,QAAQ;EAChC;EACA,MAAMC,sBAAsB,GAAG;IAC7B/D,KAAK,EAAE;MACLC,IAAI,EAAEkC,UAAU,CAAC6B,IAAI;MACrB9D,QAAQ,EAAEiC,UAAU,CAAChD,QAAQ;MAC7B,GAAGa;IACL,CAAC;IACDgD,SAAS,EAAE;MACT,GAAGZ,eAAe;MAClB,GAAGY;IACL;EACF,CAAC;EACD,MAAM,CAACiB,QAAQ,EAAEC,SAAS,CAAC,GAAG5E,OAAO,CAAC,MAAM,EAAE;IAC5CoC,GAAG;IACHyC,WAAW,EAAEhE,SAAS;IACtB4D,sBAAsB,EAAE;MACtB,GAAGA,sBAAsB;MACzB,GAAGd,KAAK;MACRf;IACF,CAAC;IACDkC,YAAY,EAAEjB,YAAY;IAC1BvD,UAAU;IACVkC,SAAS,EAAEpD,IAAI,CAACoD,SAAS,EAAE/B,OAAO,EAAEE,IAAI,EAAE,CAACL,UAAU,CAACC,IAAI,IAAID,UAAU,CAACE,MAAM,IAAIC,OAAO,EAAEU,MAAM;EACpG,CAAC,CAAC;EACF,MAAM,CAAC4D,YAAY,EAAEC,aAAa,CAAC,GAAGhF,OAAO,CAAC,UAAU,EAAE;IACxDoC,GAAG,EAAEE,aAAa,EAAEF,GAAG;IACvByC,WAAW,EAAExC,iBAAiB;IAC9BoC,sBAAsB;IACtBQ,0BAA0B,EAAE,IAAI;IAChCC,eAAe,EAAE5C,aAAa;IAC9BwC,YAAY,EAAEK,aAAa,IAAI;MAC7B,OAAOrB,gBAAgB,CAAC;QACtB,GAAGqB,aAAa;QAChBC,OAAO,EAAEC,KAAK,IAAI;UAChB,IAAIF,aAAa,EAAEC,OAAO,EAAE;YAC1BD,aAAa,CAACC,OAAO,CAACC,KAAK,CAAC;UAC9B;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IACD7C,SAAS,EAAEpD,IAAI,CAACkD,aAAa,EAAEE,SAAS,EAAE/B,OAAO,EAAEG,QAAQ,CAAC;IAC5DN;EACF,CAAC,CAAC;EACF,IAAI,CAACgD,WAAW,IAAI,CAAC/C,IAAI,KAAK,CAAC2D,aAAa,IAAI1D,MAAM,CAAC,EAAE;IACvD,OAAO,IAAI;EACb;EACA,OAAO,aAAaN,IAAI,CAACT,MAAM,EAAE;IAC/B2C,GAAG,EAAE4B,SAAS;IACdrB,SAAS,EAAEA,SAAS;IACpBO,aAAa,EAAEA,aAAa;IAC5BR,QAAQ,EAAE,aAAatC,KAAK,CAACuE,QAAQ,EAAE;MACrC,GAAGC,SAAS;MACZlC,QAAQ,EAAE,CAAC,CAACW,YAAY,IAAIhB,iBAAiB,GAAG,aAAanC,IAAI,CAAC6E,YAAY,EAAE;QAC9E,GAAGC;MACL,CAAC,CAAC,GAAG,IAAI,EAAE,aAAa9E,IAAI,CAACV,SAAS,EAAE;QACtCwD,mBAAmB,EAAEA,mBAAmB;QACxCD,gBAAgB,EAAEA,gBAAgB;QAClCI,mBAAmB,EAAEA,mBAAmB;QACxCmC,SAAS,EAAErB,UAAU;QACrB1D,IAAI,EAAEA,IAAI;QACVmC,QAAQ,EAAE,aAAaxD,KAAK,CAACqG,YAAY,CAAC7C,QAAQ,EAAE0B,UAAU;MAChE,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzD,KAAK,CAAC0D,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtD,iBAAiB,EAAElD,SAAS,CAAC0F,WAAW;EACxC;AACF;AACA;AACA;EACEvC,aAAa,EAAEnD,SAAS,CAACyG,MAAM;EAC/B;AACF;AACA;EACElD,QAAQ,EAAEpD,mBAAmB,CAACuG,UAAU;EACxC;AACF;AACA;EACEpF,OAAO,EAAEtB,SAAS,CAACyG,MAAM;EACzB;AACF;AACA;EACEpD,SAAS,EAAErD,SAAS,CAAC2G,MAAM;EAC3B;AACF;AACA;AACA;EACErD,oBAAoB,EAAEtD,SAAS,CAAC4G,IAAI;EACpC;AACF;AACA;AACA;EACEnD,SAAS,EAAEzD,SAAS,CAAC0F,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;EACEhC,UAAU,EAAE1D,SAAS,CAAC6G,KAAK,CAAC;IAC1BnG,QAAQ,EAAEV,SAAS,CAAC0F,WAAW;IAC/BH,IAAI,EAAEvF,SAAS,CAAC0F;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE/B,eAAe,EAAE3D,SAAS,CAAC6G,KAAK,CAAC;IAC/BpF,QAAQ,EAAEzB,SAAS,CAAC8G,SAAS,CAAC,CAAC9G,SAAS,CAAC+G,IAAI,EAAE/G,SAAS,CAACyG,MAAM,CAAC,CAAC;IACjEjF,IAAI,EAAExB,SAAS,CAAC8G,SAAS,CAAC,CAAC9G,SAAS,CAAC+G,IAAI,EAAE/G,SAAS,CAACyG,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEjD,SAAS,EAAExD,SAAS,CAAC,sCAAsC8G,SAAS,CAAC,CAAC5G,eAAe,EAAEF,SAAS,CAAC+G,IAAI,CAAC,CAAC;EACvG;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEnD,gBAAgB,EAAE5D,SAAS,CAAC4G,IAAI;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;EACE/C,mBAAmB,EAAE7D,SAAS,CAAC4G,IAAI;EACnC;AACF;AACA;AACA;EACE9C,oBAAoB,EAAE9D,SAAS,CAAC4G,IAAI;EACpC;AACF;AACA;AACA;EACE7C,aAAa,EAAE/D,SAAS,CAAC4G,IAAI;EAC7B;AACF;AACA;AACA;AACA;EACE5C,mBAAmB,EAAEhE,SAAS,CAAC4G,IAAI;EACnC;AACF;AACA;AACA;EACE3C,iBAAiB,EAAEjE,SAAS,CAAC4G,IAAI;EACjC;AACF;AACA;AACA;EACE1C,YAAY,EAAElE,SAAS,CAAC4G,IAAI;EAC5B;AACF;AACA;AACA;AACA;AACA;EACEzC,WAAW,EAAEnE,SAAS,CAAC4G,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;EACExC,OAAO,EAAEpE,SAAS,CAAC+G,IAAI;EACvB;AACF;AACA;EACE1C,iBAAiB,EAAErE,SAAS,CAAC+G,IAAI;EACjC;AACF;AACA;EACEzC,kBAAkB,EAAEtE,SAAS,CAAC+G,IAAI;EAClC;AACF;AACA;EACE3F,IAAI,EAAEpB,SAAS,CAAC4G,IAAI,CAACF,UAAU;EAC/B;AACF;AACA;AACA;EACEnC,SAAS,EAAEvE,SAAS,CAAC6G,KAAK,CAAC;IACzBpF,QAAQ,EAAEzB,SAAS,CAAC8G,SAAS,CAAC,CAAC9G,SAAS,CAAC+G,IAAI,EAAE/G,SAAS,CAACyG,MAAM,CAAC,CAAC;IACjEjF,IAAI,EAAExB,SAAS,CAAC8G,SAAS,CAAC,CAAC9G,SAAS,CAAC+G,IAAI,EAAE/G,SAAS,CAACyG,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACElF,KAAK,EAAEvB,SAAS,CAAC6G,KAAK,CAAC;IACrBpF,QAAQ,EAAEzB,SAAS,CAAC0F,WAAW;IAC/BlE,IAAI,EAAExB,SAAS,CAAC0F;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEsB,EAAE,EAAEhH,SAAS,CAAC8G,SAAS,CAAC,CAAC9G,SAAS,CAACiH,OAAO,CAACjH,SAAS,CAAC8G,SAAS,CAAC,CAAC9G,SAAS,CAAC+G,IAAI,EAAE/G,SAAS,CAACyG,MAAM,EAAEzG,SAAS,CAAC4G,IAAI,CAAC,CAAC,CAAC,EAAE5G,SAAS,CAAC+G,IAAI,EAAE/G,SAAS,CAACyG,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAe3D,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}