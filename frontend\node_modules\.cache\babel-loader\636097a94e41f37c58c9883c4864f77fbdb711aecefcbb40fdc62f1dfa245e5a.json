{"ast": null, "code": "import React from'react';import{<PERSON>,Typo<PERSON>,Button}from'@mui/material';import{Add}from'@mui/icons-material';import DataTable,{createAvatarFormatter,createChipFormatter}from'../../components/dashboard/DataTable';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Users=()=>{// Mock data - replace with actual API call\nconst users=[{id:1,name:'<PERSON>',email:'<EMAIL>',role:'Admin',status:'active'},{id:2,name:'<PERSON>',email:'<EMAIL>',role:'User',status:'active'},{id:3,name:'<PERSON>',email:'<EMAIL>',role:'Moderator',status:'inactive'},{id:4,name:'<PERSON>',email:'<EMAIL>',role:'User',status:'active'}];const handleAddUser=()=>{console.log('Add user clicked');// Implement add user functionality\n};const columns=[{id:'name',label:'User',minWidth:200,format:createAvatarFormatter('name')},{id:'email',label:'Email',minWidth:200},{id:'role',label:'Role',minWidth:120,format:createChipFormatter({Admin:'error',Moderator:'warning',User:'primary'})},{id:'status',label:'Status',minWidth:120,format:createChipFormatter({active:'success',inactive:'default'})}];return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:3},children:/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",children:\"Users Management\"})}),/*#__PURE__*/_jsx(DataTable,{title:\"Users\",columns:columns,data:users,searchPlaceholder:\"Search users by name or email...\",onRowClick:user=>console.log('Row clicked:',user),actions:/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Add,{}),onClick:handleAddUser,children:\"Add User\"})})]});};export default Users;", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "Add", "DataTable", "createAvatar<PERSON><PERSON><PERSON><PERSON>", "createChipFormatter", "jsx", "_jsx", "jsxs", "_jsxs", "Users", "users", "id", "name", "email", "role", "status", "handleAddUser", "console", "log", "columns", "label", "min<PERSON><PERSON><PERSON>", "format", "Admin", "Moderator", "User", "active", "inactive", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "component", "title", "data", "searchPlaceholder", "onRowClick", "user", "actions", "startIcon", "onClick"], "sources": ["C:/laragon/www/frontend/src/pages/dashboard/Users.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON>,\n  Typo<PERSON>,\n  Button,\n} from '@mui/material';\nimport {\n  Add,\n} from '@mui/icons-material';\nimport DataTable, { Column, createAvatarFormatter, createChipFormatter } from '../../components/dashboard/DataTable';\n\ninterface User {\n  id: number;\n  name: string;\n  email: string;\n  role: string;\n  status: 'active' | 'inactive';\n  avatar?: string;\n}\n\nconst Users: React.FC = () => {\n  // Mock data - replace with actual API call\n  const users: User[] = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      role: 'Admin',\n      status: 'active',\n    },\n    {\n      id: 2,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      role: 'User',\n      status: 'active',\n    },\n    {\n      id: 3,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      role: 'Moderator',\n      status: 'inactive',\n    },\n    {\n      id: 4,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      role: 'User',\n      status: 'active',\n    },\n  ];\n\n  const handleAddUser = () => {\n    console.log('Add user clicked');\n    // Implement add user functionality\n  };\n\n  const columns: Column[] = [\n    {\n      id: 'name',\n      label: 'User',\n      minWidth: 200,\n      format: createAvatarFormatter('name'),\n    },\n    {\n      id: 'email',\n      label: 'Email',\n      minWidth: 200,\n    },\n    {\n      id: 'role',\n      label: 'Role',\n      minWidth: 120,\n      format: createChipFormatter({\n        Admin: 'error',\n        Moderator: 'warning',\n        User: 'primary',\n      }),\n    },\n    {\n      id: 'status',\n      label: 'Status',\n      minWidth: 120,\n      format: createChipFormatter({\n        active: 'success',\n        inactive: 'default',\n      }),\n    },\n  ];\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" component=\"h1\">\n          Users Management\n        </Typography>\n      </Box>\n\n      <DataTable\n        title=\"Users\"\n        columns={columns}\n        data={users}\n        searchPlaceholder=\"Search users by name or email...\"\n        onRowClick={(user) => console.log('Row clicked:', user)}\n        actions={\n          <Button\n            variant=\"contained\"\n            startIcon={<Add />}\n            onClick={handleAddUser}\n          >\n            Add User\n          </Button>\n        }\n      />\n    </Box>\n  );\n};\n\nexport default Users;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,GAAG,CACHC,UAAU,CACVC,MAAM,KACD,eAAe,CACtB,OACEC,GAAG,KACE,qBAAqB,CAC5B,MAAO,CAAAC,SAAS,EAAYC,qBAAqB,CAAEC,mBAAmB,KAAQ,sCAAsC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWrH,KAAM,CAAAC,KAAe,CAAGA,CAAA,GAAM,CAC5B;AACA,KAAM,CAAAC,KAAa,CAAG,CACpB,CACEC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,sBAAsB,CAC7BC,IAAI,CAAE,OAAO,CACbC,MAAM,CAAE,QACV,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,wBAAwB,CAC/BC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QACV,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,aAAa,CACnBC,KAAK,CAAE,yBAAyB,CAChCC,IAAI,CAAE,WAAW,CACjBC,MAAM,CAAE,UACV,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,aAAa,CACnBC,KAAK,CAAE,yBAAyB,CAChCC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QACV,CAAC,CACF,CAED,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1BC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC,CAC/B;AACF,CAAC,CAED,KAAM,CAAAC,OAAiB,CAAG,CACxB,CACER,EAAE,CAAE,MAAM,CACVS,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAEnB,qBAAqB,CAAC,MAAM,CACtC,CAAC,CACD,CACEQ,EAAE,CAAE,OAAO,CACXS,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,GACZ,CAAC,CACD,CACEV,EAAE,CAAE,MAAM,CACVS,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAElB,mBAAmB,CAAC,CAC1BmB,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,SAAS,CACpBC,IAAI,CAAE,SACR,CAAC,CACH,CAAC,CACD,CACEd,EAAE,CAAE,QAAQ,CACZS,KAAK,CAAE,QAAQ,CACfC,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAElB,mBAAmB,CAAC,CAC1BsB,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,SACZ,CAAC,CACH,CAAC,CACF,CAED,mBACEnB,KAAA,CAACV,GAAG,EAAA8B,QAAA,eACFtB,IAAA,CAACR,GAAG,EAAC+B,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,cACzFtB,IAAA,CAACP,UAAU,EAACmC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAAP,QAAA,CAAC,kBAExC,CAAY,CAAC,CACV,CAAC,cAENtB,IAAA,CAACJ,SAAS,EACRkC,KAAK,CAAC,OAAO,CACbjB,OAAO,CAAEA,OAAQ,CACjBkB,IAAI,CAAE3B,KAAM,CACZ4B,iBAAiB,CAAC,kCAAkC,CACpDC,UAAU,CAAGC,IAAI,EAAKvB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEsB,IAAI,CAAE,CACxDC,OAAO,cACLnC,IAAA,CAACN,MAAM,EACLkC,OAAO,CAAC,WAAW,CACnBQ,SAAS,cAAEpC,IAAA,CAACL,GAAG,GAAE,CAAE,CACnB0C,OAAO,CAAE3B,aAAc,CAAAY,QAAA,CACxB,UAED,CAAQ,CACT,CACF,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}