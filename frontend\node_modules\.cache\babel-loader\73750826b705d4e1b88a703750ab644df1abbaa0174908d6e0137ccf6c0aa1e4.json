{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\FileUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Box, Typography, Paper, Button, LinearProgress, Alert, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, Grid2 as Grid, Card, CardContent, CardActions, Tooltip } from '@mui/material';\nimport { CloudUpload as UploadIcon, Delete as DeleteIcon, Warning as WarningIcon, CheckCircle as CheckIcon, Error as ErrorIcon, Image as ImageIcon, PictureAsPdf as PdfIcon, Description as FileIcon } from '@mui/icons-material';\nimport { useDropzone } from 'react-dropzone';\nimport printingService from '../../services/printingService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FileUpload = ({\n  orderId,\n  onFilesUploaded,\n  onError\n}) => {\n  _s();\n  const [settings, setSettings] = useState(null);\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [uploadingFiles, setUploadingFiles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [fileToDelete, setFileToDelete] = useState(null);\n  const [selectedFileType, setSelectedFileType] = useState('artwork');\n\n  // Load settings and files on component mount\n  useEffect(() => {\n    loadSettings();\n    if (orderId) {\n      loadUploadedFiles();\n    }\n  }, [orderId]);\n  const loadSettings = async () => {\n    try {\n      const uploadSettings = await printingService.getUploadSettings();\n      setSettings(uploadSettings);\n    } catch (error) {\n      console.error('Failed to load upload settings:', error);\n      onError === null || onError === void 0 ? void 0 : onError('Failed to load upload settings');\n    }\n  };\n  const loadUploadedFiles = async () => {\n    if (!orderId) return;\n    try {\n      const files = await printingService.getOrderFiles(orderId);\n      setUploadedFiles(files);\n    } catch (error) {\n      console.error('Failed to load uploaded files:', error);\n    }\n  };\n\n  // Validate files before upload\n  const validateFiles = async files => {\n    if (!settings) return {\n      valid: [],\n      invalid: []\n    };\n    const valid = [];\n    const invalid = [];\n\n    // Check total file count\n    const totalFiles = uploadedFiles.length + files.length;\n    if (totalFiles > settings.max_files_per_order) {\n      const excess = totalFiles - settings.max_files_per_order;\n      for (let i = files.length - excess; i < files.length; i++) {\n        invalid.push({\n          file: files[i],\n          errors: [`Maximum ${settings.max_files_per_order} files allowed per order`]\n        });\n      }\n      files = files.slice(0, files.length - excess);\n    }\n\n    // Check total upload size\n    const currentTotalSize = uploadedFiles.reduce((sum, file) => sum + file.file_size, 0);\n    const newFilesSize = files.reduce((sum, file) => sum + file.size, 0);\n    const totalSize = currentTotalSize + newFilesSize;\n    const maxTotalSize = settings.max_total_upload_size_mb * 1024 * 1024;\n    if (totalSize > maxTotalSize) {\n      onError === null || onError === void 0 ? void 0 : onError(`Total upload size would exceed ${settings.max_total_upload_size_mb}MB limit`);\n      return {\n        valid: [],\n        invalid: files.map(file => ({\n          file,\n          errors: ['Total size limit exceeded']\n        }))\n      };\n    }\n\n    // Validate each file\n    for (const file of files) {\n      const validation = await printingService.validateFile(file);\n      if (validation.valid) {\n        valid.push(file);\n      } else {\n        invalid.push({\n          file,\n          errors: validation.message ? [validation.message] : ['Validation failed']\n        });\n      }\n    }\n    return {\n      valid,\n      invalid\n    };\n  };\n  const onDrop = useCallback(async acceptedFiles => {\n    if (!orderId) {\n      onError === null || onError === void 0 ? void 0 : onError('Please create an order first before uploading files');\n      return;\n    }\n    const {\n      valid,\n      invalid\n    } = await validateFiles(acceptedFiles);\n\n    // Show errors for invalid files\n    if (invalid.length > 0) {\n      const errorMessages = invalid.map(({\n        file,\n        errors\n      }) => `${file.name}: ${errors.join(', ')}`).join('\\n');\n      onError === null || onError === void 0 ? void 0 : onError(errorMessages);\n    }\n\n    // Upload valid files\n    if (valid.length > 0) {\n      await uploadFiles(valid);\n    }\n  }, [orderId, settings, uploadedFiles]);\n  const uploadFiles = async files => {\n    setLoading(true);\n\n    // Initialize uploading files state\n    const newUploadingFiles = files.map(file => ({\n      file,\n      progress: 0,\n      status: 'uploading'\n    }));\n    setUploadingFiles(newUploadingFiles);\n    try {\n      const uploadedFileResults = await printingService.uploadFiles(orderId, files, selectedFileType);\n\n      // Update uploading files to success\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        progress: 100,\n        status: 'success'\n      })));\n\n      // Add to uploaded files list\n      setUploadedFiles(prev => [...prev, ...uploadedFileResults]);\n      onFilesUploaded === null || onFilesUploaded === void 0 ? void 0 : onFilesUploaded(uploadedFileResults);\n\n      // Clear uploading files after a delay\n      setTimeout(() => {\n        setUploadingFiles([]);\n      }, 2000);\n    } catch (error) {\n      // Update uploading files to error\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        status: 'error',\n        error: error.message || 'Upload failed'\n      })));\n      onError === null || onError === void 0 ? void 0 : onError(error.message || 'Failed to upload files');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteFile = async () => {\n    if (!fileToDelete || !orderId) return;\n    try {\n      await printingService.deleteFile(orderId, fileToDelete.id);\n      setUploadedFiles(prev => prev.filter(f => f.id !== fileToDelete.id));\n      setDeleteDialogOpen(false);\n      setFileToDelete(null);\n    } catch (error) {\n      onError === null || onError === void 0 ? void 0 : onError(error.message || 'Failed to delete file');\n    }\n  };\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: settings ? {\n      'image/*': settings.allowed_file_types.filter(type => ['png', 'jpg', 'jpeg', 'tiff', 'svg'].includes(type)).map(type => `.${type}`),\n      'application/pdf': settings.allowed_file_types.includes('pdf') ? ['.pdf'] : [],\n      'application/postscript': settings.allowed_file_types.includes('eps') ? ['.eps'] : [],\n      'application/illustrator': settings.allowed_file_types.includes('ai') ? ['.ai'] : []\n    } : undefined,\n    maxSize: settings ? settings.max_file_size_mb * 1024 * 1024 : undefined,\n    disabled: loading || !orderId\n  });\n  const getFileIcon = mimeType => {\n    if (mimeType.startsWith('image/')) return /*#__PURE__*/_jsxDEV(ImageIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 47\n    }, this);\n    if (mimeType === 'application/pdf') return /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 48\n    }, this);\n    return /*#__PURE__*/_jsxDEV(FileIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 12\n    }, this);\n  };\n  const getStatusIcon = file => {\n    if (file.dpi && settings !== null && settings !== void 0 && settings.enable_dpi_validation) {\n      if (file.dpi >= settings.min_dpi_requirement) {\n        return /*#__PURE__*/_jsxDEV(CheckIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 16\n        }, this);\n      } else if (file.dpi >= settings.dpi_warning_threshold) {\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 16\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(ErrorIcon, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 16\n        }, this);\n      }\n    }\n    return file.is_approved ? /*#__PURE__*/_jsxDEV(CheckIcon, {\n      color: \"success\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 31\n    }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {\n      color: \"warning\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 63\n    }, this);\n  };\n  const formatFileSize = bytes => {\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let size = bytes;\n    let unitIndex = 0;\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  };\n  if (!settings) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"200px\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Loading upload settings...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Upload Limits:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), \" Max \", settings.max_files_per_order, \" files,\", settings.max_file_size_mb, \"MB per file, \", settings.max_total_upload_size_mb, \"MB total. Allowed types: \", settings.allowed_file_types.join(', ').toUpperCase()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      fullWidth: true,\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n        children: \"File Type\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        value: selectedFileType,\n        onChange: e => setSelectedFileType(e.target.value),\n        label: \"File Type\",\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"artwork\",\n          children: \"Artwork Files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"reference\",\n          children: \"Reference Files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"proof\",\n          children: \"Proof Files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      ...getRootProps(),\n      sx: {\n        p: 4,\n        border: '2px dashed',\n        borderColor: isDragActive ? 'primary.main' : 'grey.300',\n        backgroundColor: isDragActive ? 'action.hover' : 'background.paper',\n        cursor: orderId ? 'pointer' : 'not-allowed',\n        opacity: orderId ? 1 : 0.5,\n        textAlign: 'center',\n        mb: 3,\n        transition: 'all 0.2s ease-in-out'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        ...getInputProps()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UploadIcon, {\n        sx: {\n          fontSize: 48,\n          color: 'text.secondary',\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: isDragActive ? 'Drop files here' : 'Drag & drop files here'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: \"or click to browse files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        disabled: !orderId,\n        children: \"Browse Files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), !orderId && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"error\",\n        sx: {\n          display: 'block',\n          mt: 1\n        },\n        children: \"Please create an order first\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), uploadingFiles.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Uploading Files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this), uploadingFiles.map((uploadingFile, index) => /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 2,\n            children: [getFileIcon(uploadingFile.file.type), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                noWrap: true,\n                children: uploadingFile.file.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: formatFileSize(uploadingFile.file.size)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: 100\n              },\n              children: [uploadingFile.status === 'uploading' && /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"indeterminate\",\n                sx: {\n                  height: 6,\n                  borderRadius: 3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 23\n              }, this), uploadingFile.status === 'success' && /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 31\n                }, this),\n                label: \"Success\",\n                color: \"success\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 23\n              }, this), uploadingFile.status === 'error' && /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(ErrorIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 31\n                }, this),\n                label: \"Error\",\n                color: \"error\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 17\n          }, this), uploadingFile.error && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mt: 1\n            },\n            children: uploadingFile.error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 19\n          }, this), uploadingFile.warnings && uploadingFile.warnings.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mt: 1\n            },\n            children: uploadingFile.warnings.join(', ')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 9\n    }, this), uploadedFiles.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: [\"Uploaded Files (\", uploadedFiles.length, \"/\", settings.max_files_per_order, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: uploadedFiles.map(file => /*#__PURE__*/_jsxDEV(Grid, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                mb: 1,\n                children: [getFileIcon(file.mime_type), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: getStatusIcon(file),\n                  children: getStatusIcon(file)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  noWrap: true,\n                  sx: {\n                    flex: 1\n                  },\n                  children: file.original_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                display: \"block\",\n                children: [\"Size: \", file.formatted_file_size]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                display: \"block\",\n                children: [\"Type: \", file.file_type_label]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 21\n              }, this), file.dimensions && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                display: \"block\",\n                children: [\"Dimensions: \", file.dimensions.width, \"\\xD7\", file.dimensions.height, \"px\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 23\n              }, this), file.dpi && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: file.dpi >= (settings.min_dpi_requirement || 300) ? 'success.main' : file.dpi >= (settings.dpi_warning_threshold || 150) ? 'warning.main' : 'error.main',\n                display: \"block\",\n                children: [\"DPI: \", file.dpi, \" \", file.dpi_status && `(${file.dpi_status})`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 23\n              }, this), file.notes && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                display: \"block\",\n                sx: {\n                  mt: 1\n                },\n                children: [\"Notes: \", file.notes]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                gap: 1,\n                mt: 1,\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: file.is_approved ? 'Approved' : 'Pending Review',\n                  color: file.is_approved ? 'success' : 'warning',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                href: file.file_url,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: \"View\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => {\n                  setFileToDelete(file);\n                  setDeleteDialogOpen(true);\n                },\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 17\n          }, this)\n        }, file.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: () => setDeleteDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Delete File\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Are you sure you want to delete \\\"\", fileToDelete === null || fileToDelete === void 0 ? void 0 : fileToDelete.original_name, \"\\\"? This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDeleteDialogOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteFile,\n          color: \"error\",\n          variant: \"contained\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUpload, \"0J3MDxy5WHrG1dJIKHbn3stw5f4=\", false, function () {\n  return [useDropzone];\n});\n_c = FileUpload;\nexport default FileUpload;\nvar _c;\n$RefreshReg$(_c, \"FileUpload\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Typography", "Paper", "<PERSON><PERSON>", "LinearProgress", "<PERSON><PERSON>", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "Grid2", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON><PERSON>", "CloudUpload", "UploadIcon", "Delete", "DeleteIcon", "Warning", "WarningIcon", "CheckCircle", "CheckIcon", "Error", "ErrorIcon", "Image", "ImageIcon", "PictureAsPdf", "PdfIcon", "Description", "FileIcon", "useDropzone", "printingService", "jsxDEV", "_jsxDEV", "FileUpload", "orderId", "onFilesUploaded", "onError", "_s", "settings", "setSettings", "uploadedFiles", "setUploadedFiles", "uploadingFiles", "setUploadingFiles", "loading", "setLoading", "deleteDialogOpen", "setDeleteDialogOpen", "fileToDelete", "setFileToDelete", "selectedFileType", "setSelectedFileType", "loadSettings", "loadUploadedFiles", "uploadSettings", "getUploadSettings", "error", "console", "files", "getOrderFiles", "validateFiles", "valid", "invalid", "totalFiles", "length", "max_files_per_order", "excess", "i", "push", "file", "errors", "slice", "currentTotalSize", "reduce", "sum", "file_size", "newFilesSize", "size", "totalSize", "maxTotalSize", "max_total_upload_size_mb", "map", "validation", "validateFile", "message", "onDrop", "acceptedFiles", "errorMessages", "name", "join", "uploadFiles", "newUploadingFiles", "progress", "status", "uploadedFileResults", "prev", "uf", "setTimeout", "handleDeleteFile", "deleteFile", "id", "filter", "f", "getRootProps", "getInputProps", "isDragActive", "accept", "allowed_file_types", "type", "includes", "undefined", "maxSize", "max_file_size_mb", "disabled", "getFileIcon", "mimeType", "startsWith", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusIcon", "dpi", "enable_dpi_validation", "min_dpi_requirement", "color", "dpi_warning_threshold", "is_approved", "formatFileSize", "bytes", "units", "unitIndex", "toFixed", "display", "justifyContent", "alignItems", "minHeight", "children", "severity", "sx", "mb", "variant", "toUpperCase", "fullWidth", "value", "onChange", "e", "target", "label", "p", "border", "borderColor", "backgroundColor", "cursor", "opacity", "textAlign", "transition", "fontSize", "gutterBottom", "mt", "uploadingFile", "index", "py", "gap", "flex", "noWrap", "width", "height", "borderRadius", "icon", "warnings", "container", "spacing", "xs", "sm", "md", "mime_type", "title", "original_name", "formatted_file_size", "file_type_label", "dimensions", "dpi_status", "notes", "href", "file_url", "rel", "onClick", "open", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/FileUpload.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  LinearProgress,\n  Alert,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid2 as Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Tooltip,\n} from '@mui/material';\nimport {\n  CloudUpload as UploadIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckIcon,\n  Error as ErrorIcon,\n  Image as ImageIcon,\n  PictureAsPdf as PdfIcon,\n  Description as FileIcon,\n} from '@mui/icons-material';\nimport { useDropzone } from 'react-dropzone';\nimport printingService, { OrderFile } from '../../services/printingService';\n\ninterface FileUploadSettings {\n  allowed_file_types: string[];\n  max_file_size_mb: number;\n  max_total_upload_size_mb: number;\n  max_files_per_order: number;\n  min_dpi_requirement: number;\n  dpi_warning_threshold: number;\n  enable_dpi_validation: boolean;\n  min_width_px: number;\n  min_height_px: number;\n  max_width_px: number;\n  max_height_px: number;\n  enable_dimension_validation: boolean;\n}\n\ninterface FileUploadProps {\n  orderId?: number;\n  onFilesUploaded?: (files: OrderFile[]) => void;\n  onError?: (error: string) => void;\n}\n\ninterface UploadingFile {\n  file: File;\n  progress: number;\n  status: 'uploading' | 'success' | 'error';\n  error?: string;\n  warnings?: string[];\n}\n\nconst FileUpload: React.FC<FileUploadProps> = ({ orderId, onFilesUploaded, onError }) => {\n  const [settings, setSettings] = useState<FileUploadSettings | null>(null);\n  const [uploadedFiles, setUploadedFiles] = useState<OrderFile[]>([]);\n  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [fileToDelete, setFileToDelete] = useState<OrderFile | null>(null);\n  const [selectedFileType, setSelectedFileType] = useState<string>('artwork');\n\n  // Load settings and files on component mount\n  useEffect(() => {\n    loadSettings();\n    if (orderId) {\n      loadUploadedFiles();\n    }\n  }, [orderId]);\n\n  const loadSettings = async () => {\n    try {\n      const uploadSettings = await printingService.getUploadSettings();\n      setSettings(uploadSettings);\n    } catch (error) {\n      console.error('Failed to load upload settings:', error);\n      onError?.('Failed to load upload settings');\n    }\n  };\n\n  const loadUploadedFiles = async () => {\n    if (!orderId) return;\n\n    try {\n      const files = await printingService.getOrderFiles(orderId);\n      setUploadedFiles(files);\n    } catch (error) {\n      console.error('Failed to load uploaded files:', error);\n    }\n  };\n\n  // Validate files before upload\n  const validateFiles = async (files: File[]): Promise<{ valid: File[]; invalid: { file: File; errors: string[] }[] }> => {\n    if (!settings) return { valid: [], invalid: [] };\n\n    const valid: File[] = [];\n    const invalid: { file: File; errors: string[] }[] = [];\n\n    // Check total file count\n    const totalFiles = uploadedFiles.length + files.length;\n    if (totalFiles > settings.max_files_per_order) {\n      const excess = totalFiles - settings.max_files_per_order;\n      for (let i = files.length - excess; i < files.length; i++) {\n        invalid.push({\n          file: files[i],\n          errors: [`Maximum ${settings.max_files_per_order} files allowed per order`]\n        });\n      }\n      files = files.slice(0, files.length - excess);\n    }\n\n    // Check total upload size\n    const currentTotalSize = uploadedFiles.reduce((sum, file) => sum + file.file_size, 0);\n    const newFilesSize = files.reduce((sum, file) => sum + file.size, 0);\n    const totalSize = currentTotalSize + newFilesSize;\n    const maxTotalSize = settings.max_total_upload_size_mb * 1024 * 1024;\n\n    if (totalSize > maxTotalSize) {\n      onError?.(`Total upload size would exceed ${settings.max_total_upload_size_mb}MB limit`);\n      return { valid: [], invalid: files.map(file => ({ file, errors: ['Total size limit exceeded'] })) };\n    }\n\n    // Validate each file\n    for (const file of files) {\n      const validation = await printingService.validateFile(file);\n      if (validation.valid) {\n        valid.push(file);\n      } else {\n        invalid.push({\n          file,\n          errors: validation.message ? [validation.message] : ['Validation failed']\n        });\n      }\n    }\n\n    return { valid, invalid };\n  };\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    if (!orderId) {\n      onError?.('Please create an order first before uploading files');\n      return;\n    }\n\n    const { valid, invalid } = await validateFiles(acceptedFiles);\n\n    // Show errors for invalid files\n    if (invalid.length > 0) {\n      const errorMessages = invalid.map(({ file, errors }) =>\n        `${file.name}: ${errors.join(', ')}`\n      ).join('\\n');\n      onError?.(errorMessages);\n    }\n\n    // Upload valid files\n    if (valid.length > 0) {\n      await uploadFiles(valid);\n    }\n  }, [orderId, settings, uploadedFiles]);\n\n  const uploadFiles = async (files: File[]) => {\n    setLoading(true);\n\n    // Initialize uploading files state\n    const newUploadingFiles: UploadingFile[] = files.map(file => ({\n      file,\n      progress: 0,\n      status: 'uploading'\n    }));\n    setUploadingFiles(newUploadingFiles);\n\n    try {\n      const uploadedFileResults = await printingService.uploadFiles(orderId!, files, selectedFileType);\n\n      // Update uploading files to success\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        progress: 100,\n        status: 'success'\n      })));\n\n      // Add to uploaded files list\n      setUploadedFiles(prev => [...prev, ...uploadedFileResults]);\n      onFilesUploaded?.(uploadedFileResults);\n\n      // Clear uploading files after a delay\n      setTimeout(() => {\n        setUploadingFiles([]);\n      }, 2000);\n\n    } catch (error: any) {\n      // Update uploading files to error\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        status: 'error',\n        error: error.message || 'Upload failed'\n      })));\n\n      onError?.(error.message || 'Failed to upload files');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteFile = async () => {\n    if (!fileToDelete || !orderId) return;\n\n    try {\n      await printingService.deleteFile(orderId, fileToDelete.id);\n      setUploadedFiles(prev => prev.filter(f => f.id !== fileToDelete.id));\n      setDeleteDialogOpen(false);\n      setFileToDelete(null);\n    } catch (error: any) {\n      onError?.(error.message || 'Failed to delete file');\n    }\n  };\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: settings ? {\n      'image/*': settings.allowed_file_types.filter(type =>\n        ['png', 'jpg', 'jpeg', 'tiff', 'svg'].includes(type)\n      ).map(type => `.${type}`),\n      'application/pdf': settings.allowed_file_types.includes('pdf') ? ['.pdf'] : [],\n      'application/postscript': settings.allowed_file_types.includes('eps') ? ['.eps'] : [],\n      'application/illustrator': settings.allowed_file_types.includes('ai') ? ['.ai'] : [],\n    } : undefined,\n    maxSize: settings ? settings.max_file_size_mb * 1024 * 1024 : undefined,\n    disabled: loading || !orderId\n  });\n\n  const getFileIcon = (mimeType: string) => {\n    if (mimeType.startsWith('image/')) return <ImageIcon />;\n    if (mimeType === 'application/pdf') return <PdfIcon />;\n    return <FileIcon />;\n  };\n\n  const getStatusIcon = (file: OrderFile) => {\n    if (file.dpi && settings?.enable_dpi_validation) {\n      if (file.dpi >= settings.min_dpi_requirement) {\n        return <CheckIcon color=\"success\" />;\n      } else if (file.dpi >= settings.dpi_warning_threshold) {\n        return <WarningIcon color=\"warning\" />;\n      } else {\n        return <ErrorIcon color=\"error\" />;\n      }\n    }\n    return file.is_approved ? <CheckIcon color=\"success\" /> : <WarningIcon color=\"warning\" />;\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let size = bytes;\n    let unitIndex = 0;\n\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  };\n\n  if (!settings) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"200px\">\n        <Typography>Loading upload settings...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Upload Settings Info */}\n      <Alert severity=\"info\" sx={{ mb: 2 }}>\n        <Typography variant=\"body2\">\n          <strong>Upload Limits:</strong> Max {settings.max_files_per_order} files,\n          {settings.max_file_size_mb}MB per file, {settings.max_total_upload_size_mb}MB total.\n          Allowed types: {settings.allowed_file_types.join(', ').toUpperCase()}\n        </Typography>\n      </Alert>\n\n      {/* File Type Selection */}\n      <FormControl fullWidth sx={{ mb: 2 }}>\n        <InputLabel>File Type</InputLabel>\n        <Select\n          value={selectedFileType}\n          onChange={(e) => setSelectedFileType(e.target.value)}\n          label=\"File Type\"\n        >\n          <MenuItem value=\"artwork\">Artwork Files</MenuItem>\n          <MenuItem value=\"reference\">Reference Files</MenuItem>\n          <MenuItem value=\"proof\">Proof Files</MenuItem>\n        </Select>\n      </FormControl>\n\n      {/* Drag and Drop Upload Area */}\n      <Paper\n        {...getRootProps()}\n        sx={{\n          p: 4,\n          border: '2px dashed',\n          borderColor: isDragActive ? 'primary.main' : 'grey.300',\n          backgroundColor: isDragActive ? 'action.hover' : 'background.paper',\n          cursor: orderId ? 'pointer' : 'not-allowed',\n          opacity: orderId ? 1 : 0.5,\n          textAlign: 'center',\n          mb: 3,\n          transition: 'all 0.2s ease-in-out',\n        }}\n      >\n        <input {...getInputProps()} />\n        <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />\n        <Typography variant=\"h6\" gutterBottom>\n          {isDragActive ? 'Drop files here' : 'Drag & drop files here'}\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          or click to browse files\n        </Typography>\n        <Button variant=\"outlined\" disabled={!orderId}>\n          Browse Files\n        </Button>\n        {!orderId && (\n          <Typography variant=\"caption\" color=\"error\" sx={{ display: 'block', mt: 1 }}>\n            Please create an order first\n          </Typography>\n        )}\n      </Paper>\n\n      {/* Uploading Files */}\n      {uploadingFiles.length > 0 && (\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Uploading Files\n          </Typography>\n          {uploadingFiles.map((uploadingFile, index) => (\n            <Card key={index} sx={{ mb: 1 }}>\n              <CardContent sx={{ py: 2 }}>\n                <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                  {getFileIcon(uploadingFile.file.type)}\n                  <Box sx={{ flex: 1 }}>\n                    <Typography variant=\"body2\" noWrap>\n                      {uploadingFile.file.name}\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      {formatFileSize(uploadingFile.file.size)}\n                    </Typography>\n                  </Box>\n                  <Box sx={{ width: 100 }}>\n                    {uploadingFile.status === 'uploading' && (\n                      <LinearProgress\n                        variant=\"indeterminate\"\n                        sx={{ height: 6, borderRadius: 3 }}\n                      />\n                    )}\n                    {uploadingFile.status === 'success' && (\n                      <Chip\n                        icon={<CheckIcon />}\n                        label=\"Success\"\n                        color=\"success\"\n                        size=\"small\"\n                      />\n                    )}\n                    {uploadingFile.status === 'error' && (\n                      <Chip\n                        icon={<ErrorIcon />}\n                        label=\"Error\"\n                        color=\"error\"\n                        size=\"small\"\n                      />\n                    )}\n                  </Box>\n                </Box>\n                {uploadingFile.error && (\n                  <Alert severity=\"error\" sx={{ mt: 1 }}>\n                    {uploadingFile.error}\n                  </Alert>\n                )}\n                {uploadingFile.warnings && uploadingFile.warnings.length > 0 && (\n                  <Alert severity=\"warning\" sx={{ mt: 1 }}>\n                    {uploadingFile.warnings.join(', ')}\n                  </Alert>\n                )}\n              </CardContent>\n            </Card>\n          ))}\n        </Box>\n      )}\n\n      {/* Uploaded Files List */}\n      {uploadedFiles.length > 0 && (\n        <Box>\n          <Typography variant=\"h6\" gutterBottom>\n            Uploaded Files ({uploadedFiles.length}/{settings.max_files_per_order})\n          </Typography>\n          <Grid container spacing={2}>\n            {uploadedFiles.map((file) => (\n              <Grid key={file.id} xs={12} sm={6} md={4}>\n                <Card>\n                  <CardContent>\n                    <Box display=\"flex\" alignItems=\"center\" gap={1} mb={1}>\n                      {getFileIcon(file.mime_type)}\n                      <Tooltip title={getStatusIcon(file)}>\n                        {getStatusIcon(file)}\n                      </Tooltip>\n                      <Typography variant=\"body2\" noWrap sx={{ flex: 1 }}>\n                        {file.original_name}\n                      </Typography>\n                    </Box>\n\n                    <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                      Size: {file.formatted_file_size}\n                    </Typography>\n\n                    <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                      Type: {file.file_type_label}\n                    </Typography>\n\n                    {file.dimensions && (\n                      <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                        Dimensions: {file.dimensions.width}×{file.dimensions.height}px\n                      </Typography>\n                    )}\n\n                    {file.dpi && (\n                      <Typography\n                        variant=\"caption\"\n                        color={\n                          file.dpi >= (settings.min_dpi_requirement || 300) ? 'success.main' :\n                          file.dpi >= (settings.dpi_warning_threshold || 150) ? 'warning.main' : 'error.main'\n                        }\n                        display=\"block\"\n                      >\n                        DPI: {file.dpi} {file.dpi_status && `(${file.dpi_status})`}\n                      </Typography>\n                    )}\n\n                    {file.notes && (\n                      <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" sx={{ mt: 1 }}>\n                        Notes: {file.notes}\n                      </Typography>\n                    )}\n\n                    <Box display=\"flex\" gap={1} mt={1}>\n                      <Chip\n                        label={file.is_approved ? 'Approved' : 'Pending Review'}\n                        color={file.is_approved ? 'success' : 'warning'}\n                        size=\"small\"\n                      />\n                    </Box>\n                  </CardContent>\n\n                  <CardActions>\n                    <Button\n                      size=\"small\"\n                      href={file.file_url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                    >\n                      View\n                    </Button>\n                    <IconButton\n                      size=\"small\"\n                      color=\"error\"\n                      onClick={() => {\n                        setFileToDelete(file);\n                        setDeleteDialogOpen(true);\n                      }}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </CardActions>\n                </Card>\n              </Grid>\n            ))}\n          </Grid>\n        </Box>\n      )}\n\n      {/* Delete Confirmation Dialog */}\n      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>\n        <DialogTitle>Delete File</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Are you sure you want to delete \"{fileToDelete?.original_name}\"? This action cannot be undone.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>\n          <Button onClick={handleDeleteFile} color=\"error\" variant=\"contained\">\n            Delete\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default FileUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,cAAc,EACdC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,IAAIC,IAAI,EACbC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,OAAO,QACF,eAAe;AACtB,SACEC,WAAW,IAAIC,UAAU,EACzBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,SAAS,EACxBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,YAAY,IAAIC,OAAO,EACvBC,WAAW,IAAIC,QAAQ,QAClB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,eAAe,MAAqB,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA+B5E,MAAMC,UAAqC,GAAGA,CAAC;EAAEC,OAAO;EAAEC,eAAe;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAA4B,IAAI,CAAC;EACzE,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAc,EAAE,CAAC;EACnE,MAAM,CAACsD,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACwD,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAmB,IAAI,CAAC;EACxE,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAAS,SAAS,CAAC;;EAE3E;EACAC,SAAS,CAAC,MAAM;IACd+D,YAAY,CAAC,CAAC;IACd,IAAIlB,OAAO,EAAE;MACXmB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACnB,OAAO,CAAC,CAAC;EAEb,MAAMkB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAME,cAAc,GAAG,MAAMxB,eAAe,CAACyB,iBAAiB,CAAC,CAAC;MAChEhB,WAAW,CAACe,cAAc,CAAC;IAC7B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDpB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,gCAAgC,CAAC;IAC7C;EACF,CAAC;EAED,MAAMiB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACnB,OAAO,EAAE;IAEd,IAAI;MACF,MAAMwB,KAAK,GAAG,MAAM5B,eAAe,CAAC6B,aAAa,CAACzB,OAAO,CAAC;MAC1DO,gBAAgB,CAACiB,KAAK,CAAC;IACzB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMI,aAAa,GAAG,MAAOF,KAAa,IAA8E;IACtH,IAAI,CAACpB,QAAQ,EAAE,OAAO;MAAEuB,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC;IAEhD,MAAMD,KAAa,GAAG,EAAE;IACxB,MAAMC,OAA2C,GAAG,EAAE;;IAEtD;IACA,MAAMC,UAAU,GAAGvB,aAAa,CAACwB,MAAM,GAAGN,KAAK,CAACM,MAAM;IACtD,IAAID,UAAU,GAAGzB,QAAQ,CAAC2B,mBAAmB,EAAE;MAC7C,MAAMC,MAAM,GAAGH,UAAU,GAAGzB,QAAQ,CAAC2B,mBAAmB;MACxD,KAAK,IAAIE,CAAC,GAAGT,KAAK,CAACM,MAAM,GAAGE,MAAM,EAAEC,CAAC,GAAGT,KAAK,CAACM,MAAM,EAAEG,CAAC,EAAE,EAAE;QACzDL,OAAO,CAACM,IAAI,CAAC;UACXC,IAAI,EAAEX,KAAK,CAACS,CAAC,CAAC;UACdG,MAAM,EAAE,CAAC,WAAWhC,QAAQ,CAAC2B,mBAAmB,0BAA0B;QAC5E,CAAC,CAAC;MACJ;MACAP,KAAK,GAAGA,KAAK,CAACa,KAAK,CAAC,CAAC,EAAEb,KAAK,CAACM,MAAM,GAAGE,MAAM,CAAC;IAC/C;;IAEA;IACA,MAAMM,gBAAgB,GAAGhC,aAAa,CAACiC,MAAM,CAAC,CAACC,GAAG,EAAEL,IAAI,KAAKK,GAAG,GAAGL,IAAI,CAACM,SAAS,EAAE,CAAC,CAAC;IACrF,MAAMC,YAAY,GAAGlB,KAAK,CAACe,MAAM,CAAC,CAACC,GAAG,EAAEL,IAAI,KAAKK,GAAG,GAAGL,IAAI,CAACQ,IAAI,EAAE,CAAC,CAAC;IACpE,MAAMC,SAAS,GAAGN,gBAAgB,GAAGI,YAAY;IACjD,MAAMG,YAAY,GAAGzC,QAAQ,CAAC0C,wBAAwB,GAAG,IAAI,GAAG,IAAI;IAEpE,IAAIF,SAAS,GAAGC,YAAY,EAAE;MAC5B3C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,kCAAkCE,QAAQ,CAAC0C,wBAAwB,UAAU,CAAC;MACxF,OAAO;QAAEnB,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAEJ,KAAK,CAACuB,GAAG,CAACZ,IAAI,KAAK;UAAEA,IAAI;UAAEC,MAAM,EAAE,CAAC,2BAA2B;QAAE,CAAC,CAAC;MAAE,CAAC;IACrG;;IAEA;IACA,KAAK,MAAMD,IAAI,IAAIX,KAAK,EAAE;MACxB,MAAMwB,UAAU,GAAG,MAAMpD,eAAe,CAACqD,YAAY,CAACd,IAAI,CAAC;MAC3D,IAAIa,UAAU,CAACrB,KAAK,EAAE;QACpBA,KAAK,CAACO,IAAI,CAACC,IAAI,CAAC;MAClB,CAAC,MAAM;QACLP,OAAO,CAACM,IAAI,CAAC;UACXC,IAAI;UACJC,MAAM,EAAEY,UAAU,CAACE,OAAO,GAAG,CAACF,UAAU,CAACE,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAC1E,CAAC,CAAC;MACJ;IACF;IAEA,OAAO;MAAEvB,KAAK;MAAEC;IAAQ,CAAC;EAC3B,CAAC;EAED,MAAMuB,MAAM,GAAG/F,WAAW,CAAC,MAAOgG,aAAqB,IAAK;IAC1D,IAAI,CAACpD,OAAO,EAAE;MACZE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,qDAAqD,CAAC;MAChE;IACF;IAEA,MAAM;MAAEyB,KAAK;MAAEC;IAAQ,CAAC,GAAG,MAAMF,aAAa,CAAC0B,aAAa,CAAC;;IAE7D;IACA,IAAIxB,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMuB,aAAa,GAAGzB,OAAO,CAACmB,GAAG,CAAC,CAAC;QAAEZ,IAAI;QAAEC;MAAO,CAAC,KACjD,GAAGD,IAAI,CAACmB,IAAI,KAAKlB,MAAM,CAACmB,IAAI,CAAC,IAAI,CAAC,EACpC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;MACZrD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGmD,aAAa,CAAC;IAC1B;;IAEA;IACA,IAAI1B,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MACpB,MAAM0B,WAAW,CAAC7B,KAAK,CAAC;IAC1B;EACF,CAAC,EAAE,CAAC3B,OAAO,EAAEI,QAAQ,EAAEE,aAAa,CAAC,CAAC;EAEtC,MAAMkD,WAAW,GAAG,MAAOhC,KAAa,IAAK;IAC3Cb,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACA,MAAM8C,iBAAkC,GAAGjC,KAAK,CAACuB,GAAG,CAACZ,IAAI,KAAK;MAC5DA,IAAI;MACJuB,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IACHlD,iBAAiB,CAACgD,iBAAiB,CAAC;IAEpC,IAAI;MACF,MAAMG,mBAAmB,GAAG,MAAMhE,eAAe,CAAC4D,WAAW,CAACxD,OAAO,EAAGwB,KAAK,EAAER,gBAAgB,CAAC;;MAEhG;MACAP,iBAAiB,CAACoD,IAAI,IAAIA,IAAI,CAACd,GAAG,CAACe,EAAE,KAAK;QACxC,GAAGA,EAAE;QACLJ,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC,CAAC;;MAEJ;MACApD,gBAAgB,CAACsD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGD,mBAAmB,CAAC,CAAC;MAC3D3D,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAG2D,mBAAmB,CAAC;;MAEtC;MACAG,UAAU,CAAC,MAAM;QACftD,iBAAiB,CAAC,EAAE,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOa,KAAU,EAAE;MACnB;MACAb,iBAAiB,CAACoD,IAAI,IAAIA,IAAI,CAACd,GAAG,CAACe,EAAE,KAAK;QACxC,GAAGA,EAAE;QACLH,MAAM,EAAE,OAAO;QACfrC,KAAK,EAAEA,KAAK,CAAC4B,OAAO,IAAI;MAC1B,CAAC,CAAC,CAAC,CAAC;MAEJhD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGoB,KAAK,CAAC4B,OAAO,IAAI,wBAAwB,CAAC;IACtD,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAClD,YAAY,IAAI,CAACd,OAAO,EAAE;IAE/B,IAAI;MACF,MAAMJ,eAAe,CAACqE,UAAU,CAACjE,OAAO,EAAEc,YAAY,CAACoD,EAAE,CAAC;MAC1D3D,gBAAgB,CAACsD,IAAI,IAAIA,IAAI,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKpD,YAAY,CAACoD,EAAE,CAAC,CAAC;MACpErD,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOO,KAAU,EAAE;MACnBpB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGoB,KAAK,CAAC4B,OAAO,IAAI,uBAAuB,CAAC;IACrD;EACF,CAAC;EAED,MAAM;IAAEmB,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAG5E,WAAW,CAAC;IAChEwD,MAAM;IACNqB,MAAM,EAAEpE,QAAQ,GAAG;MACjB,SAAS,EAAEA,QAAQ,CAACqE,kBAAkB,CAACN,MAAM,CAACO,IAAI,IAChD,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,IAAI,CACrD,CAAC,CAAC3B,GAAG,CAAC2B,IAAI,IAAI,IAAIA,IAAI,EAAE,CAAC;MACzB,iBAAiB,EAAEtE,QAAQ,CAACqE,kBAAkB,CAACE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE;MAC9E,wBAAwB,EAAEvE,QAAQ,CAACqE,kBAAkB,CAACE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE;MACrF,yBAAyB,EAAEvE,QAAQ,CAACqE,kBAAkB,CAACE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG;IACpF,CAAC,GAAGC,SAAS;IACbC,OAAO,EAAEzE,QAAQ,GAAGA,QAAQ,CAAC0E,gBAAgB,GAAG,IAAI,GAAG,IAAI,GAAGF,SAAS;IACvEG,QAAQ,EAAErE,OAAO,IAAI,CAACV;EACxB,CAAC,CAAC;EAEF,MAAMgF,WAAW,GAAIC,QAAgB,IAAK;IACxC,IAAIA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE,oBAAOpF,OAAA,CAACR,SAAS;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvD,IAAIL,QAAQ,KAAK,iBAAiB,EAAE,oBAAOnF,OAAA,CAACN,OAAO;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtD,oBAAOxF,OAAA,CAACJ,QAAQ;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrB,CAAC;EAED,MAAMC,aAAa,GAAIpD,IAAe,IAAK;IACzC,IAAIA,IAAI,CAACqD,GAAG,IAAIpF,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEqF,qBAAqB,EAAE;MAC/C,IAAItD,IAAI,CAACqD,GAAG,IAAIpF,QAAQ,CAACsF,mBAAmB,EAAE;QAC5C,oBAAO5F,OAAA,CAACZ,SAAS;UAACyG,KAAK,EAAC;QAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtC,CAAC,MAAM,IAAInD,IAAI,CAACqD,GAAG,IAAIpF,QAAQ,CAACwF,qBAAqB,EAAE;QACrD,oBAAO9F,OAAA,CAACd,WAAW;UAAC2G,KAAK,EAAC;QAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxC,CAAC,MAAM;QACL,oBAAOxF,OAAA,CAACV,SAAS;UAACuG,KAAK,EAAC;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpC;IACF;IACA,OAAOnD,IAAI,CAAC0D,WAAW,gBAAG/F,OAAA,CAACZ,SAAS;MAACyG,KAAK,EAAC;IAAS;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGxF,OAAA,CAACd,WAAW;MAAC2G,KAAK,EAAC;IAAS;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3F,CAAC;EAED,MAAMQ,cAAc,GAAIC,KAAa,IAAa;IAChD,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,IAAIrD,IAAI,GAAGoD,KAAK;IAChB,IAAIE,SAAS,GAAG,CAAC;IAEjB,OAAOtD,IAAI,IAAI,IAAI,IAAIsD,SAAS,GAAGD,KAAK,CAAClE,MAAM,GAAG,CAAC,EAAE;MACnDa,IAAI,IAAI,IAAI;MACZsD,SAAS,EAAE;IACb;IAEA,OAAO,GAAGtD,IAAI,CAACuD,OAAO,CAAC,CAAC,CAAC,IAAIF,KAAK,CAACC,SAAS,CAAC,EAAE;EACjD,CAAC;EAED,IAAI,CAAC7F,QAAQ,EAAE;IACb,oBACEN,OAAA,CAACzC,GAAG;MAAC8I,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/EzG,OAAA,CAACxC,UAAU;QAAAiJ,QAAA,EAAC;MAA0B;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACExF,OAAA,CAACzC,GAAG;IAAAkJ,QAAA,gBAEFzG,OAAA,CAACpC,KAAK;MAAC8I,QAAQ,EAAC,MAAM;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eACnCzG,OAAA,CAACxC,UAAU;QAACqJ,OAAO,EAAC,OAAO;QAAAJ,QAAA,gBACzBzG,OAAA;UAAAyG,QAAA,EAAQ;QAAc;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,SAAK,EAAClF,QAAQ,CAAC2B,mBAAmB,EAAC,SAClE,EAAC3B,QAAQ,CAAC0E,gBAAgB,EAAC,eAAa,EAAC1E,QAAQ,CAAC0C,wBAAwB,EAAC,2BAC5D,EAAC1C,QAAQ,CAACqE,kBAAkB,CAAClB,IAAI,CAAC,IAAI,CAAC,CAACqD,WAAW,CAAC,CAAC;MAAA;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGRxF,OAAA,CAAC7B,WAAW;MAAC4I,SAAS;MAACJ,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACnCzG,OAAA,CAAC5B,UAAU;QAAAqI,QAAA,EAAC;MAAS;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAClCxF,OAAA,CAAC3B,MAAM;QACL2I,KAAK,EAAE9F,gBAAiB;QACxB+F,QAAQ,EAAGC,CAAC,IAAK/F,mBAAmB,CAAC+F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QACrDI,KAAK,EAAC,WAAW;QAAAX,QAAA,gBAEjBzG,OAAA,CAAC1B,QAAQ;UAAC0I,KAAK,EAAC,SAAS;UAAAP,QAAA,EAAC;QAAa;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAClDxF,OAAA,CAAC1B,QAAQ;UAAC0I,KAAK,EAAC,WAAW;UAAAP,QAAA,EAAC;QAAe;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACtDxF,OAAA,CAAC1B,QAAQ;UAAC0I,KAAK,EAAC,OAAO;UAAAP,QAAA,EAAC;QAAW;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGdxF,OAAA,CAACvC,KAAK;MAAA,GACA8G,YAAY,CAAC,CAAC;MAClBoC,EAAE,EAAE;QACFU,CAAC,EAAE,CAAC;QACJC,MAAM,EAAE,YAAY;QACpBC,WAAW,EAAE9C,YAAY,GAAG,cAAc,GAAG,UAAU;QACvD+C,eAAe,EAAE/C,YAAY,GAAG,cAAc,GAAG,kBAAkB;QACnEgD,MAAM,EAAEvH,OAAO,GAAG,SAAS,GAAG,aAAa;QAC3CwH,OAAO,EAAExH,OAAO,GAAG,CAAC,GAAG,GAAG;QAC1ByH,SAAS,EAAE,QAAQ;QACnBf,EAAE,EAAE,CAAC;QACLgB,UAAU,EAAE;MACd,CAAE;MAAAnB,QAAA,gBAEFzG,OAAA;QAAA,GAAWwE,aAAa,CAAC;MAAC;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC9BxF,OAAA,CAAClB,UAAU;QAAC6H,EAAE,EAAE;UAAEkB,QAAQ,EAAE,EAAE;UAAEhC,KAAK,EAAE,gBAAgB;UAAEe,EAAE,EAAE;QAAE;MAAE;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpExF,OAAA,CAACxC,UAAU;QAACqJ,OAAO,EAAC,IAAI;QAACiB,YAAY;QAAArB,QAAA,EAClChC,YAAY,GAAG,iBAAiB,GAAG;MAAwB;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACbxF,OAAA,CAACxC,UAAU;QAACqJ,OAAO,EAAC,OAAO;QAAChB,KAAK,EAAC,gBAAgB;QAACc,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAElE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxF,OAAA,CAACtC,MAAM;QAACmJ,OAAO,EAAC,UAAU;QAAC5B,QAAQ,EAAE,CAAC/E,OAAQ;QAAAuG,QAAA,EAAC;MAE/C;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR,CAACtF,OAAO,iBACPF,OAAA,CAACxC,UAAU;QAACqJ,OAAO,EAAC,SAAS;QAAChB,KAAK,EAAC,OAAO;QAACc,EAAE,EAAE;UAAEN,OAAO,EAAE,OAAO;UAAE0B,EAAE,EAAE;QAAE,CAAE;QAAAtB,QAAA,EAAC;MAE7E;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGP9E,cAAc,CAACsB,MAAM,GAAG,CAAC,iBACxBhC,OAAA,CAACzC,GAAG;MAACoJ,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACjBzG,OAAA,CAACxC,UAAU;QAACqJ,OAAO,EAAC,IAAI;QAACiB,YAAY;QAAArB,QAAA,EAAC;MAEtC;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZ9E,cAAc,CAACuC,GAAG,CAAC,CAAC+E,aAAa,EAAEC,KAAK,kBACvCjI,OAAA,CAACvB,IAAI;QAAakI,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,eAC9BzG,OAAA,CAACtB,WAAW;UAACiI,EAAE,EAAE;YAAEuB,EAAE,EAAE;UAAE,CAAE;UAAAzB,QAAA,gBACzBzG,OAAA,CAACzC,GAAG;YAAC8I,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAAC4B,GAAG,EAAE,CAAE;YAAA1B,QAAA,GAC5CvB,WAAW,CAAC8C,aAAa,CAAC3F,IAAI,CAACuC,IAAI,CAAC,eACrC5E,OAAA,CAACzC,GAAG;cAACoJ,EAAE,EAAE;gBAAEyB,IAAI,EAAE;cAAE,CAAE;cAAA3B,QAAA,gBACnBzG,OAAA,CAACxC,UAAU;gBAACqJ,OAAO,EAAC,OAAO;gBAACwB,MAAM;gBAAA5B,QAAA,EAC/BuB,aAAa,CAAC3F,IAAI,CAACmB;cAAI;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACbxF,OAAA,CAACxC,UAAU;gBAACqJ,OAAO,EAAC,SAAS;gBAAChB,KAAK,EAAC,gBAAgB;gBAAAY,QAAA,EACjDT,cAAc,CAACgC,aAAa,CAAC3F,IAAI,CAACQ,IAAI;cAAC;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxF,OAAA,CAACzC,GAAG;cAACoJ,EAAE,EAAE;gBAAE2B,KAAK,EAAE;cAAI,CAAE;cAAA7B,QAAA,GACrBuB,aAAa,CAACnE,MAAM,KAAK,WAAW,iBACnC7D,OAAA,CAACrC,cAAc;gBACbkJ,OAAO,EAAC,eAAe;gBACvBF,EAAE,EAAE;kBAAE4B,MAAM,EAAE,CAAC;kBAAEC,YAAY,EAAE;gBAAE;cAAE;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CACF,EACAwC,aAAa,CAACnE,MAAM,KAAK,SAAS,iBACjC7D,OAAA,CAACnC,IAAI;gBACH4K,IAAI,eAAEzI,OAAA,CAACZ,SAAS;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpB4B,KAAK,EAAC,SAAS;gBACfvB,KAAK,EAAC,SAAS;gBACfhD,IAAI,EAAC;cAAO;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACF,EACAwC,aAAa,CAACnE,MAAM,KAAK,OAAO,iBAC/B7D,OAAA,CAACnC,IAAI;gBACH4K,IAAI,eAAEzI,OAAA,CAACV,SAAS;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpB4B,KAAK,EAAC,OAAO;gBACbvB,KAAK,EAAC,OAAO;gBACbhD,IAAI,EAAC;cAAO;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLwC,aAAa,CAACxG,KAAK,iBAClBxB,OAAA,CAACpC,KAAK;YAAC8I,QAAQ,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YAAAtB,QAAA,EACnCuB,aAAa,CAACxG;UAAK;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACR,EACAwC,aAAa,CAACU,QAAQ,IAAIV,aAAa,CAACU,QAAQ,CAAC1G,MAAM,GAAG,CAAC,iBAC1DhC,OAAA,CAACpC,KAAK;YAAC8I,QAAQ,EAAC,SAAS;YAACC,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YAAAtB,QAAA,EACrCuB,aAAa,CAACU,QAAQ,CAACjF,IAAI,CAAC,IAAI;UAAC;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC,GA/CLyC,KAAK;QAAA5C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgDV,CACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAhF,aAAa,CAACwB,MAAM,GAAG,CAAC,iBACvBhC,OAAA,CAACzC,GAAG;MAAAkJ,QAAA,gBACFzG,OAAA,CAACxC,UAAU;QAACqJ,OAAO,EAAC,IAAI;QAACiB,YAAY;QAAArB,QAAA,GAAC,kBACpB,EAACjG,aAAa,CAACwB,MAAM,EAAC,GAAC,EAAC1B,QAAQ,CAAC2B,mBAAmB,EAAC,GACvE;MAAA;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxF,OAAA,CAACxB,IAAI;QAACmK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAnC,QAAA,EACxBjG,aAAa,CAACyC,GAAG,CAAEZ,IAAI,iBACtBrC,OAAA,CAACxB,IAAI;UAAeqK,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAtC,QAAA,eACvCzG,OAAA,CAACvB,IAAI;YAAAgI,QAAA,gBACHzG,OAAA,CAACtB,WAAW;cAAA+H,QAAA,gBACVzG,OAAA,CAACzC,GAAG;gBAAC8I,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAAC4B,GAAG,EAAE,CAAE;gBAACvB,EAAE,EAAE,CAAE;gBAAAH,QAAA,GACnDvB,WAAW,CAAC7C,IAAI,CAAC2G,SAAS,CAAC,eAC5BhJ,OAAA,CAACpB,OAAO;kBAACqK,KAAK,EAAExD,aAAa,CAACpD,IAAI,CAAE;kBAAAoE,QAAA,EACjChB,aAAa,CAACpD,IAAI;gBAAC;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACVxF,OAAA,CAACxC,UAAU;kBAACqJ,OAAO,EAAC,OAAO;kBAACwB,MAAM;kBAAC1B,EAAE,EAAE;oBAAEyB,IAAI,EAAE;kBAAE,CAAE;kBAAA3B,QAAA,EAChDpE,IAAI,CAAC6G;gBAAa;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENxF,OAAA,CAACxC,UAAU;gBAACqJ,OAAO,EAAC,SAAS;gBAAChB,KAAK,EAAC,gBAAgB;gBAACQ,OAAO,EAAC,OAAO;gBAAAI,QAAA,GAAC,QAC7D,EAACpE,IAAI,CAAC8G,mBAAmB;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eAEbxF,OAAA,CAACxC,UAAU;gBAACqJ,OAAO,EAAC,SAAS;gBAAChB,KAAK,EAAC,gBAAgB;gBAACQ,OAAO,EAAC,OAAO;gBAAAI,QAAA,GAAC,QAC7D,EAACpE,IAAI,CAAC+G,eAAe;cAAA;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,EAEZnD,IAAI,CAACgH,UAAU,iBACdrJ,OAAA,CAACxC,UAAU;gBAACqJ,OAAO,EAAC,SAAS;gBAAChB,KAAK,EAAC,gBAAgB;gBAACQ,OAAO,EAAC,OAAO;gBAAAI,QAAA,GAAC,cACvD,EAACpE,IAAI,CAACgH,UAAU,CAACf,KAAK,EAAC,MAAC,EAACjG,IAAI,CAACgH,UAAU,CAACd,MAAM,EAAC,IAC9D;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb,EAEAnD,IAAI,CAACqD,GAAG,iBACP1F,OAAA,CAACxC,UAAU;gBACTqJ,OAAO,EAAC,SAAS;gBACjBhB,KAAK,EACHxD,IAAI,CAACqD,GAAG,KAAKpF,QAAQ,CAACsF,mBAAmB,IAAI,GAAG,CAAC,GAAG,cAAc,GAClEvD,IAAI,CAACqD,GAAG,KAAKpF,QAAQ,CAACwF,qBAAqB,IAAI,GAAG,CAAC,GAAG,cAAc,GAAG,YACxE;gBACDO,OAAO,EAAC,OAAO;gBAAAI,QAAA,GAChB,OACM,EAACpE,IAAI,CAACqD,GAAG,EAAC,GAAC,EAACrD,IAAI,CAACiH,UAAU,IAAI,IAAIjH,IAAI,CAACiH,UAAU,GAAG;cAAA;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CACb,EAEAnD,IAAI,CAACkH,KAAK,iBACTvJ,OAAA,CAACxC,UAAU;gBAACqJ,OAAO,EAAC,SAAS;gBAAChB,KAAK,EAAC,gBAAgB;gBAACQ,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,GAAC,SAC3E,EAACpE,IAAI,CAACkH,KAAK;cAAA;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACb,eAEDxF,OAAA,CAACzC,GAAG;gBAAC8I,OAAO,EAAC,MAAM;gBAAC8B,GAAG,EAAE,CAAE;gBAACJ,EAAE,EAAE,CAAE;gBAAAtB,QAAA,eAChCzG,OAAA,CAACnC,IAAI;kBACHuJ,KAAK,EAAE/E,IAAI,CAAC0D,WAAW,GAAG,UAAU,GAAG,gBAAiB;kBACxDF,KAAK,EAAExD,IAAI,CAAC0D,WAAW,GAAG,SAAS,GAAG,SAAU;kBAChDlD,IAAI,EAAC;gBAAO;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAEdxF,OAAA,CAACrB,WAAW;cAAA8H,QAAA,gBACVzG,OAAA,CAACtC,MAAM;gBACLmF,IAAI,EAAC,OAAO;gBACZ2G,IAAI,EAAEnH,IAAI,CAACoH,QAAS;gBACpBtC,MAAM,EAAC,QAAQ;gBACfuC,GAAG,EAAC,qBAAqB;gBAAAjD,QAAA,EAC1B;cAED;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxF,OAAA,CAAClC,UAAU;gBACT+E,IAAI,EAAC,OAAO;gBACZgD,KAAK,EAAC,OAAO;gBACb8D,OAAO,EAAEA,CAAA,KAAM;kBACb1I,eAAe,CAACoB,IAAI,CAAC;kBACrBtB,mBAAmB,CAAC,IAAI,CAAC;gBAC3B,CAAE;gBAAA0F,QAAA,eAEFzG,OAAA,CAAChB,UAAU;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GA3EEnD,IAAI,CAAC+B,EAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4EZ,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,eAGDxF,OAAA,CAACjC,MAAM;MAAC6L,IAAI,EAAE9I,gBAAiB;MAAC+I,OAAO,EAAEA,CAAA,KAAM9I,mBAAmB,CAAC,KAAK,CAAE;MAAA0F,QAAA,gBACxEzG,OAAA,CAAChC,WAAW;QAAAyI,QAAA,EAAC;MAAW;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACtCxF,OAAA,CAAC/B,aAAa;QAAAwI,QAAA,eACZzG,OAAA,CAACxC,UAAU;UAAAiJ,QAAA,GAAC,oCACuB,EAACzF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkI,aAAa,EAAC,mCAChE;QAAA;UAAA7D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBxF,OAAA,CAAC9B,aAAa;QAAAuI,QAAA,gBACZzG,OAAA,CAACtC,MAAM;UAACiM,OAAO,EAAEA,CAAA,KAAM5I,mBAAmB,CAAC,KAAK,CAAE;UAAA0F,QAAA,EAAC;QAAM;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClExF,OAAA,CAACtC,MAAM;UAACiM,OAAO,EAAEzF,gBAAiB;UAAC2B,KAAK,EAAC,OAAO;UAACgB,OAAO,EAAC,WAAW;UAAAJ,QAAA,EAAC;QAErE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnF,EAAA,CA3bIJ,UAAqC;EAAA,QAoKaJ,WAAW;AAAA;AAAAiK,EAAA,GApK7D7J,UAAqC;AA6b3C,eAAeA,UAAU;AAAC,IAAA6J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}