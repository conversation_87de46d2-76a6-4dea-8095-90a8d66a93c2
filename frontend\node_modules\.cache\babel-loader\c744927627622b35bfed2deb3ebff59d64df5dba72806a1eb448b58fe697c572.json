{"ast": null, "code": "import api, { endpoints } from './api';\nclass CmsService {\n  // Get published pages\n  async getPages(filters = {}) {\n    const params = new URLSearchParams();\n    if (filters.featured !== undefined) {\n      params.append('featured', filters.featured.toString());\n    }\n    if (filters.search) {\n      params.append('search', filters.search);\n    }\n    if (filters.per_page) {\n      params.append('per_page', filters.per_page.toString());\n    }\n    if (filters.page) {\n      params.append('page', filters.page.toString());\n    }\n    const response = await api.get(`${endpoints.pages}?${params.toString()}`);\n    return response.data;\n  }\n\n  // Get single page by slug\n  async getPage(slug) {\n    const response = await api.get(endpoints.page(slug));\n    return response.data.page;\n  }\n\n  // Get featured pages\n  async getFeaturedPages() {\n    const response = await this.getPages({\n      featured: true,\n      per_page: 6\n    });\n    return response.pages;\n  }\n\n  // Search pages\n  async searchPages(query, page = 1) {\n    return this.getPages({\n      search: query,\n      page\n    });\n  }\n}\nconst cmsService = new CmsService();\nexport default cmsService;", "map": {"version": 3, "names": ["api", "endpoints", "CmsService", "getPages", "filters", "params", "URLSearchParams", "featured", "undefined", "append", "toString", "search", "per_page", "page", "response", "get", "pages", "data", "getPage", "slug", "getFeaturedPages", "searchPages", "query", "cmsService"], "sources": ["C:/laragon/www/frontend/src/services/cmsService.ts"], "sourcesContent": ["import api, { endpoints } from './api';\n\nexport interface CmsPage {\n  id: number;\n  title: string;\n  slug: string;\n  content: string;\n  meta_description?: string;\n  meta_keywords?: string;\n  is_published: boolean;\n  is_featured: boolean;\n  featured_image?: string;\n  created_by: number;\n  updated_by?: number;\n  published_at?: string;\n  created_at: string;\n  updated_at: string;\n  creator?: {\n    id: number;\n    name: string;\n  };\n  updater?: {\n    id: number;\n    name: string;\n  };\n}\n\nexport interface PaginatedPages {\n  pages: CmsPage[];\n  pagination: {\n    current_page: number;\n    last_page: number;\n    per_page: number;\n    total: number;\n    has_more_pages: boolean;\n  };\n}\n\nexport interface PageFilters {\n  featured?: boolean;\n  search?: string;\n  per_page?: number;\n  page?: number;\n}\n\nclass CmsService {\n  // Get published pages\n  async getPages(filters: PageFilters = {}): Promise<PaginatedPages> {\n    const params = new URLSearchParams();\n\n    if (filters.featured !== undefined) {\n      params.append('featured', filters.featured.toString());\n    }\n    if (filters.search) {\n      params.append('search', filters.search);\n    }\n    if (filters.per_page) {\n      params.append('per_page', filters.per_page.toString());\n    }\n    if (filters.page) {\n      params.append('page', filters.page.toString());\n    }\n\n    const response = await api.get(`${endpoints.pages}?${params.toString()}`);\n    return response.data;\n  }\n\n  // Get single page by slug\n  async getPage(slug: string): Promise<CmsPage> {\n    const response = await api.get(endpoints.page(slug));\n    return response.data.page;\n  }\n\n  // Get featured pages\n  async getFeaturedPages(): Promise<CmsPage[]> {\n    const response = await this.getPages({ featured: true, per_page: 6 });\n    return response.pages;\n  }\n\n  // Search pages\n  async searchPages(query: string, page: number = 1): Promise<PaginatedPages> {\n    return this.getPages({ search: query, page });\n  }\n}\n\nconst cmsService = new CmsService();\nexport default cmsService;\n"], "mappings": "AAAA,OAAOA,GAAG,IAAIC,SAAS,QAAQ,OAAO;AA6CtC,MAAMC,UAAU,CAAC;EACf;EACA,MAAMC,QAAQA,CAACC,OAAoB,GAAG,CAAC,CAAC,EAA2B;IACjE,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpC,IAAIF,OAAO,CAACG,QAAQ,KAAKC,SAAS,EAAE;MAClCH,MAAM,CAACI,MAAM,CAAC,UAAU,EAAEL,OAAO,CAACG,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAAC;IACxD;IACA,IAAIN,OAAO,CAACO,MAAM,EAAE;MAClBN,MAAM,CAACI,MAAM,CAAC,QAAQ,EAAEL,OAAO,CAACO,MAAM,CAAC;IACzC;IACA,IAAIP,OAAO,CAACQ,QAAQ,EAAE;MACpBP,MAAM,CAACI,MAAM,CAAC,UAAU,EAAEL,OAAO,CAACQ,QAAQ,CAACF,QAAQ,CAAC,CAAC,CAAC;IACxD;IACA,IAAIN,OAAO,CAACS,IAAI,EAAE;MAChBR,MAAM,CAACI,MAAM,CAAC,MAAM,EAAEL,OAAO,CAACS,IAAI,CAACH,QAAQ,CAAC,CAAC,CAAC;IAChD;IAEA,MAAMI,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAAC,GAAGd,SAAS,CAACe,KAAK,IAAIX,MAAM,CAACK,QAAQ,CAAC,CAAC,EAAE,CAAC;IACzE,OAAOI,QAAQ,CAACG,IAAI;EACtB;;EAEA;EACA,MAAMC,OAAOA,CAACC,IAAY,EAAoB;IAC5C,MAAML,QAAQ,GAAG,MAAMd,GAAG,CAACe,GAAG,CAACd,SAAS,CAACY,IAAI,CAACM,IAAI,CAAC,CAAC;IACpD,OAAOL,QAAQ,CAACG,IAAI,CAACJ,IAAI;EAC3B;;EAEA;EACA,MAAMO,gBAAgBA,CAAA,EAAuB;IAC3C,MAAMN,QAAQ,GAAG,MAAM,IAAI,CAACX,QAAQ,CAAC;MAAEI,QAAQ,EAAE,IAAI;MAAEK,QAAQ,EAAE;IAAE,CAAC,CAAC;IACrE,OAAOE,QAAQ,CAACE,KAAK;EACvB;;EAEA;EACA,MAAMK,WAAWA,CAACC,KAAa,EAAET,IAAY,GAAG,CAAC,EAA2B;IAC1E,OAAO,IAAI,CAACV,QAAQ,CAAC;MAAEQ,MAAM,EAAEW,KAAK;MAAET;IAAK,CAAC,CAAC;EAC/C;AACF;AAEA,MAAMU,UAAU,GAAG,IAAIrB,UAAU,CAAC,CAAC;AACnC,eAAeqB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}