{"ast": null, "code": "export { default } from \"./CardActionArea.js\";\nexport { default as cardActionAreaClasses } from \"./cardActionAreaClasses.js\";\nexport * from \"./cardActionAreaClasses.js\";", "map": {"version": 3, "names": ["default", "cardActionAreaClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/CardActionArea/index.js"], "sourcesContent": ["export { default } from \"./CardActionArea.js\";\nexport { default as cardActionAreaClasses } from \"./cardActionAreaClasses.js\";\nexport * from \"./cardActionAreaClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB;AAC7C,SAASA,OAAO,IAAIC,qBAAqB,QAAQ,4BAA4B;AAC7E,cAAc,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}