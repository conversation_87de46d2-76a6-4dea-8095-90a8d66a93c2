{"ast": null, "code": "import React,{useState}from'react';import{<PERSON>,Typography,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Avatar,Chip,IconButton,Button,TextField,InputAdornment}from'@mui/material';import{Edit,Delete,Search,Add}from'@mui/icons-material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Users=()=>{const[searchTerm,setSearchTerm]=useState('');// Mock data - replace with actual API call\nconst users=[{id:1,name:'<PERSON>',email:'<EMAIL>',role:'Admin',status:'active'},{id:2,name:'<PERSON>',email:'<EMAIL>',role:'User',status:'active'},{id:3,name:'<PERSON>',email:'<EMAIL>',role:'Moderator',status:'inactive'},{id:4,name:'<PERSON>',email:'<EMAIL>',role:'User',status:'active'}];const filteredUsers=users.filter(user=>user.name.toLowerCase().includes(searchTerm.toLowerCase())||user.email.toLowerCase().includes(searchTerm.toLowerCase()));const getStatusColor=status=>{return status==='active'?'success':'default';};const getRoleColor=role=>{switch(role){case'Admin':return'error';case'Moderator':return'warning';default:return'primary';}};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",children:\"Users Management\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Add,{}),onClick:()=>{// Handle add user\nconsole.log('Add user clicked');},children:\"Add User\"})]}),/*#__PURE__*/_jsx(Box,{sx:{mb:3},children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,variant:\"outlined\",placeholder:\"Search users...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(Search,{})})}})}),/*#__PURE__*/_jsx(TableContainer,{component:Paper,children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"User\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Email\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Role\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Status\"}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:filteredUsers.map(user=>/*#__PURE__*/_jsxs(TableRow,{hover:true,children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Avatar,{sx:{mr:2},children:user.name.charAt(0)}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:user.name})]})}),/*#__PURE__*/_jsx(TableCell,{children:user.email}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:user.role,color:getRoleColor(user.role),size:\"small\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:user.status,color:getStatusColor(user.status),size:\"small\"})}),/*#__PURE__*/_jsxs(TableCell,{align:\"right\",children:[/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>{// Handle edit user\nconsole.log('Edit user:',user.id);},children:/*#__PURE__*/_jsx(Edit,{})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>{// Handle delete user\nconsole.log('Delete user:',user.id);},children:/*#__PURE__*/_jsx(Delete,{})})]})]},user.id))})]})}),filteredUsers.length===0&&/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"textSecondary\",children:\"No users found matching your search criteria.\"})})]});};export default Users;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Avatar", "Chip", "IconButton", "<PERSON><PERSON>", "TextField", "InputAdornment", "Edit", "Delete", "Search", "Add", "jsx", "_jsx", "jsxs", "_jsxs", "Users", "searchTerm", "setSearchTerm", "users", "id", "name", "email", "role", "status", "filteredUsers", "filter", "user", "toLowerCase", "includes", "getStatusColor", "getRoleColor", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "component", "startIcon", "onClick", "console", "log", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "position", "align", "map", "hover", "mr", "char<PERSON>t", "label", "color", "size", "length", "textAlign", "py"], "sources": ["C:/laragon/www/frontend/src/pages/dashboard/Users.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>,\n  Typography,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Avatar,\n  Chip,\n  IconButton,\n  Button,\n  TextField,\n  InputAdornment,\n} from '@mui/material';\nimport {\n  Edit,\n  Delete,\n  Search,\n  Add,\n} from '@mui/icons-material';\n\ninterface User {\n  id: number;\n  name: string;\n  email: string;\n  role: string;\n  status: 'active' | 'inactive';\n  avatar?: string;\n}\n\nconst Users: React.FC = () => {\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Mock data - replace with actual API call\n  const users: User[] = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      role: 'Admin',\n      status: 'active',\n    },\n    {\n      id: 2,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      role: 'User',\n      status: 'active',\n    },\n    {\n      id: 3,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      role: 'Moderator',\n      status: 'inactive',\n    },\n    {\n      id: 4,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      role: 'User',\n      status: 'active',\n    },\n  ];\n\n  const filteredUsers = users.filter(\n    (user) =>\n      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      user.email.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const getStatusColor = (status: string) => {\n    return status === 'active' ? 'success' : 'default';\n  };\n\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'Admin':\n        return 'error';\n      case 'Moderator':\n        return 'warning';\n      default:\n        return 'primary';\n    }\n  };\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" component=\"h1\">\n          Users Management\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<Add />}\n          onClick={() => {\n            // Handle add user\n            console.log('Add user clicked');\n          }}\n        >\n          Add User\n        </Button>\n      </Box>\n\n      <Box sx={{ mb: 3 }}>\n        <TextField\n          fullWidth\n          variant=\"outlined\"\n          placeholder=\"Search users...\"\n          value={searchTerm}\n          onChange={(e) => setSearchTerm(e.target.value)}\n          InputProps={{\n            startAdornment: (\n              <InputAdornment position=\"start\">\n                <Search />\n              </InputAdornment>\n            ),\n          }}\n        />\n      </Box>\n\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>User</TableCell>\n              <TableCell>Email</TableCell>\n              <TableCell>Role</TableCell>\n              <TableCell>Status</TableCell>\n              <TableCell align=\"right\">Actions</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {filteredUsers.map((user) => (\n              <TableRow key={user.id} hover>\n                <TableCell>\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <Avatar sx={{ mr: 2 }}>\n                      {user.name.charAt(0)}\n                    </Avatar>\n                    <Typography variant=\"body1\">{user.name}</Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>{user.email}</TableCell>\n                <TableCell>\n                  <Chip\n                    label={user.role}\n                    color={getRoleColor(user.role) as any}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={user.status}\n                    color={getStatusColor(user.status) as any}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell align=\"right\">\n                  <IconButton\n                    size=\"small\"\n                    onClick={() => {\n                      // Handle edit user\n                      console.log('Edit user:', user.id);\n                    }}\n                  >\n                    <Edit />\n                  </IconButton>\n                  <IconButton\n                    size=\"small\"\n                    onClick={() => {\n                      // Handle delete user\n                      console.log('Delete user:', user.id);\n                    }}\n                  >\n                    <Delete />\n                  </IconButton>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {filteredUsers.length === 0 && (\n        <Box sx={{ textAlign: 'center', py: 4 }}>\n          <Typography variant=\"body1\" color=\"textSecondary\">\n            No users found matching your search criteria.\n          </Typography>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default Users;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,MAAM,CACNC,IAAI,CACJC,UAAU,CACVC,MAAM,CACNC,SAAS,CACTC,cAAc,KACT,eAAe,CACtB,OACEC,IAAI,CACJC,MAAM,CACNC,MAAM,CACNC,GAAG,KACE,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAW7B,KAAM,CAAAC,KAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAEhD;AACA,KAAM,CAAA2B,KAAa,CAAG,CACpB,CACEC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,sBAAsB,CAC7BC,IAAI,CAAE,OAAO,CACbC,MAAM,CAAE,QACV,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,wBAAwB,CAC/BC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QACV,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,aAAa,CACnBC,KAAK,CAAE,yBAAyB,CAChCC,IAAI,CAAE,WAAW,CACjBC,MAAM,CAAE,UACV,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,aAAa,CACnBC,KAAK,CAAE,yBAAyB,CAChCC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QACV,CAAC,CACF,CAED,KAAM,CAAAC,aAAa,CAAGN,KAAK,CAACO,MAAM,CAC/BC,IAAI,EACHA,IAAI,CAACN,IAAI,CAACO,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC,EAC1DD,IAAI,CAACL,KAAK,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC,CAC9D,CAAC,CAED,KAAM,CAAAE,cAAc,CAAIN,MAAc,EAAK,CACzC,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,SAAS,CACpD,CAAC,CAED,KAAM,CAAAO,YAAY,CAAIR,IAAY,EAAK,CACrC,OAAQA,IAAI,EACV,IAAK,OAAO,CACV,MAAO,OAAO,CAChB,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED,mBACER,KAAA,CAACtB,GAAG,EAAAuC,QAAA,eACFjB,KAAA,CAACtB,GAAG,EAACwC,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACzFnB,IAAA,CAACnB,UAAU,EAAC4C,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAAP,QAAA,CAAC,kBAExC,CAAY,CAAC,cACbnB,IAAA,CAACR,MAAM,EACLiC,OAAO,CAAC,WAAW,CACnBE,SAAS,cAAE3B,IAAA,CAACF,GAAG,GAAE,CAAE,CACnB8B,OAAO,CAAEA,CAAA,GAAM,CACb;AACAC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC,CACjC,CAAE,CAAAX,QAAA,CACH,UAED,CAAQ,CAAC,EACN,CAAC,cAENnB,IAAA,CAACpB,GAAG,EAACwC,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,cACjBnB,IAAA,CAACP,SAAS,EACRsC,SAAS,MACTN,OAAO,CAAC,UAAU,CAClBO,WAAW,CAAC,iBAAiB,CAC7BC,KAAK,CAAE7B,UAAW,CAClB8B,QAAQ,CAAGC,CAAC,EAAK9B,aAAa,CAAC8B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CI,UAAU,CAAE,CACVC,cAAc,cACZtC,IAAA,CAACN,cAAc,EAAC6C,QAAQ,CAAC,OAAO,CAAApB,QAAA,cAC9BnB,IAAA,CAACH,MAAM,GAAE,CAAC,CACI,CAEpB,CAAE,CACH,CAAC,CACC,CAAC,cAENG,IAAA,CAACd,cAAc,EAACwC,SAAS,CAAE5C,KAAM,CAAAqC,QAAA,cAC/BjB,KAAA,CAACnB,KAAK,EAAAoC,QAAA,eACJnB,IAAA,CAACb,SAAS,EAAAgC,QAAA,cACRjB,KAAA,CAACd,QAAQ,EAAA+B,QAAA,eACPnB,IAAA,CAACf,SAAS,EAAAkC,QAAA,CAAC,MAAI,CAAW,CAAC,cAC3BnB,IAAA,CAACf,SAAS,EAAAkC,QAAA,CAAC,OAAK,CAAW,CAAC,cAC5BnB,IAAA,CAACf,SAAS,EAAAkC,QAAA,CAAC,MAAI,CAAW,CAAC,cAC3BnB,IAAA,CAACf,SAAS,EAAAkC,QAAA,CAAC,QAAM,CAAW,CAAC,cAC7BnB,IAAA,CAACf,SAAS,EAACuD,KAAK,CAAC,OAAO,CAAArB,QAAA,CAAC,SAAO,CAAW,CAAC,EACpC,CAAC,CACF,CAAC,cACZnB,IAAA,CAAChB,SAAS,EAAAmC,QAAA,CACPP,aAAa,CAAC6B,GAAG,CAAE3B,IAAI,eACtBZ,KAAA,CAACd,QAAQ,EAAesD,KAAK,MAAAvB,QAAA,eAC3BnB,IAAA,CAACf,SAAS,EAAAkC,QAAA,cACRjB,KAAA,CAACtB,GAAG,EAACwC,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAJ,QAAA,eACjDnB,IAAA,CAACX,MAAM,EAAC+B,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAE,CAAE,CAAAxB,QAAA,CACnBL,IAAI,CAACN,IAAI,CAACoC,MAAM,CAAC,CAAC,CAAC,CACd,CAAC,cACT5C,IAAA,CAACnB,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAEL,IAAI,CAACN,IAAI,CAAa,CAAC,EACjD,CAAC,CACG,CAAC,cACZR,IAAA,CAACf,SAAS,EAAAkC,QAAA,CAAEL,IAAI,CAACL,KAAK,CAAY,CAAC,cACnCT,IAAA,CAACf,SAAS,EAAAkC,QAAA,cACRnB,IAAA,CAACV,IAAI,EACHuD,KAAK,CAAE/B,IAAI,CAACJ,IAAK,CACjBoC,KAAK,CAAE5B,YAAY,CAACJ,IAAI,CAACJ,IAAI,CAAS,CACtCqC,IAAI,CAAC,OAAO,CACb,CAAC,CACO,CAAC,cACZ/C,IAAA,CAACf,SAAS,EAAAkC,QAAA,cACRnB,IAAA,CAACV,IAAI,EACHuD,KAAK,CAAE/B,IAAI,CAACH,MAAO,CACnBmC,KAAK,CAAE7B,cAAc,CAACH,IAAI,CAACH,MAAM,CAAS,CAC1CoC,IAAI,CAAC,OAAO,CACb,CAAC,CACO,CAAC,cACZ7C,KAAA,CAACjB,SAAS,EAACuD,KAAK,CAAC,OAAO,CAAArB,QAAA,eACtBnB,IAAA,CAACT,UAAU,EACTwD,IAAI,CAAC,OAAO,CACZnB,OAAO,CAAEA,CAAA,GAAM,CACb;AACAC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEhB,IAAI,CAACP,EAAE,CAAC,CACpC,CAAE,CAAAY,QAAA,cAEFnB,IAAA,CAACL,IAAI,GAAE,CAAC,CACE,CAAC,cACbK,IAAA,CAACT,UAAU,EACTwD,IAAI,CAAC,OAAO,CACZnB,OAAO,CAAEA,CAAA,GAAM,CACb;AACAC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEhB,IAAI,CAACP,EAAE,CAAC,CACtC,CAAE,CAAAY,QAAA,cAEFnB,IAAA,CAACJ,MAAM,GAAE,CAAC,CACA,CAAC,EACJ,CAAC,GA3CCkB,IAAI,CAACP,EA4CV,CACX,CAAC,CACO,CAAC,EACP,CAAC,CACM,CAAC,CAEhBK,aAAa,CAACoC,MAAM,GAAK,CAAC,eACzBhD,IAAA,CAACpB,GAAG,EAACwC,EAAE,CAAE,CAAE6B,SAAS,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA/B,QAAA,cACtCnB,IAAA,CAACnB,UAAU,EAAC4C,OAAO,CAAC,OAAO,CAACqB,KAAK,CAAC,eAAe,CAAA3B,QAAA,CAAC,+CAElD,CAAY,CAAC,CACV,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}