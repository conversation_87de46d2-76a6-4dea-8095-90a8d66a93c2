{"ast": null, "code": "export { default } from \"./TableCell.js\";\nexport { default as tableCellClasses } from \"./tableCellClasses.js\";\nexport * from \"./tableCellClasses.js\";", "map": {"version": 3, "names": ["default", "tableCellClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/TableCell/index.js"], "sourcesContent": ["export { default } from \"./TableCell.js\";\nexport { default as tableCellClasses } from \"./tableCellClasses.js\";\nexport * from \"./tableCellClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AACxC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,uBAAuB;AACnE,cAAc,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}