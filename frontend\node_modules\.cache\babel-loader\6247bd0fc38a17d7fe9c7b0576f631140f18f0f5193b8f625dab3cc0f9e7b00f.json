{"ast": null, "code": "'use client';\n\nimport useForkRef from '@mui/utils/useForkRef';\nexport default useForkRef;", "map": {"version": 3, "names": ["useForkRef"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/utils/useForkRef.js"], "sourcesContent": ["'use client';\n\nimport useForkRef from '@mui/utils/useForkRef';\nexport default useForkRef;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}