{"ast": null, "code": "/**\n * Based on Kendo UI Core expression code <https://github.com/telerik/kendo-ui-core#license-information>\n */\n'use strict';\n\nfunction Cache(maxSize) {\n  this._maxSize = maxSize;\n  this.clear();\n}\nCache.prototype.clear = function () {\n  this._size = 0;\n  this._values = Object.create(null);\n};\nCache.prototype.get = function (key) {\n  return this._values[key];\n};\nCache.prototype.set = function (key, value) {\n  this._size >= this._maxSize && this.clear();\n  if (!(key in this._values)) this._size++;\n  return this._values[key] = value;\n};\nvar SPLIT_REGEX = /[^.^\\]^[]+|(?=\\[\\]|\\.\\.)/g,\n  DIGIT_REGEX = /^\\d+$/,\n  LEAD_DIGIT_REGEX = /^\\d/,\n  SPEC_CHAR_REGEX = /[~`!#$%\\^&*+=\\-\\[\\]\\\\';,/{}|\\\\\":<>\\?]/g,\n  CLEAN_QUOTES_REGEX = /^\\s*(['\"]?)(.*?)(\\1)\\s*$/,\n  MAX_CACHE_SIZE = 512;\nvar pathCache = new Cache(MAX_CACHE_SIZE),\n  setCache = new Cache(MAX_CACHE_SIZE),\n  getCache = new Cache(MAX_CACHE_SIZE);\nvar config;\nmodule.exports = {\n  Cache: Cache,\n  split: split,\n  normalizePath: normalizePath,\n  setter: function (path) {\n    var parts = normalizePath(path);\n    return setCache.get(path) || setCache.set(path, function setter(obj, value) {\n      var index = 0;\n      var len = parts.length;\n      var data = obj;\n      while (index < len - 1) {\n        var part = parts[index];\n        if (part === '__proto__' || part === 'constructor' || part === 'prototype') {\n          return obj;\n        }\n        data = data[parts[index++]];\n      }\n      data[parts[index]] = value;\n    });\n  },\n  getter: function (path, safe) {\n    var parts = normalizePath(path);\n    return getCache.get(path) || getCache.set(path, function getter(data) {\n      var index = 0,\n        len = parts.length;\n      while (index < len) {\n        if (data != null || !safe) data = data[parts[index++]];else return;\n      }\n      return data;\n    });\n  },\n  join: function (segments) {\n    return segments.reduce(function (path, part) {\n      return path + (isQuoted(part) || DIGIT_REGEX.test(part) ? '[' + part + ']' : (path ? '.' : '') + part);\n    }, '');\n  },\n  forEach: function (path, cb, thisArg) {\n    forEach(Array.isArray(path) ? path : split(path), cb, thisArg);\n  }\n};\nfunction normalizePath(path) {\n  return pathCache.get(path) || pathCache.set(path, split(path).map(function (part) {\n    return part.replace(CLEAN_QUOTES_REGEX, '$2');\n  }));\n}\nfunction split(path) {\n  return path.match(SPLIT_REGEX) || [''];\n}\nfunction forEach(parts, iter, thisArg) {\n  var len = parts.length,\n    part,\n    idx,\n    isArray,\n    isBracket;\n  for (idx = 0; idx < len; idx++) {\n    part = parts[idx];\n    if (part) {\n      if (shouldBeQuoted(part)) {\n        part = '\"' + part + '\"';\n      }\n      isBracket = isQuoted(part);\n      isArray = !isBracket && /^\\d+$/.test(part);\n      iter.call(thisArg, part, isBracket, isArray, idx, parts);\n    }\n  }\n}\nfunction isQuoted(str) {\n  return typeof str === 'string' && str && [\"'\", '\"'].indexOf(str.charAt(0)) !== -1;\n}\nfunction hasLeadingNumber(part) {\n  return part.match(LEAD_DIGIT_REGEX) && !part.match(DIGIT_REGEX);\n}\nfunction hasSpecialChars(part) {\n  return SPEC_CHAR_REGEX.test(part);\n}\nfunction shouldBeQuoted(part) {\n  return !isQuoted(part) && (hasLeadingNumber(part) || hasSpecialChars(part));\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "maxSize", "_maxSize", "clear", "prototype", "_size", "_values", "Object", "create", "get", "key", "set", "value", "SPLIT_REGEX", "DIGIT_REGEX", "LEAD_DIGIT_REGEX", "SPEC_CHAR_REGEX", "CLEAN_QUOTES_REGEX", "MAX_CACHE_SIZE", "pathCache", "setCache", "getCache", "config", "module", "exports", "split", "normalizePath", "setter", "path", "parts", "obj", "index", "len", "length", "data", "part", "getter", "safe", "join", "segments", "reduce", "isQuoted", "test", "for<PERSON>ach", "cb", "thisArg", "Array", "isArray", "map", "replace", "match", "iter", "idx", "isBracket", "shouldBeQuoted", "call", "str", "indexOf", "char<PERSON>t", "hasLeadingNumber", "hasSpecialChars"], "sources": ["C:/laragon/www/frontend/node_modules/property-expr/index.js"], "sourcesContent": ["/**\n * Based on Kendo UI Core expression code <https://github.com/telerik/kendo-ui-core#license-information>\n */\n'use strict'\n\nfunction Cache(maxSize) {\n  this._maxSize = maxSize\n  this.clear()\n}\nCache.prototype.clear = function () {\n  this._size = 0\n  this._values = Object.create(null)\n}\nCache.prototype.get = function (key) {\n  return this._values[key]\n}\nCache.prototype.set = function (key, value) {\n  this._size >= this._maxSize && this.clear()\n  if (!(key in this._values)) this._size++\n\n  return (this._values[key] = value)\n}\n\nvar SPLIT_REGEX = /[^.^\\]^[]+|(?=\\[\\]|\\.\\.)/g,\n  DIGIT_REGEX = /^\\d+$/,\n  LEAD_DIGIT_REGEX = /^\\d/,\n  SPEC_CHAR_REGEX = /[~`!#$%\\^&*+=\\-\\[\\]\\\\';,/{}|\\\\\":<>\\?]/g,\n  CLEAN_QUOTES_REGEX = /^\\s*(['\"]?)(.*?)(\\1)\\s*$/,\n  MAX_CACHE_SIZE = 512\n\nvar pathCache = new Cache(MAX_CACHE_SIZE),\n  setCache = new Cache(MAX_CACHE_SIZE),\n  getCache = new Cache(MAX_CACHE_SIZE)\n\nvar config\n\nmodule.exports = {\n  Cache: Cache,\n\n  split: split,\n\n  normalizePath: normalizePath,\n\n  setter: function (path) {\n    var parts = normalizePath(path)\n\n    return (\n      setCache.get(path) ||\n      setCache.set(path, function setter(obj, value) {\n        var index = 0\n        var len = parts.length\n        var data = obj\n\n        while (index < len - 1) {\n          var part = parts[index]\n          if (\n            part === '__proto__' ||\n            part === 'constructor' ||\n            part === 'prototype'\n          ) {\n            return obj\n          }\n\n          data = data[parts[index++]]\n        }\n        data[parts[index]] = value\n      })\n    )\n  },\n\n  getter: function (path, safe) {\n    var parts = normalizePath(path)\n    return (\n      getCache.get(path) ||\n      getCache.set(path, function getter(data) {\n        var index = 0,\n          len = parts.length\n        while (index < len) {\n          if (data != null || !safe) data = data[parts[index++]]\n          else return\n        }\n        return data\n      })\n    )\n  },\n\n  join: function (segments) {\n    return segments.reduce(function (path, part) {\n      return (\n        path +\n        (isQuoted(part) || DIGIT_REGEX.test(part)\n          ? '[' + part + ']'\n          : (path ? '.' : '') + part)\n      )\n    }, '')\n  },\n\n  forEach: function (path, cb, thisArg) {\n    forEach(Array.isArray(path) ? path : split(path), cb, thisArg)\n  },\n}\n\nfunction normalizePath(path) {\n  return (\n    pathCache.get(path) ||\n    pathCache.set(\n      path,\n      split(path).map(function (part) {\n        return part.replace(CLEAN_QUOTES_REGEX, '$2')\n      })\n    )\n  )\n}\n\nfunction split(path) {\n  return path.match(SPLIT_REGEX) || ['']\n}\n\nfunction forEach(parts, iter, thisArg) {\n  var len = parts.length,\n    part,\n    idx,\n    isArray,\n    isBracket\n\n  for (idx = 0; idx < len; idx++) {\n    part = parts[idx]\n\n    if (part) {\n      if (shouldBeQuoted(part)) {\n        part = '\"' + part + '\"'\n      }\n\n      isBracket = isQuoted(part)\n      isArray = !isBracket && /^\\d+$/.test(part)\n\n      iter.call(thisArg, part, isBracket, isArray, idx, parts)\n    }\n  }\n}\n\nfunction isQuoted(str) {\n  return (\n    typeof str === 'string' && str && [\"'\", '\"'].indexOf(str.charAt(0)) !== -1\n  )\n}\n\nfunction hasLeadingNumber(part) {\n  return part.match(LEAD_DIGIT_REGEX) && !part.match(DIGIT_REGEX)\n}\n\nfunction hasSpecialChars(part) {\n  return SPEC_CHAR_REGEX.test(part)\n}\n\nfunction shouldBeQuoted(part) {\n  return !isQuoted(part) && (hasLeadingNumber(part) || hasSpecialChars(part))\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,YAAY;;AAEZ,SAASA,KAAKA,CAACC,OAAO,EAAE;EACtB,IAAI,CAACC,QAAQ,GAAGD,OAAO;EACvB,IAAI,CAACE,KAAK,CAAC,CAAC;AACd;AACAH,KAAK,CAACI,SAAS,CAACD,KAAK,GAAG,YAAY;EAClC,IAAI,CAACE,KAAK,GAAG,CAAC;EACd,IAAI,CAACC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AACpC,CAAC;AACDR,KAAK,CAACI,SAAS,CAACK,GAAG,GAAG,UAAUC,GAAG,EAAE;EACnC,OAAO,IAAI,CAACJ,OAAO,CAACI,GAAG,CAAC;AAC1B,CAAC;AACDV,KAAK,CAACI,SAAS,CAACO,GAAG,GAAG,UAAUD,GAAG,EAAEE,KAAK,EAAE;EAC1C,IAAI,CAACP,KAAK,IAAI,IAAI,CAACH,QAAQ,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC;EAC3C,IAAI,EAAEO,GAAG,IAAI,IAAI,CAACJ,OAAO,CAAC,EAAE,IAAI,CAACD,KAAK,EAAE;EAExC,OAAQ,IAAI,CAACC,OAAO,CAACI,GAAG,CAAC,GAAGE,KAAK;AACnC,CAAC;AAED,IAAIC,WAAW,GAAG,2BAA2B;EAC3CC,WAAW,GAAG,OAAO;EACrBC,gBAAgB,GAAG,KAAK;EACxBC,eAAe,GAAG,wCAAwC;EAC1DC,kBAAkB,GAAG,0BAA0B;EAC/CC,cAAc,GAAG,GAAG;AAEtB,IAAIC,SAAS,GAAG,IAAInB,KAAK,CAACkB,cAAc,CAAC;EACvCE,QAAQ,GAAG,IAAIpB,KAAK,CAACkB,cAAc,CAAC;EACpCG,QAAQ,GAAG,IAAIrB,KAAK,CAACkB,cAAc,CAAC;AAEtC,IAAII,MAAM;AAEVC,MAAM,CAACC,OAAO,GAAG;EACfxB,KAAK,EAAEA,KAAK;EAEZyB,KAAK,EAAEA,KAAK;EAEZC,aAAa,EAAEA,aAAa;EAE5BC,MAAM,EAAE,SAAAA,CAAUC,IAAI,EAAE;IACtB,IAAIC,KAAK,GAAGH,aAAa,CAACE,IAAI,CAAC;IAE/B,OACER,QAAQ,CAACX,GAAG,CAACmB,IAAI,CAAC,IAClBR,QAAQ,CAACT,GAAG,CAACiB,IAAI,EAAE,SAASD,MAAMA,CAACG,GAAG,EAAElB,KAAK,EAAE;MAC7C,IAAImB,KAAK,GAAG,CAAC;MACb,IAAIC,GAAG,GAAGH,KAAK,CAACI,MAAM;MACtB,IAAIC,IAAI,GAAGJ,GAAG;MAEd,OAAOC,KAAK,GAAGC,GAAG,GAAG,CAAC,EAAE;QACtB,IAAIG,IAAI,GAAGN,KAAK,CAACE,KAAK,CAAC;QACvB,IACEI,IAAI,KAAK,WAAW,IACpBA,IAAI,KAAK,aAAa,IACtBA,IAAI,KAAK,WAAW,EACpB;UACA,OAAOL,GAAG;QACZ;QAEAI,IAAI,GAAGA,IAAI,CAACL,KAAK,CAACE,KAAK,EAAE,CAAC,CAAC;MAC7B;MACAG,IAAI,CAACL,KAAK,CAACE,KAAK,CAAC,CAAC,GAAGnB,KAAK;IAC5B,CAAC,CAAC;EAEN,CAAC;EAEDwB,MAAM,EAAE,SAAAA,CAAUR,IAAI,EAAES,IAAI,EAAE;IAC5B,IAAIR,KAAK,GAAGH,aAAa,CAACE,IAAI,CAAC;IAC/B,OACEP,QAAQ,CAACZ,GAAG,CAACmB,IAAI,CAAC,IAClBP,QAAQ,CAACV,GAAG,CAACiB,IAAI,EAAE,SAASQ,MAAMA,CAACF,IAAI,EAAE;MACvC,IAAIH,KAAK,GAAG,CAAC;QACXC,GAAG,GAAGH,KAAK,CAACI,MAAM;MACpB,OAAOF,KAAK,GAAGC,GAAG,EAAE;QAClB,IAAIE,IAAI,IAAI,IAAI,IAAI,CAACG,IAAI,EAAEH,IAAI,GAAGA,IAAI,CAACL,KAAK,CAACE,KAAK,EAAE,CAAC,CAAC,MACjD;MACP;MACA,OAAOG,IAAI;IACb,CAAC,CAAC;EAEN,CAAC;EAEDI,IAAI,EAAE,SAAAA,CAAUC,QAAQ,EAAE;IACxB,OAAOA,QAAQ,CAACC,MAAM,CAAC,UAAUZ,IAAI,EAAEO,IAAI,EAAE;MAC3C,OACEP,IAAI,IACHa,QAAQ,CAACN,IAAI,CAAC,IAAIrB,WAAW,CAAC4B,IAAI,CAACP,IAAI,CAAC,GACrC,GAAG,GAAGA,IAAI,GAAG,GAAG,GAChB,CAACP,IAAI,GAAG,GAAG,GAAG,EAAE,IAAIO,IAAI,CAAC;IAEjC,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EAEDQ,OAAO,EAAE,SAAAA,CAAUf,IAAI,EAAEgB,EAAE,EAAEC,OAAO,EAAE;IACpCF,OAAO,CAACG,KAAK,CAACC,OAAO,CAACnB,IAAI,CAAC,GAAGA,IAAI,GAAGH,KAAK,CAACG,IAAI,CAAC,EAAEgB,EAAE,EAAEC,OAAO,CAAC;EAChE;AACF,CAAC;AAED,SAASnB,aAAaA,CAACE,IAAI,EAAE;EAC3B,OACET,SAAS,CAACV,GAAG,CAACmB,IAAI,CAAC,IACnBT,SAAS,CAACR,GAAG,CACXiB,IAAI,EACJH,KAAK,CAACG,IAAI,CAAC,CAACoB,GAAG,CAAC,UAAUb,IAAI,EAAE;IAC9B,OAAOA,IAAI,CAACc,OAAO,CAAChC,kBAAkB,EAAE,IAAI,CAAC;EAC/C,CAAC,CACH,CAAC;AAEL;AAEA,SAASQ,KAAKA,CAACG,IAAI,EAAE;EACnB,OAAOA,IAAI,CAACsB,KAAK,CAACrC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;AACxC;AAEA,SAAS8B,OAAOA,CAACd,KAAK,EAAEsB,IAAI,EAAEN,OAAO,EAAE;EACrC,IAAIb,GAAG,GAAGH,KAAK,CAACI,MAAM;IACpBE,IAAI;IACJiB,GAAG;IACHL,OAAO;IACPM,SAAS;EAEX,KAAKD,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGpB,GAAG,EAAEoB,GAAG,EAAE,EAAE;IAC9BjB,IAAI,GAAGN,KAAK,CAACuB,GAAG,CAAC;IAEjB,IAAIjB,IAAI,EAAE;MACR,IAAImB,cAAc,CAACnB,IAAI,CAAC,EAAE;QACxBA,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,GAAG;MACzB;MAEAkB,SAAS,GAAGZ,QAAQ,CAACN,IAAI,CAAC;MAC1BY,OAAO,GAAG,CAACM,SAAS,IAAI,OAAO,CAACX,IAAI,CAACP,IAAI,CAAC;MAE1CgB,IAAI,CAACI,IAAI,CAACV,OAAO,EAAEV,IAAI,EAAEkB,SAAS,EAAEN,OAAO,EAAEK,GAAG,EAAEvB,KAAK,CAAC;IAC1D;EACF;AACF;AAEA,SAASY,QAAQA,CAACe,GAAG,EAAE;EACrB,OACE,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,OAAO,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAE9E;AAEA,SAASC,gBAAgBA,CAACxB,IAAI,EAAE;EAC9B,OAAOA,IAAI,CAACe,KAAK,CAACnC,gBAAgB,CAAC,IAAI,CAACoB,IAAI,CAACe,KAAK,CAACpC,WAAW,CAAC;AACjE;AAEA,SAAS8C,eAAeA,CAACzB,IAAI,EAAE;EAC7B,OAAOnB,eAAe,CAAC0B,IAAI,CAACP,IAAI,CAAC;AACnC;AAEA,SAASmB,cAAcA,CAACnB,IAAI,EAAE;EAC5B,OAAO,CAACM,QAAQ,CAACN,IAAI,CAAC,KAAKwB,gBAAgB,CAACxB,IAAI,CAAC,IAAIyB,eAAe,CAACzB,IAAI,CAAC,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}