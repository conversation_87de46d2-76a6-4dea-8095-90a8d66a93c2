{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ModalDialog = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    contentClassName,\n    centered,\n    size,\n    fullscreen,\n    children,\n    scrollable,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal');\n  const dialogClass = `${bsPrefix}-dialog`;\n  const fullScreenClass = typeof fullscreen === 'string' ? `${bsPrefix}-fullscreen-${fullscreen}` : `${bsPrefix}-fullscreen`;\n  return /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(dialogClass, className, size && `${bsPrefix}-${size}`, centered && `${dialogClass}-centered`, scrollable && `${dialogClass}-scrollable`, fullscreen && fullScreenClass),\n    children: /*#__PURE__*/_jsx(\"div\", {\n      className: classNames(`${bsPrefix}-content`, contentClassName),\n      children: children\n    })\n  });\n});\nModalDialog.displayName = 'ModalDialog';\nexport default ModalDialog;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "ModalDialog", "forwardRef", "_ref", "ref", "bsPrefix", "className", "contentClassName", "centered", "size", "fullscreen", "children", "scrollable", "props", "dialogClass", "fullScreenClass", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/ModalDialog.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ModalDialog = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  contentClassName,\n  centered,\n  size,\n  fullscreen,\n  children,\n  scrollable,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal');\n  const dialogClass = `${bsPrefix}-dialog`;\n  const fullScreenClass = typeof fullscreen === 'string' ? `${bsPrefix}-fullscreen-${fullscreen}` : `${bsPrefix}-fullscreen`;\n  return /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(dialogClass, className, size && `${bsPrefix}-${size}`, centered && `${dialogClass}-centered`, scrollable && `${dialogClass}-scrollable`, fullscreen && fullScreenClass),\n    children: /*#__PURE__*/_jsx(\"div\", {\n      className: classNames(`${bsPrefix}-content`, contentClassName),\n      children: children\n    })\n  });\n});\nModalDialog.displayName = 'ModalDialog';\nexport default ModalDialog;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAAC,IAAA,EAU/CC,GAAG,KAAK;EAAA,IAVwC;IACjDC,QAAQ;IACRC,SAAS;IACTC,gBAAgB;IAChBC,QAAQ;IACRC,IAAI;IACJC,UAAU;IACVC,QAAQ;IACRC,UAAU;IACV,GAAGC;EACL,CAAC,GAAAV,IAAA;EACCE,QAAQ,GAAGP,kBAAkB,CAACO,QAAQ,EAAE,OAAO,CAAC;EAChD,MAAMS,WAAW,GAAG,GAAGT,QAAQ,SAAS;EACxC,MAAMU,eAAe,GAAG,OAAOL,UAAU,KAAK,QAAQ,GAAG,GAAGL,QAAQ,eAAeK,UAAU,EAAE,GAAG,GAAGL,QAAQ,aAAa;EAC1H,OAAO,aAAaL,IAAI,CAAC,KAAK,EAAE;IAC9B,GAAGa,KAAK;IACRT,GAAG,EAAEA,GAAG;IACRE,SAAS,EAAEV,UAAU,CAACkB,WAAW,EAAER,SAAS,EAAEG,IAAI,IAAI,GAAGJ,QAAQ,IAAII,IAAI,EAAE,EAAED,QAAQ,IAAI,GAAGM,WAAW,WAAW,EAAEF,UAAU,IAAI,GAAGE,WAAW,aAAa,EAAEJ,UAAU,IAAIK,eAAe,CAAC;IAC7LJ,QAAQ,EAAE,aAAaX,IAAI,CAAC,KAAK,EAAE;MACjCM,SAAS,EAAEV,UAAU,CAAC,GAAGS,QAAQ,UAAU,EAAEE,gBAAgB,CAAC;MAC9DI,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFV,WAAW,CAACe,WAAW,GAAG,aAAa;AACvC,eAAef,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}