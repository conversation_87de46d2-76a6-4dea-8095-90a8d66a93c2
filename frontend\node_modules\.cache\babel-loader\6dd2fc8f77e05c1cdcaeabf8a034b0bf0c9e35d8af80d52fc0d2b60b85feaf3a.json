{"ast": null, "code": "export { default } from \"./Tab.js\";\nexport { default as tabClasses } from \"./tabClasses.js\";\nexport * from \"./tabClasses.js\";", "map": {"version": 3, "names": ["default", "tabClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Tab/index.js"], "sourcesContent": ["export { default } from \"./Tab.js\";\nexport { default as tabClasses } from \"./tabClasses.js\";\nexport * from \"./tabClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,UAAU;AAClC,SAASA,OAAO,IAAIC,UAAU,QAAQ,iBAAiB;AACvD,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}