{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M13 4H6v16h12V9h-5zm.97 7.03c1.12 1.12 1.31 2.8.59 4.13l1.88 1.88-1.41 1.41-1.88-1.88c-1.33.71-3.01.53-4.13-.59-1.37-1.37-1.37-3.58 0-4.95s3.59-1.37 4.95 0\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"11.5\",\n  cy: \"13.5\",\n  r: \"1.5\",\n  opacity: \".3\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8zm4 18H6V4h7v5h5z\"\n}, \"2\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M9.03 11.03c-1.37 1.37-1.37 3.58 0 4.95 1.12 1.12 2.8 1.31 4.13.59l1.88 1.88 1.41-1.41-1.88-1.88c.71-1.33.53-3.01-.59-4.13-1.37-1.37-3.59-1.37-4.95 0m3.53 3.53c-.59.59-1.54.59-2.12 0-.59-.59-.59-1.54 0-2.12.59-.59 1.54-.59 2.12 0 .59.59.59 1.53 0 2.12\"\n}, \"3\")], 'PlagiarismTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity", "cx", "cy", "r"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/icons-material/esm/PlagiarismTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M13 4H6v16h12V9h-5zm.97 7.03c1.12 1.12 1.31 2.8.59 4.13l1.88 1.88-1.41 1.41-1.88-1.88c-1.33.71-3.01.53-4.13-.59-1.37-1.37-1.37-3.58 0-4.95s3.59-1.37 4.95 0\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"11.5\",\n  cy: \"13.5\",\n  r: \"1.5\",\n  opacity: \".3\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8zm4 18H6V4h7v5h5z\"\n}, \"2\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M9.03 11.03c-1.37 1.37-1.37 3.58 0 4.95 1.12 1.12 2.8 1.31 4.13.59l1.88 1.88 1.41-1.41-1.88-1.88c.71-1.33.53-3.01-.59-4.13-1.37-1.37-3.59-1.37-4.95 0m3.53 3.53c-.59.59-1.54.59-2.12 0-.59-.59-.59-1.54 0-2.12.59-.59 1.54-.59 2.12 0 .59.59.59 1.53 0 2.12\"\n}, \"3\")], 'PlagiarismTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,6JAA6J;EAChKC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,QAAQ,EAAE;EACnCG,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE,KAAK;EACRH,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}