{"ast": null, "code": "export { default } from \"./DialogContentText.js\";\nexport { default as dialogContentTextClasses } from \"./dialogContentTextClasses.js\";\nexport * from \"./dialogContentTextClasses.js\";", "map": {"version": 3, "names": ["default", "dialogContentTextClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/DialogContentText/index.js"], "sourcesContent": ["export { default } from \"./DialogContentText.js\";\nexport { default as dialogContentTextClasses } from \"./dialogContentTextClasses.js\";\nexport * from \"./dialogContentTextClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASA,OAAO,IAAIC,wBAAwB,QAAQ,+BAA+B;AACnF,cAAc,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}