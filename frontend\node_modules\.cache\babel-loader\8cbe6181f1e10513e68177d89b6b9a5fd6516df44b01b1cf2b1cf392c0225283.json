{"ast": null, "code": "import getComputedStyle from './getComputedStyle';\nimport hyphenate from './hyphenateStyle';\nimport isTransform from './isTransform';\nfunction style(node, property) {\n  var css = '';\n  var transforms = '';\n  if (typeof property === 'string') {\n    return node.style.getPropertyValue(hyphenate(property)) || getComputedStyle(node).getPropertyValue(hyphenate(property));\n  }\n  Object.keys(property).forEach(function (key) {\n    var value = property[key];\n    if (!value && value !== 0) {\n      node.style.removeProperty(hyphenate(key));\n    } else if (isTransform(key)) {\n      transforms += key + \"(\" + value + \") \";\n    } else {\n      css += hyphenate(key) + \": \" + value + \";\";\n    }\n  });\n  if (transforms) {\n    css += \"transform: \" + transforms + \";\";\n  }\n  node.style.cssText += \";\" + css;\n}\nexport default style;", "map": {"version": 3, "names": ["getComputedStyle", "hyphenate", "isTransform", "style", "node", "property", "css", "transforms", "getPropertyValue", "Object", "keys", "for<PERSON>ach", "key", "value", "removeProperty", "cssText"], "sources": ["C:/laragon/www/frontend/node_modules/dom-helpers/esm/css.js"], "sourcesContent": ["import getComputedStyle from './getComputedStyle';\nimport hyphenate from './hyphenateStyle';\nimport isTransform from './isTransform';\n\nfunction style(node, property) {\n  var css = '';\n  var transforms = '';\n\n  if (typeof property === 'string') {\n    return node.style.getPropertyValue(hyphenate(property)) || getComputedStyle(node).getPropertyValue(hyphenate(property));\n  }\n\n  Object.keys(property).forEach(function (key) {\n    var value = property[key];\n\n    if (!value && value !== 0) {\n      node.style.removeProperty(hyphenate(key));\n    } else if (isTransform(key)) {\n      transforms += key + \"(\" + value + \") \";\n    } else {\n      css += hyphenate(key) + \": \" + value + \";\";\n    }\n  });\n\n  if (transforms) {\n    css += \"transform: \" + transforms + \";\";\n  }\n\n  node.style.cssText += \";\" + css;\n}\n\nexport default style;"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAOC,WAAW,MAAM,eAAe;AAEvC,SAASC,KAAKA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC7B,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,UAAU,GAAG,EAAE;EAEnB,IAAI,OAAOF,QAAQ,KAAK,QAAQ,EAAE;IAChC,OAAOD,IAAI,CAACD,KAAK,CAACK,gBAAgB,CAACP,SAAS,CAACI,QAAQ,CAAC,CAAC,IAAIL,gBAAgB,CAACI,IAAI,CAAC,CAACI,gBAAgB,CAACP,SAAS,CAACI,QAAQ,CAAC,CAAC;EACzH;EAEAI,MAAM,CAACC,IAAI,CAACL,QAAQ,CAAC,CAACM,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC3C,IAAIC,KAAK,GAAGR,QAAQ,CAACO,GAAG,CAAC;IAEzB,IAAI,CAACC,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE;MACzBT,IAAI,CAACD,KAAK,CAACW,cAAc,CAACb,SAAS,CAACW,GAAG,CAAC,CAAC;IAC3C,CAAC,MAAM,IAAIV,WAAW,CAACU,GAAG,CAAC,EAAE;MAC3BL,UAAU,IAAIK,GAAG,GAAG,GAAG,GAAGC,KAAK,GAAG,IAAI;IACxC,CAAC,MAAM;MACLP,GAAG,IAAIL,SAAS,CAACW,GAAG,CAAC,GAAG,IAAI,GAAGC,KAAK,GAAG,GAAG;IAC5C;EACF,CAAC,CAAC;EAEF,IAAIN,UAAU,EAAE;IACdD,GAAG,IAAI,aAAa,GAAGC,UAAU,GAAG,GAAG;EACzC;EAEAH,IAAI,CAACD,KAAK,CAACY,OAAO,IAAI,GAAG,GAAGT,GAAG;AACjC;AAEA,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}