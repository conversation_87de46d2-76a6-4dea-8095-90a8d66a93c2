{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\components\\\\dashboard\\\\FileUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Box, Typography, Paper, Button, LinearProgress, Alert, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, Card, CardContent, CardActions, Tooltip } from '@mui/material';\nimport { CloudUpload as UploadIcon, Delete as DeleteIcon, Warning as WarningIcon, CheckCircle as CheckIcon, Error as ErrorIcon, Image as ImageIcon, PictureAsPdf as PdfIcon, Description as FileIcon } from '@mui/icons-material';\nimport { useDropzone } from 'react-dropzone';\nimport printingService from '../../services/printingService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FileUpload = ({\n  orderId,\n  onFilesUploaded,\n  onError\n}) => {\n  _s();\n  const [settings, setSettings] = useState(null);\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [uploadingFiles, setUploadingFiles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [fileToDelete, setFileToDelete] = useState(null);\n  const [selectedFileType, setSelectedFileType] = useState('artwork');\n\n  // Load settings and files on component mount\n  useEffect(() => {\n    loadSettings();\n    if (orderId) {\n      loadUploadedFiles();\n    }\n  }, [orderId]);\n  const loadSettings = async () => {\n    try {\n      const uploadSettings = await printingService.getUploadSettings();\n\n      // Ensure allowed_file_types is always an array\n      if (uploadSettings && !Array.isArray(uploadSettings.allowed_file_types)) {\n        uploadSettings.allowed_file_types = ['pdf', 'png', 'jpg', 'jpeg'];\n      }\n      setSettings(uploadSettings);\n    } catch (error) {\n      console.error('Failed to load upload settings:', error);\n      onError === null || onError === void 0 ? void 0 : onError('Failed to load upload settings');\n\n      // Set default settings if loading fails\n      setSettings({\n        allowed_file_types: ['pdf', 'png', 'jpg', 'jpeg'],\n        max_file_size_mb: 50,\n        max_total_upload_size_mb: 200,\n        max_files_per_order: 10,\n        min_dpi_requirement: 300,\n        dpi_warning_threshold: 150,\n        enable_dpi_validation: true,\n        min_width_px: 100,\n        min_height_px: 100,\n        max_width_px: 10000,\n        max_height_px: 10000,\n        enable_dimension_validation: true\n      });\n    }\n  };\n  const loadUploadedFiles = async () => {\n    if (!orderId) return;\n    try {\n      const files = await printingService.getOrderFiles(orderId);\n      setUploadedFiles(files);\n    } catch (error) {\n      console.error('Failed to load uploaded files:', error);\n    }\n  };\n\n  // Validate files before upload\n  const validateFiles = async files => {\n    if (!settings) return {\n      valid: [],\n      invalid: []\n    };\n    const valid = [];\n    const invalid = [];\n\n    // Check total file count\n    const totalFiles = uploadedFiles.length + files.length;\n    if (totalFiles > settings.max_files_per_order) {\n      const excess = totalFiles - settings.max_files_per_order;\n      for (let i = files.length - excess; i < files.length; i++) {\n        invalid.push({\n          file: files[i],\n          errors: [`Maximum ${settings.max_files_per_order} files allowed per order`]\n        });\n      }\n      files = files.slice(0, files.length - excess);\n    }\n\n    // Check total upload size\n    const currentTotalSize = uploadedFiles.reduce((sum, file) => sum + file.file_size, 0);\n    const newFilesSize = files.reduce((sum, file) => sum + file.size, 0);\n    const totalSize = currentTotalSize + newFilesSize;\n    const maxTotalSize = settings.max_total_upload_size_mb * 1024 * 1024;\n    if (totalSize > maxTotalSize) {\n      onError === null || onError === void 0 ? void 0 : onError(`Total upload size would exceed ${settings.max_total_upload_size_mb}MB limit`);\n      return {\n        valid: [],\n        invalid: files.map(file => ({\n          file,\n          errors: ['Total size limit exceeded']\n        }))\n      };\n    }\n\n    // Validate each file\n    for (const file of files) {\n      const validation = await printingService.validateFile(file);\n      if (validation.valid) {\n        valid.push(file);\n      } else {\n        invalid.push({\n          file,\n          errors: validation.message ? [validation.message] : ['Validation failed']\n        });\n      }\n    }\n    return {\n      valid,\n      invalid\n    };\n  };\n  const onDrop = useCallback(async acceptedFiles => {\n    if (!orderId) {\n      onError === null || onError === void 0 ? void 0 : onError('Please create an order first before uploading files');\n      return;\n    }\n    const {\n      valid,\n      invalid\n    } = await validateFiles(acceptedFiles);\n\n    // Show errors for invalid files\n    if (invalid.length > 0) {\n      const errorMessages = invalid.map(({\n        file,\n        errors\n      }) => `${file.name}: ${errors.join(', ')}`).join('\\n');\n      onError === null || onError === void 0 ? void 0 : onError(errorMessages);\n    }\n\n    // Upload valid files\n    if (valid.length > 0) {\n      await uploadFiles(valid);\n    }\n  }, [orderId, settings, uploadedFiles]);\n  const uploadFiles = async files => {\n    setLoading(true);\n\n    // Initialize uploading files state\n    const newUploadingFiles = files.map(file => ({\n      file,\n      progress: 0,\n      status: 'uploading'\n    }));\n    setUploadingFiles(newUploadingFiles);\n    try {\n      const uploadedFileResults = await printingService.uploadFiles(orderId, files, selectedFileType);\n\n      // Update uploading files to success\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        progress: 100,\n        status: 'success'\n      })));\n\n      // Add to uploaded files list\n      setUploadedFiles(prev => [...prev, ...uploadedFileResults]);\n      onFilesUploaded === null || onFilesUploaded === void 0 ? void 0 : onFilesUploaded(uploadedFileResults);\n\n      // Clear uploading files after a delay\n      setTimeout(() => {\n        setUploadingFiles([]);\n      }, 2000);\n    } catch (error) {\n      // Update uploading files to error\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        status: 'error',\n        error: error.message || 'Upload failed'\n      })));\n      onError === null || onError === void 0 ? void 0 : onError(error.message || 'Failed to upload files');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteFile = async () => {\n    if (!fileToDelete || !orderId) return;\n    try {\n      await printingService.deleteFile(orderId, fileToDelete.id);\n      setUploadedFiles(prev => prev.filter(f => f.id !== fileToDelete.id));\n      setDeleteDialogOpen(false);\n      setFileToDelete(null);\n    } catch (error) {\n      onError === null || onError === void 0 ? void 0 : onError(error.message || 'Failed to delete file');\n    }\n  };\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: settings && Array.isArray(settings.allowed_file_types) ? {\n      'image/*': settings.allowed_file_types.filter(type => ['png', 'jpg', 'jpeg', 'tiff', 'svg'].includes(type)).map(type => `.${type}`),\n      'application/pdf': settings.allowed_file_types.includes('pdf') ? ['.pdf'] : [],\n      'application/postscript': settings.allowed_file_types.includes('eps') ? ['.eps'] : [],\n      'application/illustrator': settings.allowed_file_types.includes('ai') ? ['.ai'] : []\n    } : undefined,\n    maxSize: settings ? settings.max_file_size_mb * 1024 * 1024 : undefined,\n    disabled: loading || !orderId\n  });\n  const getFileIcon = mimeType => {\n    if (mimeType.startsWith('image/')) return /*#__PURE__*/_jsxDEV(ImageIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 47\n    }, this);\n    if (mimeType === 'application/pdf') return /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 48\n    }, this);\n    return /*#__PURE__*/_jsxDEV(FileIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 12\n    }, this);\n  };\n  const getStatusIcon = file => {\n    if (file.dpi && settings !== null && settings !== void 0 && settings.enable_dpi_validation) {\n      if (file.dpi >= settings.min_dpi_requirement) {\n        return /*#__PURE__*/_jsxDEV(CheckIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 16\n        }, this);\n      } else if (file.dpi >= settings.dpi_warning_threshold) {\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 16\n        }, this);\n      } else {\n        return /*#__PURE__*/_jsxDEV(ErrorIcon, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 16\n        }, this);\n      }\n    }\n    return file.is_approved ? /*#__PURE__*/_jsxDEV(CheckIcon, {\n      color: \"success\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 31\n    }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {\n      color: \"warning\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 63\n    }, this);\n  };\n  const formatFileSize = bytes => {\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let size = bytes;\n    let unitIndex = 0;\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  };\n  if (!settings) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"200px\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Loading upload settings...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Upload Limits:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), \" Max \", settings.max_files_per_order, \" files,\", settings.max_file_size_mb, \"MB per file, \", settings.max_total_upload_size_mb, \"MB total. Allowed types: \", settings.allowed_file_types.join(', ').toUpperCase()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      fullWidth: true,\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n        children: \"File Type\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        value: selectedFileType,\n        onChange: e => setSelectedFileType(e.target.value),\n        label: \"File Type\",\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"artwork\",\n          children: \"Artwork Files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"reference\",\n          children: \"Reference Files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"proof\",\n          children: \"Proof Files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      ...getRootProps(),\n      sx: {\n        p: 4,\n        border: '2px dashed',\n        borderColor: isDragActive ? 'primary.main' : 'grey.300',\n        backgroundColor: isDragActive ? 'action.hover' : 'background.paper',\n        cursor: orderId ? 'pointer' : 'not-allowed',\n        opacity: orderId ? 1 : 0.5,\n        textAlign: 'center',\n        mb: 3,\n        transition: 'all 0.2s ease-in-out'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        ...getInputProps()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(UploadIcon, {\n        sx: {\n          fontSize: 48,\n          color: 'text.secondary',\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: isDragActive ? 'Drop files here' : 'Drag & drop files here'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: \"or click to browse files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        disabled: !orderId,\n        children: \"Browse Files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this), !orderId && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"error\",\n        sx: {\n          display: 'block',\n          mt: 1\n        },\n        children: \"Please create an order first\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this), uploadingFiles.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Uploading Files\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 11\n      }, this), uploadingFiles.map((uploadingFile, index) => /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            py: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 2,\n            children: [getFileIcon(uploadingFile.file.type), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                noWrap: true,\n                children: uploadingFile.file.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: formatFileSize(uploadingFile.file.size)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: 100\n              },\n              children: [uploadingFile.status === 'uploading' && /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"indeterminate\",\n                sx: {\n                  height: 6,\n                  borderRadius: 3\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 23\n              }, this), uploadingFile.status === 'success' && /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 31\n                }, this),\n                label: \"Success\",\n                color: \"success\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 23\n              }, this), uploadingFile.status === 'error' && /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(ErrorIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 31\n                }, this),\n                label: \"Error\",\n                color: \"error\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 17\n          }, this), uploadingFile.error && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mt: 1\n            },\n            children: uploadingFile.error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 19\n          }, this), uploadingFile.warnings && uploadingFile.warnings.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mt: 1\n            },\n            children: uploadingFile.warnings.join(', ')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 9\n    }, this), uploadedFiles.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: [\"Uploaded Files (\", uploadedFiles.length, \"/\", settings.max_files_per_order, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n          gap: 2\n        },\n        children: uploadedFiles.map(file => /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 1,\n              mb: 1,\n              children: [getFileIcon(file.mime_type), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: getStatusIcon(file),\n                children: getStatusIcon(file)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                noWrap: true,\n                sx: {\n                  flex: 1\n                },\n                children: file.original_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              display: \"block\",\n              children: [\"Size: \", file.formatted_file_size]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              display: \"block\",\n              children: [\"Type: \", file.file_type_label]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 19\n            }, this), file.dimensions && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              display: \"block\",\n              children: [\"Dimensions: \", file.dimensions.width, \"\\xD7\", file.dimensions.height, \"px\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 21\n            }, this), file.dpi && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: file.dpi >= (settings.min_dpi_requirement || 300) ? 'success.main' : file.dpi >= (settings.dpi_warning_threshold || 150) ? 'warning.main' : 'error.main',\n              display: \"block\",\n              children: [\"DPI: \", file.dpi, \" \", file.dpi_status && `(${file.dpi_status})`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 21\n            }, this), file.notes && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              display: \"block\",\n              sx: {\n                mt: 1\n              },\n              children: [\"Notes: \", file.notes]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              gap: 1,\n              mt: 1,\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: file.is_approved ? 'Approved' : 'Pending Review',\n                color: file.is_approved ? 'success' : 'warning',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              href: file.file_url,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              children: \"View\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              color: \"error\",\n              onClick: () => {\n                setFileToDelete(file);\n                setDeleteDialogOpen(true);\n              },\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 17\n          }, this)]\n        }, file.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: () => setDeleteDialogOpen(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Delete File\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Are you sure you want to delete \\\"\", fileToDelete === null || fileToDelete === void 0 ? void 0 : fileToDelete.original_name, \"\\\"? This action cannot be undone.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDeleteDialogOpen(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteFile,\n          color: \"error\",\n          variant: \"contained\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 308,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUpload, \"0J3MDxy5WHrG1dJIKHbn3stw5f4=\", false, function () {\n  return [useDropzone];\n});\n_c = FileUpload;\nexport default FileUpload;\nvar _c;\n$RefreshReg$(_c, \"FileUpload\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Typography", "Paper", "<PERSON><PERSON>", "LinearProgress", "<PERSON><PERSON>", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON><PERSON>", "CloudUpload", "UploadIcon", "Delete", "DeleteIcon", "Warning", "WarningIcon", "CheckCircle", "CheckIcon", "Error", "ErrorIcon", "Image", "ImageIcon", "PictureAsPdf", "PdfIcon", "Description", "FileIcon", "useDropzone", "printingService", "jsxDEV", "_jsxDEV", "FileUpload", "orderId", "onFilesUploaded", "onError", "_s", "settings", "setSettings", "uploadedFiles", "setUploadedFiles", "uploadingFiles", "setUploadingFiles", "loading", "setLoading", "deleteDialogOpen", "setDeleteDialogOpen", "fileToDelete", "setFileToDelete", "selectedFileType", "setSelectedFileType", "loadSettings", "loadUploadedFiles", "uploadSettings", "getUploadSettings", "Array", "isArray", "allowed_file_types", "error", "console", "max_file_size_mb", "max_total_upload_size_mb", "max_files_per_order", "min_dpi_requirement", "dpi_warning_threshold", "enable_dpi_validation", "min_width_px", "min_height_px", "max_width_px", "max_height_px", "enable_dimension_validation", "files", "getOrderFiles", "validateFiles", "valid", "invalid", "totalFiles", "length", "excess", "i", "push", "file", "errors", "slice", "currentTotalSize", "reduce", "sum", "file_size", "newFilesSize", "size", "totalSize", "maxTotalSize", "map", "validation", "validateFile", "message", "onDrop", "acceptedFiles", "errorMessages", "name", "join", "uploadFiles", "newUploadingFiles", "progress", "status", "uploadedFileResults", "prev", "uf", "setTimeout", "handleDeleteFile", "deleteFile", "id", "filter", "f", "getRootProps", "getInputProps", "isDragActive", "accept", "type", "includes", "undefined", "maxSize", "disabled", "getFileIcon", "mimeType", "startsWith", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusIcon", "dpi", "color", "is_approved", "formatFileSize", "bytes", "units", "unitIndex", "toFixed", "display", "justifyContent", "alignItems", "minHeight", "children", "severity", "sx", "mb", "variant", "toUpperCase", "fullWidth", "value", "onChange", "e", "target", "label", "p", "border", "borderColor", "backgroundColor", "cursor", "opacity", "textAlign", "transition", "fontSize", "gutterBottom", "mt", "uploadingFile", "index", "py", "gap", "flex", "noWrap", "width", "height", "borderRadius", "icon", "warnings", "gridTemplateColumns", "mime_type", "title", "original_name", "formatted_file_size", "file_type_label", "dimensions", "dpi_status", "notes", "href", "file_url", "rel", "onClick", "open", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/FileUpload.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  LinearProgress,\n  Alert,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n\n  Card,\n  CardContent,\n  CardActions,\n  Tooltip,\n} from '@mui/material';\nimport {\n  CloudUpload as UploadIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckIcon,\n  Error as ErrorIcon,\n  Image as ImageIcon,\n  PictureAsPdf as PdfIcon,\n  Description as FileIcon,\n} from '@mui/icons-material';\nimport { useDropzone } from 'react-dropzone';\nimport printingService, { OrderFile } from '../../services/printingService';\n\ninterface FileUploadSettings {\n  allowed_file_types: string[];\n  max_file_size_mb: number;\n  max_total_upload_size_mb: number;\n  max_files_per_order: number;\n  min_dpi_requirement: number;\n  dpi_warning_threshold: number;\n  enable_dpi_validation: boolean;\n  min_width_px: number;\n  min_height_px: number;\n  max_width_px: number;\n  max_height_px: number;\n  enable_dimension_validation: boolean;\n}\n\ninterface FileUploadProps {\n  orderId?: number;\n  onFilesUploaded?: (files: OrderFile[]) => void;\n  onError?: (error: string) => void;\n}\n\ninterface UploadingFile {\n  file: File;\n  progress: number;\n  status: 'uploading' | 'success' | 'error';\n  error?: string;\n  warnings?: string[];\n}\n\nconst FileUpload: React.FC<FileUploadProps> = ({ orderId, onFilesUploaded, onError }) => {\n  const [settings, setSettings] = useState<FileUploadSettings | null>(null);\n  const [uploadedFiles, setUploadedFiles] = useState<OrderFile[]>([]);\n  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [fileToDelete, setFileToDelete] = useState<OrderFile | null>(null);\n  const [selectedFileType, setSelectedFileType] = useState<string>('artwork');\n\n  // Load settings and files on component mount\n  useEffect(() => {\n    loadSettings();\n    if (orderId) {\n      loadUploadedFiles();\n    }\n  }, [orderId]);\n\n  const loadSettings = async () => {\n    try {\n      const uploadSettings = await printingService.getUploadSettings();\n\n      // Ensure allowed_file_types is always an array\n      if (uploadSettings && !Array.isArray(uploadSettings.allowed_file_types)) {\n        uploadSettings.allowed_file_types = ['pdf', 'png', 'jpg', 'jpeg'];\n      }\n\n      setSettings(uploadSettings);\n    } catch (error) {\n      console.error('Failed to load upload settings:', error);\n      onError?.('Failed to load upload settings');\n\n      // Set default settings if loading fails\n      setSettings({\n        allowed_file_types: ['pdf', 'png', 'jpg', 'jpeg'],\n        max_file_size_mb: 50,\n        max_total_upload_size_mb: 200,\n        max_files_per_order: 10,\n        min_dpi_requirement: 300,\n        dpi_warning_threshold: 150,\n        enable_dpi_validation: true,\n        min_width_px: 100,\n        min_height_px: 100,\n        max_width_px: 10000,\n        max_height_px: 10000,\n        enable_dimension_validation: true,\n      });\n    }\n  };\n\n  const loadUploadedFiles = async () => {\n    if (!orderId) return;\n\n    try {\n      const files = await printingService.getOrderFiles(orderId);\n      setUploadedFiles(files);\n    } catch (error) {\n      console.error('Failed to load uploaded files:', error);\n    }\n  };\n\n  // Validate files before upload\n  const validateFiles = async (files: File[]): Promise<{ valid: File[]; invalid: { file: File; errors: string[] }[] }> => {\n    if (!settings) return { valid: [], invalid: [] };\n\n    const valid: File[] = [];\n    const invalid: { file: File; errors: string[] }[] = [];\n\n    // Check total file count\n    const totalFiles = uploadedFiles.length + files.length;\n    if (totalFiles > settings.max_files_per_order) {\n      const excess = totalFiles - settings.max_files_per_order;\n      for (let i = files.length - excess; i < files.length; i++) {\n        invalid.push({\n          file: files[i],\n          errors: [`Maximum ${settings.max_files_per_order} files allowed per order`]\n        });\n      }\n      files = files.slice(0, files.length - excess);\n    }\n\n    // Check total upload size\n    const currentTotalSize = uploadedFiles.reduce((sum, file) => sum + file.file_size, 0);\n    const newFilesSize = files.reduce((sum, file) => sum + file.size, 0);\n    const totalSize = currentTotalSize + newFilesSize;\n    const maxTotalSize = settings.max_total_upload_size_mb * 1024 * 1024;\n\n    if (totalSize > maxTotalSize) {\n      onError?.(`Total upload size would exceed ${settings.max_total_upload_size_mb}MB limit`);\n      return { valid: [], invalid: files.map(file => ({ file, errors: ['Total size limit exceeded'] })) };\n    }\n\n    // Validate each file\n    for (const file of files) {\n      const validation = await printingService.validateFile(file);\n      if (validation.valid) {\n        valid.push(file);\n      } else {\n        invalid.push({\n          file,\n          errors: validation.message ? [validation.message] : ['Validation failed']\n        });\n      }\n    }\n\n    return { valid, invalid };\n  };\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    if (!orderId) {\n      onError?.('Please create an order first before uploading files');\n      return;\n    }\n\n    const { valid, invalid } = await validateFiles(acceptedFiles);\n\n    // Show errors for invalid files\n    if (invalid.length > 0) {\n      const errorMessages = invalid.map(({ file, errors }) =>\n        `${file.name}: ${errors.join(', ')}`\n      ).join('\\n');\n      onError?.(errorMessages);\n    }\n\n    // Upload valid files\n    if (valid.length > 0) {\n      await uploadFiles(valid);\n    }\n  }, [orderId, settings, uploadedFiles]);\n\n  const uploadFiles = async (files: File[]) => {\n    setLoading(true);\n\n    // Initialize uploading files state\n    const newUploadingFiles: UploadingFile[] = files.map(file => ({\n      file,\n      progress: 0,\n      status: 'uploading'\n    }));\n    setUploadingFiles(newUploadingFiles);\n\n    try {\n      const uploadedFileResults = await printingService.uploadFiles(orderId!, files, selectedFileType);\n\n      // Update uploading files to success\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        progress: 100,\n        status: 'success'\n      })));\n\n      // Add to uploaded files list\n      setUploadedFiles(prev => [...prev, ...uploadedFileResults]);\n      onFilesUploaded?.(uploadedFileResults);\n\n      // Clear uploading files after a delay\n      setTimeout(() => {\n        setUploadingFiles([]);\n      }, 2000);\n\n    } catch (error: any) {\n      // Update uploading files to error\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        status: 'error',\n        error: error.message || 'Upload failed'\n      })));\n\n      onError?.(error.message || 'Failed to upload files');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteFile = async () => {\n    if (!fileToDelete || !orderId) return;\n\n    try {\n      await printingService.deleteFile(orderId, fileToDelete.id);\n      setUploadedFiles(prev => prev.filter(f => f.id !== fileToDelete.id));\n      setDeleteDialogOpen(false);\n      setFileToDelete(null);\n    } catch (error: any) {\n      onError?.(error.message || 'Failed to delete file');\n    }\n  };\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: settings && Array.isArray(settings.allowed_file_types) ? {\n      'image/*': settings.allowed_file_types.filter(type =>\n        ['png', 'jpg', 'jpeg', 'tiff', 'svg'].includes(type)\n      ).map(type => `.${type}`),\n      'application/pdf': settings.allowed_file_types.includes('pdf') ? ['.pdf'] : [],\n      'application/postscript': settings.allowed_file_types.includes('eps') ? ['.eps'] : [],\n      'application/illustrator': settings.allowed_file_types.includes('ai') ? ['.ai'] : [],\n    } : undefined,\n    maxSize: settings ? settings.max_file_size_mb * 1024 * 1024 : undefined,\n    disabled: loading || !orderId\n  });\n\n  const getFileIcon = (mimeType: string) => {\n    if (mimeType.startsWith('image/')) return <ImageIcon />;\n    if (mimeType === 'application/pdf') return <PdfIcon />;\n    return <FileIcon />;\n  };\n\n  const getStatusIcon = (file: OrderFile) => {\n    if (file.dpi && settings?.enable_dpi_validation) {\n      if (file.dpi >= settings.min_dpi_requirement) {\n        return <CheckIcon color=\"success\" />;\n      } else if (file.dpi >= settings.dpi_warning_threshold) {\n        return <WarningIcon color=\"warning\" />;\n      } else {\n        return <ErrorIcon color=\"error\" />;\n      }\n    }\n    return file.is_approved ? <CheckIcon color=\"success\" /> : <WarningIcon color=\"warning\" />;\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let size = bytes;\n    let unitIndex = 0;\n\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  };\n\n  if (!settings) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"200px\">\n        <Typography>Loading upload settings...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Upload Settings Info */}\n      <Alert severity=\"info\" sx={{ mb: 2 }}>\n        <Typography variant=\"body2\">\n          <strong>Upload Limits:</strong> Max {settings.max_files_per_order} files,\n          {settings.max_file_size_mb}MB per file, {settings.max_total_upload_size_mb}MB total.\n          Allowed types: {settings.allowed_file_types.join(', ').toUpperCase()}\n        </Typography>\n      </Alert>\n\n      {/* File Type Selection */}\n      <FormControl fullWidth sx={{ mb: 2 }}>\n        <InputLabel>File Type</InputLabel>\n        <Select\n          value={selectedFileType}\n          onChange={(e) => setSelectedFileType(e.target.value)}\n          label=\"File Type\"\n        >\n          <MenuItem value=\"artwork\">Artwork Files</MenuItem>\n          <MenuItem value=\"reference\">Reference Files</MenuItem>\n          <MenuItem value=\"proof\">Proof Files</MenuItem>\n        </Select>\n      </FormControl>\n\n      {/* Drag and Drop Upload Area */}\n      <Paper\n        {...getRootProps()}\n        sx={{\n          p: 4,\n          border: '2px dashed',\n          borderColor: isDragActive ? 'primary.main' : 'grey.300',\n          backgroundColor: isDragActive ? 'action.hover' : 'background.paper',\n          cursor: orderId ? 'pointer' : 'not-allowed',\n          opacity: orderId ? 1 : 0.5,\n          textAlign: 'center',\n          mb: 3,\n          transition: 'all 0.2s ease-in-out',\n        }}\n      >\n        <input {...getInputProps()} />\n        <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />\n        <Typography variant=\"h6\" gutterBottom>\n          {isDragActive ? 'Drop files here' : 'Drag & drop files here'}\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          or click to browse files\n        </Typography>\n        <Button variant=\"outlined\" disabled={!orderId}>\n          Browse Files\n        </Button>\n        {!orderId && (\n          <Typography variant=\"caption\" color=\"error\" sx={{ display: 'block', mt: 1 }}>\n            Please create an order first\n          </Typography>\n        )}\n      </Paper>\n\n      {/* Uploading Files */}\n      {uploadingFiles.length > 0 && (\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Uploading Files\n          </Typography>\n          {uploadingFiles.map((uploadingFile, index) => (\n            <Card key={index} sx={{ mb: 1 }}>\n              <CardContent sx={{ py: 2 }}>\n                <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                  {getFileIcon(uploadingFile.file.type)}\n                  <Box sx={{ flex: 1 }}>\n                    <Typography variant=\"body2\" noWrap>\n                      {uploadingFile.file.name}\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      {formatFileSize(uploadingFile.file.size)}\n                    </Typography>\n                  </Box>\n                  <Box sx={{ width: 100 }}>\n                    {uploadingFile.status === 'uploading' && (\n                      <LinearProgress\n                        variant=\"indeterminate\"\n                        sx={{ height: 6, borderRadius: 3 }}\n                      />\n                    )}\n                    {uploadingFile.status === 'success' && (\n                      <Chip\n                        icon={<CheckIcon />}\n                        label=\"Success\"\n                        color=\"success\"\n                        size=\"small\"\n                      />\n                    )}\n                    {uploadingFile.status === 'error' && (\n                      <Chip\n                        icon={<ErrorIcon />}\n                        label=\"Error\"\n                        color=\"error\"\n                        size=\"small\"\n                      />\n                    )}\n                  </Box>\n                </Box>\n                {uploadingFile.error && (\n                  <Alert severity=\"error\" sx={{ mt: 1 }}>\n                    {uploadingFile.error}\n                  </Alert>\n                )}\n                {uploadingFile.warnings && uploadingFile.warnings.length > 0 && (\n                  <Alert severity=\"warning\" sx={{ mt: 1 }}>\n                    {uploadingFile.warnings.join(', ')}\n                  </Alert>\n                )}\n              </CardContent>\n            </Card>\n          ))}\n        </Box>\n      )}\n\n      {/* Uploaded Files List */}\n      {uploadedFiles.length > 0 && (\n        <Box>\n          <Typography variant=\"h6\" gutterBottom>\n            Uploaded Files ({uploadedFiles.length}/{settings.max_files_per_order})\n          </Typography>\n          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: 2 }}>\n            {uploadedFiles.map((file) => (\n              <Card key={file.id}>\n                <CardContent>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1} mb={1}>\n                    {getFileIcon(file.mime_type)}\n                    <Tooltip title={getStatusIcon(file)}>\n                      {getStatusIcon(file)}\n                    </Tooltip>\n                    <Typography variant=\"body2\" noWrap sx={{ flex: 1 }}>\n                      {file.original_name}\n                    </Typography>\n                  </Box>\n\n                  <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                    Size: {file.formatted_file_size}\n                  </Typography>\n\n                  <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                    Type: {file.file_type_label}\n                  </Typography>\n\n                  {file.dimensions && (\n                    <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                      Dimensions: {file.dimensions.width}×{file.dimensions.height}px\n                    </Typography>\n                  )}\n\n                  {file.dpi && (\n                    <Typography\n                      variant=\"caption\"\n                      color={\n                        file.dpi >= (settings.min_dpi_requirement || 300) ? 'success.main' :\n                        file.dpi >= (settings.dpi_warning_threshold || 150) ? 'warning.main' : 'error.main'\n                      }\n                      display=\"block\"\n                    >\n                      DPI: {file.dpi} {file.dpi_status && `(${file.dpi_status})`}\n                    </Typography>\n                  )}\n\n                  {file.notes && (\n                    <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" sx={{ mt: 1 }}>\n                      Notes: {file.notes}\n                    </Typography>\n                  )}\n\n                  <Box display=\"flex\" gap={1} mt={1}>\n                    <Chip\n                      label={file.is_approved ? 'Approved' : 'Pending Review'}\n                      color={file.is_approved ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  </Box>\n                </CardContent>\n\n                <CardActions>\n                  <Button\n                    size=\"small\"\n                    href={file.file_url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    View\n                  </Button>\n                  <IconButton\n                    size=\"small\"\n                    color=\"error\"\n                    onClick={() => {\n                      setFileToDelete(file);\n                      setDeleteDialogOpen(true);\n                    }}\n                  >\n                    <DeleteIcon />\n                  </IconButton>\n                </CardActions>\n              </Card>\n            ))}\n          </Box>\n        </Box>\n      )}\n\n      {/* Delete Confirmation Dialog */}\n      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>\n        <DialogTitle>Delete File</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Are you sure you want to delete \"{fileToDelete?.original_name}\"? This action cannot be undone.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>\n          <Button onClick={handleDeleteFile} color=\"error\" variant=\"contained\">\n            Delete\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default FileUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,cAAc,EACdC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EAERC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,OAAO,QACF,eAAe;AACtB,SACEC,WAAW,IAAIC,UAAU,EACzBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,SAAS,EACxBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,YAAY,IAAIC,OAAO,EACvBC,WAAW,IAAIC,QAAQ,QAClB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,eAAe,MAAqB,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA+B5E,MAAMC,UAAqC,GAAGA,CAAC;EAAEC,OAAO;EAAEC,eAAe;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAA4B,IAAI,CAAC;EACzE,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAc,EAAE,CAAC;EACnE,MAAM,CAACoD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0D,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAmB,IAAI,CAAC;EACxE,MAAM,CAAC4D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7D,QAAQ,CAAS,SAAS,CAAC;;EAE3E;EACAC,SAAS,CAAC,MAAM;IACd6D,YAAY,CAAC,CAAC;IACd,IAAIlB,OAAO,EAAE;MACXmB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACnB,OAAO,CAAC,CAAC;EAEb,MAAMkB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAME,cAAc,GAAG,MAAMxB,eAAe,CAACyB,iBAAiB,CAAC,CAAC;;MAEhE;MACA,IAAID,cAAc,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,cAAc,CAACI,kBAAkB,CAAC,EAAE;QACvEJ,cAAc,CAACI,kBAAkB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;MACnE;MAEAnB,WAAW,CAACe,cAAc,CAAC;IAC7B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,gCAAgC,CAAC;;MAE3C;MACAG,WAAW,CAAC;QACVmB,kBAAkB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;QACjDG,gBAAgB,EAAE,EAAE;QACpBC,wBAAwB,EAAE,GAAG;QAC7BC,mBAAmB,EAAE,EAAE;QACvBC,mBAAmB,EAAE,GAAG;QACxBC,qBAAqB,EAAE,GAAG;QAC1BC,qBAAqB,EAAE,IAAI;QAC3BC,YAAY,EAAE,GAAG;QACjBC,aAAa,EAAE,GAAG;QAClBC,YAAY,EAAE,KAAK;QACnBC,aAAa,EAAE,KAAK;QACpBC,2BAA2B,EAAE;MAC/B,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMlB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACnB,OAAO,EAAE;IAEd,IAAI;MACF,MAAMsC,KAAK,GAAG,MAAM1C,eAAe,CAAC2C,aAAa,CAACvC,OAAO,CAAC;MAC1DO,gBAAgB,CAAC+B,KAAK,CAAC;IACzB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMe,aAAa,GAAG,MAAOF,KAAa,IAA8E;IACtH,IAAI,CAAClC,QAAQ,EAAE,OAAO;MAAEqC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC;IAEhD,MAAMD,KAAa,GAAG,EAAE;IACxB,MAAMC,OAA2C,GAAG,EAAE;;IAEtD;IACA,MAAMC,UAAU,GAAGrC,aAAa,CAACsC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACtD,IAAID,UAAU,GAAGvC,QAAQ,CAACyB,mBAAmB,EAAE;MAC7C,MAAMgB,MAAM,GAAGF,UAAU,GAAGvC,QAAQ,CAACyB,mBAAmB;MACxD,KAAK,IAAIiB,CAAC,GAAGR,KAAK,CAACM,MAAM,GAAGC,MAAM,EAAEC,CAAC,GAAGR,KAAK,CAACM,MAAM,EAAEE,CAAC,EAAE,EAAE;QACzDJ,OAAO,CAACK,IAAI,CAAC;UACXC,IAAI,EAAEV,KAAK,CAACQ,CAAC,CAAC;UACdG,MAAM,EAAE,CAAC,WAAW7C,QAAQ,CAACyB,mBAAmB,0BAA0B;QAC5E,CAAC,CAAC;MACJ;MACAS,KAAK,GAAGA,KAAK,CAACY,KAAK,CAAC,CAAC,EAAEZ,KAAK,CAACM,MAAM,GAAGC,MAAM,CAAC;IAC/C;;IAEA;IACA,MAAMM,gBAAgB,GAAG7C,aAAa,CAAC8C,MAAM,CAAC,CAACC,GAAG,EAAEL,IAAI,KAAKK,GAAG,GAAGL,IAAI,CAACM,SAAS,EAAE,CAAC,CAAC;IACrF,MAAMC,YAAY,GAAGjB,KAAK,CAACc,MAAM,CAAC,CAACC,GAAG,EAAEL,IAAI,KAAKK,GAAG,GAAGL,IAAI,CAACQ,IAAI,EAAE,CAAC,CAAC;IACpE,MAAMC,SAAS,GAAGN,gBAAgB,GAAGI,YAAY;IACjD,MAAMG,YAAY,GAAGtD,QAAQ,CAACwB,wBAAwB,GAAG,IAAI,GAAG,IAAI;IAEpE,IAAI6B,SAAS,GAAGC,YAAY,EAAE;MAC5BxD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,kCAAkCE,QAAQ,CAACwB,wBAAwB,UAAU,CAAC;MACxF,OAAO;QAAEa,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAEJ,KAAK,CAACqB,GAAG,CAACX,IAAI,KAAK;UAAEA,IAAI;UAAEC,MAAM,EAAE,CAAC,2BAA2B;QAAE,CAAC,CAAC;MAAE,CAAC;IACrG;;IAEA;IACA,KAAK,MAAMD,IAAI,IAAIV,KAAK,EAAE;MACxB,MAAMsB,UAAU,GAAG,MAAMhE,eAAe,CAACiE,YAAY,CAACb,IAAI,CAAC;MAC3D,IAAIY,UAAU,CAACnB,KAAK,EAAE;QACpBA,KAAK,CAACM,IAAI,CAACC,IAAI,CAAC;MAClB,CAAC,MAAM;QACLN,OAAO,CAACK,IAAI,CAAC;UACXC,IAAI;UACJC,MAAM,EAAEW,UAAU,CAACE,OAAO,GAAG,CAACF,UAAU,CAACE,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAC1E,CAAC,CAAC;MACJ;IACF;IAEA,OAAO;MAAErB,KAAK;MAAEC;IAAQ,CAAC;EAC3B,CAAC;EAED,MAAMqB,MAAM,GAAGzG,WAAW,CAAC,MAAO0G,aAAqB,IAAK;IAC1D,IAAI,CAAChE,OAAO,EAAE;MACZE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,qDAAqD,CAAC;MAChE;IACF;IAEA,MAAM;MAAEuC,KAAK;MAAEC;IAAQ,CAAC,GAAG,MAAMF,aAAa,CAACwB,aAAa,CAAC;;IAE7D;IACA,IAAItB,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMqB,aAAa,GAAGvB,OAAO,CAACiB,GAAG,CAAC,CAAC;QAAEX,IAAI;QAAEC;MAAO,CAAC,KACjD,GAAGD,IAAI,CAACkB,IAAI,KAAKjB,MAAM,CAACkB,IAAI,CAAC,IAAI,CAAC,EACpC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;MACZjE,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG+D,aAAa,CAAC;IAC1B;;IAEA;IACA,IAAIxB,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MACpB,MAAMwB,WAAW,CAAC3B,KAAK,CAAC;IAC1B;EACF,CAAC,EAAE,CAACzC,OAAO,EAAEI,QAAQ,EAAEE,aAAa,CAAC,CAAC;EAEtC,MAAM8D,WAAW,GAAG,MAAO9B,KAAa,IAAK;IAC3C3B,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACA,MAAM0D,iBAAkC,GAAG/B,KAAK,CAACqB,GAAG,CAACX,IAAI,KAAK;MAC5DA,IAAI;MACJsB,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IACH9D,iBAAiB,CAAC4D,iBAAiB,CAAC;IAEpC,IAAI;MACF,MAAMG,mBAAmB,GAAG,MAAM5E,eAAe,CAACwE,WAAW,CAACpE,OAAO,EAAGsC,KAAK,EAAEtB,gBAAgB,CAAC;;MAEhG;MACAP,iBAAiB,CAACgE,IAAI,IAAIA,IAAI,CAACd,GAAG,CAACe,EAAE,KAAK;QACxC,GAAGA,EAAE;QACLJ,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC,CAAC;;MAEJ;MACAhE,gBAAgB,CAACkE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGD,mBAAmB,CAAC,CAAC;MAC3DvE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGuE,mBAAmB,CAAC;;MAEtC;MACAG,UAAU,CAAC,MAAM;QACflE,iBAAiB,CAAC,EAAE,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOgB,KAAU,EAAE;MACnB;MACAhB,iBAAiB,CAACgE,IAAI,IAAIA,IAAI,CAACd,GAAG,CAACe,EAAE,KAAK;QACxC,GAAGA,EAAE;QACLH,MAAM,EAAE,OAAO;QACf9C,KAAK,EAAEA,KAAK,CAACqC,OAAO,IAAI;MAC1B,CAAC,CAAC,CAAC,CAAC;MAEJ5D,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGuB,KAAK,CAACqC,OAAO,IAAI,wBAAwB,CAAC;IACtD,CAAC,SAAS;MACRnD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC9D,YAAY,IAAI,CAACd,OAAO,EAAE;IAE/B,IAAI;MACF,MAAMJ,eAAe,CAACiF,UAAU,CAAC7E,OAAO,EAAEc,YAAY,CAACgE,EAAE,CAAC;MAC1DvE,gBAAgB,CAACkE,IAAI,IAAIA,IAAI,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKhE,YAAY,CAACgE,EAAE,CAAC,CAAC;MACpEjE,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOU,KAAU,EAAE;MACnBvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGuB,KAAK,CAACqC,OAAO,IAAI,uBAAuB,CAAC;IACrD;EACF,CAAC;EAED,MAAM;IAAEmB,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGxF,WAAW,CAAC;IAChEoE,MAAM;IACNqB,MAAM,EAAEhF,QAAQ,IAAIkB,KAAK,CAACC,OAAO,CAACnB,QAAQ,CAACoB,kBAAkB,CAAC,GAAG;MAC/D,SAAS,EAAEpB,QAAQ,CAACoB,kBAAkB,CAACuD,MAAM,CAACM,IAAI,IAChD,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,IAAI,CACrD,CAAC,CAAC1B,GAAG,CAAC0B,IAAI,IAAI,IAAIA,IAAI,EAAE,CAAC;MACzB,iBAAiB,EAAEjF,QAAQ,CAACoB,kBAAkB,CAAC8D,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE;MAC9E,wBAAwB,EAAElF,QAAQ,CAACoB,kBAAkB,CAAC8D,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE;MACrF,yBAAyB,EAAElF,QAAQ,CAACoB,kBAAkB,CAAC8D,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG;IACpF,CAAC,GAAGC,SAAS;IACbC,OAAO,EAAEpF,QAAQ,GAAGA,QAAQ,CAACuB,gBAAgB,GAAG,IAAI,GAAG,IAAI,GAAG4D,SAAS;IACvEE,QAAQ,EAAE/E,OAAO,IAAI,CAACV;EACxB,CAAC,CAAC;EAEF,MAAM0F,WAAW,GAAIC,QAAgB,IAAK;IACxC,IAAIA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE,oBAAO9F,OAAA,CAACR,SAAS;MAAAuG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvD,IAAIL,QAAQ,KAAK,iBAAiB,EAAE,oBAAO7F,OAAA,CAACN,OAAO;MAAAqG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtD,oBAAOlG,OAAA,CAACJ,QAAQ;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrB,CAAC;EAED,MAAMC,aAAa,GAAIjD,IAAe,IAAK;IACzC,IAAIA,IAAI,CAACkD,GAAG,IAAI9F,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAE4B,qBAAqB,EAAE;MAC/C,IAAIgB,IAAI,CAACkD,GAAG,IAAI9F,QAAQ,CAAC0B,mBAAmB,EAAE;QAC5C,oBAAOhC,OAAA,CAACZ,SAAS;UAACiH,KAAK,EAAC;QAAS;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtC,CAAC,MAAM,IAAIhD,IAAI,CAACkD,GAAG,IAAI9F,QAAQ,CAAC2B,qBAAqB,EAAE;QACrD,oBAAOjC,OAAA,CAACd,WAAW;UAACmH,KAAK,EAAC;QAAS;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxC,CAAC,MAAM;QACL,oBAAOlG,OAAA,CAACV,SAAS;UAAC+G,KAAK,EAAC;QAAO;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpC;IACF;IACA,OAAOhD,IAAI,CAACoD,WAAW,gBAAGtG,OAAA,CAACZ,SAAS;MAACiH,KAAK,EAAC;IAAS;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGlG,OAAA,CAACd,WAAW;MAACmH,KAAK,EAAC;IAAS;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3F,CAAC;EAED,MAAMK,cAAc,GAAIC,KAAa,IAAa;IAChD,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,IAAI/C,IAAI,GAAG8C,KAAK;IAChB,IAAIE,SAAS,GAAG,CAAC;IAEjB,OAAOhD,IAAI,IAAI,IAAI,IAAIgD,SAAS,GAAGD,KAAK,CAAC3D,MAAM,GAAG,CAAC,EAAE;MACnDY,IAAI,IAAI,IAAI;MACZgD,SAAS,EAAE;IACb;IAEA,OAAO,GAAGhD,IAAI,CAACiD,OAAO,CAAC,CAAC,CAAC,IAAIF,KAAK,CAACC,SAAS,CAAC,EAAE;EACjD,CAAC;EAED,IAAI,CAACpG,QAAQ,EAAE;IACb,oBACEN,OAAA,CAACvC,GAAG;MAACmJ,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/EhH,OAAA,CAACtC,UAAU;QAAAsJ,QAAA,EAAC;MAA0B;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACElG,OAAA,CAACvC,GAAG;IAAAuJ,QAAA,gBAEFhH,OAAA,CAAClC,KAAK;MAACmJ,QAAQ,EAAC,MAAM;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eACnChH,OAAA,CAACtC,UAAU;QAAC0J,OAAO,EAAC,OAAO;QAAAJ,QAAA,gBACzBhH,OAAA;UAAAgH,QAAA,EAAQ;QAAc;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,SAAK,EAAC5F,QAAQ,CAACyB,mBAAmB,EAAC,SAClE,EAACzB,QAAQ,CAACuB,gBAAgB,EAAC,eAAa,EAACvB,QAAQ,CAACwB,wBAAwB,EAAC,2BAC5D,EAACxB,QAAQ,CAACoB,kBAAkB,CAAC2C,IAAI,CAAC,IAAI,CAAC,CAACgD,WAAW,CAAC,CAAC;MAAA;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGRlG,OAAA,CAAC3B,WAAW;MAACiJ,SAAS;MAACJ,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACnChH,OAAA,CAAC1B,UAAU;QAAA0I,QAAA,EAAC;MAAS;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAClClG,OAAA,CAACzB,MAAM;QACLgJ,KAAK,EAAErG,gBAAiB;QACxBsG,QAAQ,EAAGC,CAAC,IAAKtG,mBAAmB,CAACsG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QACrDI,KAAK,EAAC,WAAW;QAAAX,QAAA,gBAEjBhH,OAAA,CAACxB,QAAQ;UAAC+I,KAAK,EAAC,SAAS;UAAAP,QAAA,EAAC;QAAa;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAClDlG,OAAA,CAACxB,QAAQ;UAAC+I,KAAK,EAAC,WAAW;UAAAP,QAAA,EAAC;QAAe;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACtDlG,OAAA,CAACxB,QAAQ;UAAC+I,KAAK,EAAC,OAAO;UAAAP,QAAA,EAAC;QAAW;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGdlG,OAAA,CAACrC,KAAK;MAAA,GACAwH,YAAY,CAAC,CAAC;MAClB+B,EAAE,EAAE;QACFU,CAAC,EAAE,CAAC;QACJC,MAAM,EAAE,YAAY;QACpBC,WAAW,EAAEzC,YAAY,GAAG,cAAc,GAAG,UAAU;QACvD0C,eAAe,EAAE1C,YAAY,GAAG,cAAc,GAAG,kBAAkB;QACnE2C,MAAM,EAAE9H,OAAO,GAAG,SAAS,GAAG,aAAa;QAC3C+H,OAAO,EAAE/H,OAAO,GAAG,CAAC,GAAG,GAAG;QAC1BgI,SAAS,EAAE,QAAQ;QACnBf,EAAE,EAAE,CAAC;QACLgB,UAAU,EAAE;MACd,CAAE;MAAAnB,QAAA,gBAEFhH,OAAA;QAAA,GAAWoF,aAAa,CAAC;MAAC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC9BlG,OAAA,CAAClB,UAAU;QAACoI,EAAE,EAAE;UAAEkB,QAAQ,EAAE,EAAE;UAAE/B,KAAK,EAAE,gBAAgB;UAAEc,EAAE,EAAE;QAAE;MAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpElG,OAAA,CAACtC,UAAU;QAAC0J,OAAO,EAAC,IAAI;QAACiB,YAAY;QAAArB,QAAA,EAClC3B,YAAY,GAAG,iBAAiB,GAAG;MAAwB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACblG,OAAA,CAACtC,UAAU;QAAC0J,OAAO,EAAC,OAAO;QAACf,KAAK,EAAC,gBAAgB;QAACa,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAElE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblG,OAAA,CAACpC,MAAM;QAACwJ,OAAO,EAAC,UAAU;QAACzB,QAAQ,EAAE,CAACzF,OAAQ;QAAA8G,QAAA,EAAC;MAE/C;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR,CAAChG,OAAO,iBACPF,OAAA,CAACtC,UAAU;QAAC0J,OAAO,EAAC,SAAS;QAACf,KAAK,EAAC,OAAO;QAACa,EAAE,EAAE;UAAEN,OAAO,EAAE,OAAO;UAAE0B,EAAE,EAAE;QAAE,CAAE;QAAAtB,QAAA,EAAC;MAE7E;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGPxF,cAAc,CAACoC,MAAM,GAAG,CAAC,iBACxB9C,OAAA,CAACvC,GAAG;MAACyJ,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACjBhH,OAAA,CAACtC,UAAU;QAAC0J,OAAO,EAAC,IAAI;QAACiB,YAAY;QAAArB,QAAA,EAAC;MAEtC;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZxF,cAAc,CAACmD,GAAG,CAAC,CAAC0E,aAAa,EAAEC,KAAK,kBACvCxI,OAAA,CAACvB,IAAI;QAAayI,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,eAC9BhH,OAAA,CAACtB,WAAW;UAACwI,EAAE,EAAE;YAAEuB,EAAE,EAAE;UAAE,CAAE;UAAAzB,QAAA,gBACzBhH,OAAA,CAACvC,GAAG;YAACmJ,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAAC4B,GAAG,EAAE,CAAE;YAAA1B,QAAA,GAC5CpB,WAAW,CAAC2C,aAAa,CAACrF,IAAI,CAACqC,IAAI,CAAC,eACrCvF,OAAA,CAACvC,GAAG;cAACyJ,EAAE,EAAE;gBAAEyB,IAAI,EAAE;cAAE,CAAE;cAAA3B,QAAA,gBACnBhH,OAAA,CAACtC,UAAU;gBAAC0J,OAAO,EAAC,OAAO;gBAACwB,MAAM;gBAAA5B,QAAA,EAC/BuB,aAAa,CAACrF,IAAI,CAACkB;cAAI;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACblG,OAAA,CAACtC,UAAU;gBAAC0J,OAAO,EAAC,SAAS;gBAACf,KAAK,EAAC,gBAAgB;gBAAAW,QAAA,EACjDT,cAAc,CAACgC,aAAa,CAACrF,IAAI,CAACQ,IAAI;cAAC;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlG,OAAA,CAACvC,GAAG;cAACyJ,EAAE,EAAE;gBAAE2B,KAAK,EAAE;cAAI,CAAE;cAAA7B,QAAA,GACrBuB,aAAa,CAAC9D,MAAM,KAAK,WAAW,iBACnCzE,OAAA,CAACnC,cAAc;gBACbuJ,OAAO,EAAC,eAAe;gBACvBF,EAAE,EAAE;kBAAE4B,MAAM,EAAE,CAAC;kBAAEC,YAAY,EAAE;gBAAE;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CACF,EACAqC,aAAa,CAAC9D,MAAM,KAAK,SAAS,iBACjCzE,OAAA,CAACjC,IAAI;gBACHiL,IAAI,eAAEhJ,OAAA,CAACZ,SAAS;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpByB,KAAK,EAAC,SAAS;gBACftB,KAAK,EAAC,SAAS;gBACf3C,IAAI,EAAC;cAAO;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACF,EACAqC,aAAa,CAAC9D,MAAM,KAAK,OAAO,iBAC/BzE,OAAA,CAACjC,IAAI;gBACHiL,IAAI,eAAEhJ,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpByB,KAAK,EAAC,OAAO;gBACbtB,KAAK,EAAC,OAAO;gBACb3C,IAAI,EAAC;cAAO;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLqC,aAAa,CAAC5G,KAAK,iBAClB3B,OAAA,CAAClC,KAAK;YAACmJ,QAAQ,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YAAAtB,QAAA,EACnCuB,aAAa,CAAC5G;UAAK;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACR,EACAqC,aAAa,CAACU,QAAQ,IAAIV,aAAa,CAACU,QAAQ,CAACnG,MAAM,GAAG,CAAC,iBAC1D9C,OAAA,CAAClC,KAAK;YAACmJ,QAAQ,EAAC,SAAS;YAACC,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YAAAtB,QAAA,EACrCuB,aAAa,CAACU,QAAQ,CAAC5E,IAAI,CAAC,IAAI;UAAC;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC,GA/CLsC,KAAK;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgDV,CACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGA1F,aAAa,CAACsC,MAAM,GAAG,CAAC,iBACvB9C,OAAA,CAACvC,GAAG;MAAAuJ,QAAA,gBACFhH,OAAA,CAACtC,UAAU;QAAC0J,OAAO,EAAC,IAAI;QAACiB,YAAY;QAAArB,QAAA,GAAC,kBACpB,EAACxG,aAAa,CAACsC,MAAM,EAAC,GAAC,EAACxC,QAAQ,CAACyB,mBAAmB,EAAC,GACvE;MAAA;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblG,OAAA,CAACvC,GAAG;QAACyJ,EAAE,EAAE;UAAEN,OAAO,EAAE,MAAM;UAAEsC,mBAAmB,EAAE,uCAAuC;UAAER,GAAG,EAAE;QAAE,CAAE;QAAA1B,QAAA,EAChGxG,aAAa,CAACqD,GAAG,CAAEX,IAAI,iBACtBlD,OAAA,CAACvB,IAAI;UAAAuI,QAAA,gBACHhH,OAAA,CAACtB,WAAW;YAAAsI,QAAA,gBACVhH,OAAA,CAACvC,GAAG;cAACmJ,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAAC4B,GAAG,EAAE,CAAE;cAACvB,EAAE,EAAE,CAAE;cAAAH,QAAA,GACnDpB,WAAW,CAAC1C,IAAI,CAACiG,SAAS,CAAC,eAC5BnJ,OAAA,CAACpB,OAAO;gBAACwK,KAAK,EAAEjD,aAAa,CAACjD,IAAI,CAAE;gBAAA8D,QAAA,EACjCb,aAAa,CAACjD,IAAI;cAAC;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACVlG,OAAA,CAACtC,UAAU;gBAAC0J,OAAO,EAAC,OAAO;gBAACwB,MAAM;gBAAC1B,EAAE,EAAE;kBAAEyB,IAAI,EAAE;gBAAE,CAAE;gBAAA3B,QAAA,EAChD9D,IAAI,CAACmG;cAAa;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENlG,OAAA,CAACtC,UAAU;cAAC0J,OAAO,EAAC,SAAS;cAACf,KAAK,EAAC,gBAAgB;cAACO,OAAO,EAAC,OAAO;cAAAI,QAAA,GAAC,QAC7D,EAAC9D,IAAI,CAACoG,mBAAmB;YAAA;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eAEblG,OAAA,CAACtC,UAAU;cAAC0J,OAAO,EAAC,SAAS;cAACf,KAAK,EAAC,gBAAgB;cAACO,OAAO,EAAC,OAAO;cAAAI,QAAA,GAAC,QAC7D,EAAC9D,IAAI,CAACqG,eAAe;YAAA;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EAEZhD,IAAI,CAACsG,UAAU,iBACdxJ,OAAA,CAACtC,UAAU;cAAC0J,OAAO,EAAC,SAAS;cAACf,KAAK,EAAC,gBAAgB;cAACO,OAAO,EAAC,OAAO;cAAAI,QAAA,GAAC,cACvD,EAAC9D,IAAI,CAACsG,UAAU,CAACX,KAAK,EAAC,MAAC,EAAC3F,IAAI,CAACsG,UAAU,CAACV,MAAM,EAAC,IAC9D;YAAA;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb,EAEAhD,IAAI,CAACkD,GAAG,iBACPpG,OAAA,CAACtC,UAAU;cACT0J,OAAO,EAAC,SAAS;cACjBf,KAAK,EACHnD,IAAI,CAACkD,GAAG,KAAK9F,QAAQ,CAAC0B,mBAAmB,IAAI,GAAG,CAAC,GAAG,cAAc,GAClEkB,IAAI,CAACkD,GAAG,KAAK9F,QAAQ,CAAC2B,qBAAqB,IAAI,GAAG,CAAC,GAAG,cAAc,GAAG,YACxE;cACD2E,OAAO,EAAC,OAAO;cAAAI,QAAA,GAChB,OACM,EAAC9D,IAAI,CAACkD,GAAG,EAAC,GAAC,EAAClD,IAAI,CAACuG,UAAU,IAAI,IAAIvG,IAAI,CAACuG,UAAU,GAAG;YAAA;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CACb,EAEAhD,IAAI,CAACwG,KAAK,iBACT1J,OAAA,CAACtC,UAAU;cAAC0J,OAAO,EAAC,SAAS;cAACf,KAAK,EAAC,gBAAgB;cAACO,OAAO,EAAC,OAAO;cAACM,EAAE,EAAE;gBAAEoB,EAAE,EAAE;cAAE,CAAE;cAAAtB,QAAA,GAAC,SAC3E,EAAC9D,IAAI,CAACwG,KAAK;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACb,eAEDlG,OAAA,CAACvC,GAAG;cAACmJ,OAAO,EAAC,MAAM;cAAC8B,GAAG,EAAE,CAAE;cAACJ,EAAE,EAAE,CAAE;cAAAtB,QAAA,eAChChH,OAAA,CAACjC,IAAI;gBACH4J,KAAK,EAAEzE,IAAI,CAACoD,WAAW,GAAG,UAAU,GAAG,gBAAiB;gBACxDD,KAAK,EAAEnD,IAAI,CAACoD,WAAW,GAAG,SAAS,GAAG,SAAU;gBAChD5C,IAAI,EAAC;cAAO;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEdlG,OAAA,CAACrB,WAAW;YAAAqI,QAAA,gBACVhH,OAAA,CAACpC,MAAM;cACL8F,IAAI,EAAC,OAAO;cACZiG,IAAI,EAAEzG,IAAI,CAAC0G,QAAS;cACpBlC,MAAM,EAAC,QAAQ;cACfmC,GAAG,EAAC,qBAAqB;cAAA7C,QAAA,EAC1B;YAED;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlG,OAAA,CAAChC,UAAU;cACT0F,IAAI,EAAC,OAAO;cACZ2C,KAAK,EAAC,OAAO;cACbyD,OAAO,EAAEA,CAAA,KAAM;gBACb7I,eAAe,CAACiC,IAAI,CAAC;gBACrBnC,mBAAmB,CAAC,IAAI,CAAC;cAC3B,CAAE;cAAAiG,QAAA,eAEFhH,OAAA,CAAChB,UAAU;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,GAzELhD,IAAI,CAAC8B,EAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0EZ,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDlG,OAAA,CAAC/B,MAAM;MAAC8L,IAAI,EAAEjJ,gBAAiB;MAACkJ,OAAO,EAAEA,CAAA,KAAMjJ,mBAAmB,CAAC,KAAK,CAAE;MAAAiG,QAAA,gBACxEhH,OAAA,CAAC9B,WAAW;QAAA8I,QAAA,EAAC;MAAW;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACtClG,OAAA,CAAC7B,aAAa;QAAA6I,QAAA,eACZhH,OAAA,CAACtC,UAAU;UAAAsJ,QAAA,GAAC,oCACuB,EAAChG,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqI,aAAa,EAAC,mCAChE;QAAA;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBlG,OAAA,CAAC5B,aAAa;QAAA4I,QAAA,gBACZhH,OAAA,CAACpC,MAAM;UAACkM,OAAO,EAAEA,CAAA,KAAM/I,mBAAmB,CAAC,KAAK,CAAE;UAAAiG,QAAA,EAAC;QAAM;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClElG,OAAA,CAACpC,MAAM;UAACkM,OAAO,EAAEhF,gBAAiB;UAACuB,KAAK,EAAC,OAAO;UAACe,OAAO,EAAC,WAAW;UAAAJ,QAAA,EAAC;QAErE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC7F,EAAA,CA/cIJ,UAAqC;EAAA,QA0LaJ,WAAW;AAAA;AAAAoK,EAAA,GA1L7DhK,UAAqC;AAid3C,eAAeA,UAAU;AAAC,IAAAgK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}