{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport cardActionAreaClasses, { getCardActionAreaUtilityClass } from \"./cardActionAreaClasses.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    focusHighlight: ['focusHighlight']\n  };\n  return composeClasses(slots, getCardActionAreaUtilityClass, classes);\n};\nconst CardActionAreaRoot = styled(ButtonBase, {\n  name: 'MuiCardActionArea',\n  slot: 'Root'\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'block',\n  textAlign: 'inherit',\n  borderRadius: 'inherit',\n  // for Safari to work https://github.com/mui/material-ui/issues/36285.\n  width: '100%',\n  [`&:hover .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.hoverOpacity,\n    '@media (hover: none)': {\n      opacity: 0\n    }\n  },\n  [`&.${cardActionAreaClasses.focusVisible} .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.focusOpacity\n  }\n})));\nconst CardActionAreaFocusHighlight = styled('span', {\n  name: 'MuiCardActionArea',\n  slot: 'FocusHighlight'\n})(memoTheme(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  pointerEvents: 'none',\n  position: 'absolute',\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0,\n  borderRadius: 'inherit',\n  opacity: 0,\n  backgroundColor: 'currentcolor',\n  transition: theme.transitions.create('opacity', {\n    duration: theme.transitions.duration.short\n  })\n})));\nconst CardActionArea = /*#__PURE__*/React.forwardRef(function CardActionArea(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActionArea'\n  });\n  const {\n    children,\n    className,\n    focusVisibleClassName,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: CardActionAreaRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    shouldForwardComponentProp: true,\n    ownerState,\n    ref,\n    className: clsx(classes.root, className),\n    additionalProps: {\n      focusVisibleClassName: clsx(focusVisibleClassName, classes.focusVisible)\n    }\n  });\n  const [FocusHighlightSlot, focusHighlightProps] = useSlot('focusHighlight', {\n    elementType: CardActionAreaFocusHighlight,\n    externalForwardedProps,\n    ownerState,\n    ref,\n    className: classes.focusHighlight\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootProps,\n    children: [children, /*#__PURE__*/_jsx(FocusHighlightSlot, {\n      ...focusHighlightProps\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActionArea.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    focusHighlight: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    focusHighlight: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActionArea;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "styled", "memoTheme", "useDefaultProps", "cardActionAreaClasses", "getCardActionAreaUtilityClass", "ButtonBase", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "slots", "root", "focusHighlight", "CardActionAreaRoot", "name", "slot", "theme", "display", "textAlign", "borderRadius", "width", "opacity", "vars", "palette", "action", "hoverOpacity", "focusVisible", "focusOpacity", "CardActionAreaFocusHighlight", "overflow", "pointerEvents", "position", "top", "right", "bottom", "left", "backgroundColor", "transition", "transitions", "create", "duration", "short", "CardActionArea", "forwardRef", "inProps", "ref", "props", "children", "className", "focusVisibleClassName", "slotProps", "other", "externalForwardedProps", "RootSlot", "rootProps", "elementType", "shouldForwardComponentProp", "additionalProps", "FocusHighlightSlot", "focusHighlightProps", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "shape", "oneOfType", "func", "sx", "arrayOf", "bool"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/CardActionArea/CardActionArea.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport cardActionAreaClasses, { getCardActionAreaUtilityClass } from \"./cardActionAreaClasses.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    focusHighlight: ['focusHighlight']\n  };\n  return composeClasses(slots, getCardActionAreaUtilityClass, classes);\n};\nconst CardActionAreaRoot = styled(ButtonBase, {\n  name: 'MuiCardActionArea',\n  slot: 'Root'\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'block',\n  textAlign: 'inherit',\n  borderRadius: 'inherit',\n  // for Safari to work https://github.com/mui/material-ui/issues/36285.\n  width: '100%',\n  [`&:hover .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.hoverOpacity,\n    '@media (hover: none)': {\n      opacity: 0\n    }\n  },\n  [`&.${cardActionAreaClasses.focusVisible} .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.focusOpacity\n  }\n})));\nconst CardActionAreaFocusHighlight = styled('span', {\n  name: 'MuiCardActionArea',\n  slot: 'FocusHighlight'\n})(memoTheme(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  pointerEvents: 'none',\n  position: 'absolute',\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0,\n  borderRadius: 'inherit',\n  opacity: 0,\n  backgroundColor: 'currentcolor',\n  transition: theme.transitions.create('opacity', {\n    duration: theme.transitions.duration.short\n  })\n})));\nconst CardActionArea = /*#__PURE__*/React.forwardRef(function CardActionArea(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActionArea'\n  });\n  const {\n    children,\n    className,\n    focusVisibleClassName,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: CardActionAreaRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    shouldForwardComponentProp: true,\n    ownerState,\n    ref,\n    className: clsx(classes.root, className),\n    additionalProps: {\n      focusVisibleClassName: clsx(focusVisibleClassName, classes.focusVisible)\n    }\n  });\n  const [FocusHighlightSlot, focusHighlightProps] = useSlot('focusHighlight', {\n    elementType: CardActionAreaFocusHighlight,\n    externalForwardedProps,\n    ownerState,\n    ref,\n    className: classes.focusHighlight\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootProps,\n    children: [children, /*#__PURE__*/_jsx(FocusHighlightSlot, {\n      ...focusHighlightProps\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActionArea.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    focusHighlight: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    focusHighlight: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActionArea;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,qBAAqB,IAAIC,6BAA6B,QAAQ,4BAA4B;AACjG,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,cAAc,EAAE,CAAC,gBAAgB;EACnC,CAAC;EACD,OAAOjB,cAAc,CAACe,KAAK,EAAEV,6BAA6B,EAAES,OAAO,CAAC;AACtE,CAAC;AACD,MAAMI,kBAAkB,GAAGjB,MAAM,CAACK,UAAU,EAAE;EAC5Ca,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAClB,SAAS,CAAC,CAAC;EACZmB;AACF,CAAC,MAAM;EACLC,OAAO,EAAE,OAAO;EAChBC,SAAS,EAAE,SAAS;EACpBC,YAAY,EAAE,SAAS;EACvB;EACAC,KAAK,EAAE,MAAM;EACb,CAAC,YAAYrB,qBAAqB,CAACa,cAAc,EAAE,GAAG;IACpDS,OAAO,EAAE,CAACL,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEO,OAAO,CAACC,MAAM,CAACC,YAAY;IAC1D,sBAAsB,EAAE;MACtBJ,OAAO,EAAE;IACX;EACF,CAAC;EACD,CAAC,KAAKtB,qBAAqB,CAAC2B,YAAY,KAAK3B,qBAAqB,CAACa,cAAc,EAAE,GAAG;IACpFS,OAAO,EAAE,CAACL,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEO,OAAO,CAACC,MAAM,CAACG;EAChD;AACF,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMC,4BAA4B,GAAGhC,MAAM,CAAC,MAAM,EAAE;EAClDkB,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAClB,SAAS,CAAC,CAAC;EACZmB;AACF,CAAC,MAAM;EACLa,QAAQ,EAAE,QAAQ;EAClBC,aAAa,EAAE,MAAM;EACrBC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPhB,YAAY,EAAE,SAAS;EACvBE,OAAO,EAAE,CAAC;EACVe,eAAe,EAAE,cAAc;EAC/BC,UAAU,EAAErB,KAAK,CAACsB,WAAW,CAACC,MAAM,CAAC,SAAS,EAAE;IAC9CC,QAAQ,EAAExB,KAAK,CAACsB,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMC,cAAc,GAAG,aAAalD,KAAK,CAACmD,UAAU,CAAC,SAASD,cAAcA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzF,MAAMC,KAAK,GAAGhD,eAAe,CAAC;IAC5BgD,KAAK,EAAEF,OAAO;IACd9B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJiC,QAAQ;IACRC,SAAS;IACTC,qBAAqB;IACrBvC,KAAK,GAAG,CAAC,CAAC;IACVwC,SAAS,GAAG,CAAC,CAAC;IACd,GAAGC;EACL,CAAC,GAAGL,KAAK;EACT,MAAMtC,UAAU,GAAGsC,KAAK;EACxB,MAAMrC,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM4C,sBAAsB,GAAG;IAC7B1C,KAAK;IACLwC;EACF,CAAC;EACD,MAAM,CAACG,QAAQ,EAAEC,SAAS,CAAC,GAAGpD,OAAO,CAAC,MAAM,EAAE;IAC5CqD,WAAW,EAAE1C,kBAAkB;IAC/BuC,sBAAsB,EAAE;MACtB,GAAGA,sBAAsB;MACzB,GAAGD;IACL,CAAC;IACDK,0BAA0B,EAAE,IAAI;IAChChD,UAAU;IACVqC,GAAG;IACHG,SAAS,EAAEtD,IAAI,CAACe,OAAO,CAACE,IAAI,EAAEqC,SAAS,CAAC;IACxCS,eAAe,EAAE;MACfR,qBAAqB,EAAEvD,IAAI,CAACuD,qBAAqB,EAAExC,OAAO,CAACiB,YAAY;IACzE;EACF,CAAC,CAAC;EACF,MAAM,CAACgC,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGzD,OAAO,CAAC,gBAAgB,EAAE;IAC1EqD,WAAW,EAAE3B,4BAA4B;IACzCwB,sBAAsB;IACtB5C,UAAU;IACVqC,GAAG;IACHG,SAAS,EAAEvC,OAAO,CAACG;EACrB,CAAC,CAAC;EACF,OAAO,aAAaN,KAAK,CAAC+C,QAAQ,EAAE;IAClC,GAAGC,SAAS;IACZP,QAAQ,EAAE,CAACA,QAAQ,EAAE,aAAa3C,IAAI,CAACsD,kBAAkB,EAAE;MACzD,GAAGC;IACL,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGpB,cAAc,CAACqB,SAAS,CAAC,yBAAyB;EACxF;EACA;EACA;EACA;EACA;AACF;AACA;EACEhB,QAAQ,EAAEtD,SAAS,CAACuE,IAAI;EACxB;AACF;AACA;EACEvD,OAAO,EAAEhB,SAAS,CAACwE,MAAM;EACzB;AACF;AACA;EACEjB,SAAS,EAAEvD,SAAS,CAACyE,MAAM;EAC3B;AACF;AACA;EACEjB,qBAAqB,EAAExD,SAAS,CAACyE,MAAM;EACvC;AACF;AACA;AACA;EACEhB,SAAS,EAAEzD,SAAS,CAAC0E,KAAK,CAAC;IACzBvD,cAAc,EAAEnB,SAAS,CAAC2E,SAAS,CAAC,CAAC3E,SAAS,CAAC4E,IAAI,EAAE5E,SAAS,CAACwE,MAAM,CAAC,CAAC;IACvEtD,IAAI,EAAElB,SAAS,CAAC2E,SAAS,CAAC,CAAC3E,SAAS,CAAC4E,IAAI,EAAE5E,SAAS,CAACwE,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEvD,KAAK,EAAEjB,SAAS,CAAC0E,KAAK,CAAC;IACrBvD,cAAc,EAAEnB,SAAS,CAAC8D,WAAW;IACrC5C,IAAI,EAAElB,SAAS,CAAC8D;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEe,EAAE,EAAE7E,SAAS,CAAC2E,SAAS,CAAC,CAAC3E,SAAS,CAAC8E,OAAO,CAAC9E,SAAS,CAAC2E,SAAS,CAAC,CAAC3E,SAAS,CAAC4E,IAAI,EAAE5E,SAAS,CAACwE,MAAM,EAAExE,SAAS,CAAC+E,IAAI,CAAC,CAAC,CAAC,EAAE/E,SAAS,CAAC4E,IAAI,EAAE5E,SAAS,CAACwE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAevB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}