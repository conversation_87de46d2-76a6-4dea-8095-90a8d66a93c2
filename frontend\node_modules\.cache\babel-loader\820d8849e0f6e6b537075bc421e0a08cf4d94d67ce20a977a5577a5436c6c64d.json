{"ast": null, "code": "import getScrollbarSize from '@mui/utils/getScrollbarSize';\nexport default getScrollbarSize;", "map": {"version": 3, "names": ["getScrollbarSize"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/utils/getScrollbarSize.js"], "sourcesContent": ["import getScrollbarSize from '@mui/utils/getScrollbarSize';\nexport default getScrollbarSize;"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,6BAA6B;AAC1D,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}