{"ast": null, "code": "/**\n * Topological sorting function\n *\n * @param {Array} edges\n * @returns {Array}\n */\n\nmodule.exports = function (edges) {\n  return toposort(uniqueNodes(edges), edges);\n};\nmodule.exports.array = toposort;\nfunction toposort(nodes, edges) {\n  var cursor = nodes.length,\n    sorted = new Array(cursor),\n    visited = {},\n    i = cursor\n    // Better data structures make algorithm much faster.\n    ,\n    outgoingEdges = makeOutgoingEdges(edges),\n    nodesHash = makeNodesHash(nodes);\n\n  // check for unknown nodes\n  edges.forEach(function (edge) {\n    if (!nodesHash.has(edge[0]) || !nodesHash.has(edge[1])) {\n      throw new Error('Unknown node. There is an unknown node in the supplied edges.');\n    }\n  });\n  while (i--) {\n    if (!visited[i]) visit(nodes[i], i, new Set());\n  }\n  return sorted;\n  function visit(node, i, predecessors) {\n    if (predecessors.has(node)) {\n      var nodeRep;\n      try {\n        nodeRep = \", node was:\" + JSON.stringify(node);\n      } catch (e) {\n        nodeRep = \"\";\n      }\n      throw new Error('Cyclic dependency' + nodeRep);\n    }\n    if (!nodesHash.has(node)) {\n      throw new Error('Found unknown node. Make sure to provided all involved nodes. Unknown node: ' + JSON.stringify(node));\n    }\n    if (visited[i]) return;\n    visited[i] = true;\n    var outgoing = outgoingEdges.get(node) || new Set();\n    outgoing = Array.from(outgoing);\n    if (i = outgoing.length) {\n      predecessors.add(node);\n      do {\n        var child = outgoing[--i];\n        visit(child, nodesHash.get(child), predecessors);\n      } while (i);\n      predecessors.delete(node);\n    }\n    sorted[--cursor] = node;\n  }\n}\nfunction uniqueNodes(arr) {\n  var res = new Set();\n  for (var i = 0, len = arr.length; i < len; i++) {\n    var edge = arr[i];\n    res.add(edge[0]);\n    res.add(edge[1]);\n  }\n  return Array.from(res);\n}\nfunction makeOutgoingEdges(arr) {\n  var edges = new Map();\n  for (var i = 0, len = arr.length; i < len; i++) {\n    var edge = arr[i];\n    if (!edges.has(edge[0])) edges.set(edge[0], new Set());\n    if (!edges.has(edge[1])) edges.set(edge[1], new Set());\n    edges.get(edge[0]).add(edge[1]);\n  }\n  return edges;\n}\nfunction makeNodesHash(arr) {\n  var res = new Map();\n  for (var i = 0, len = arr.length; i < len; i++) {\n    res.set(arr[i], i);\n  }\n  return res;\n}", "map": {"version": 3, "names": ["module", "exports", "edges", "toposort", "uniqueNodes", "array", "nodes", "cursor", "length", "sorted", "Array", "visited", "i", "outgoing<PERSON><PERSON>", "makeOutgoingEdges", "nodesHash", "makeNodesHash", "for<PERSON>ach", "edge", "has", "Error", "visit", "Set", "node", "predecessors", "nodeRep", "JSON", "stringify", "e", "outgoing", "get", "from", "add", "child", "delete", "arr", "res", "len", "Map", "set"], "sources": ["C:/laragon/www/frontend/node_modules/toposort/index.js"], "sourcesContent": ["\n/**\n * Topological sorting function\n *\n * @param {Array} edges\n * @returns {Array}\n */\n\nmodule.exports = function(edges) {\n  return toposort(uniqueNodes(edges), edges)\n}\n\nmodule.exports.array = toposort\n\nfunction toposort(nodes, edges) {\n  var cursor = nodes.length\n    , sorted = new Array(cursor)\n    , visited = {}\n    , i = cursor\n    // Better data structures make algorithm much faster.\n    , outgoingEdges = makeOutgoingEdges(edges)\n    , nodesHash = makeNodesHash(nodes)\n\n  // check for unknown nodes\n  edges.forEach(function(edge) {\n    if (!nodesHash.has(edge[0]) || !nodesHash.has(edge[1])) {\n      throw new Error('Unknown node. There is an unknown node in the supplied edges.')\n    }\n  })\n\n  while (i--) {\n    if (!visited[i]) visit(nodes[i], i, new Set())\n  }\n\n  return sorted\n\n  function visit(node, i, predecessors) {\n    if(predecessors.has(node)) {\n      var nodeRep\n      try {\n        nodeRep = \", node was:\" + JSON.stringify(node)\n      } catch(e) {\n        nodeRep = \"\"\n      }\n      throw new Error('Cyclic dependency' + nodeRep)\n    }\n\n    if (!nodesHash.has(node)) {\n      throw new Error('Found unknown node. Make sure to provided all involved nodes. Unknown node: '+JSON.stringify(node))\n    }\n\n    if (visited[i]) return;\n    visited[i] = true\n\n    var outgoing = outgoingEdges.get(node) || new Set()\n    outgoing = Array.from(outgoing)\n\n    if (i = outgoing.length) {\n      predecessors.add(node)\n      do {\n        var child = outgoing[--i]\n        visit(child, nodesHash.get(child), predecessors)\n      } while (i)\n      predecessors.delete(node)\n    }\n\n    sorted[--cursor] = node\n  }\n}\n\nfunction uniqueNodes(arr){\n  var res = new Set()\n  for (var i = 0, len = arr.length; i < len; i++) {\n    var edge = arr[i]\n    res.add(edge[0])\n    res.add(edge[1])\n  }\n  return Array.from(res)\n}\n\nfunction makeOutgoingEdges(arr){\n  var edges = new Map()\n  for (var i = 0, len = arr.length; i < len; i++) {\n    var edge = arr[i]\n    if (!edges.has(edge[0])) edges.set(edge[0], new Set())\n    if (!edges.has(edge[1])) edges.set(edge[1], new Set())\n    edges.get(edge[0]).add(edge[1])\n  }\n  return edges\n}\n\nfunction makeNodesHash(arr){\n  var res = new Map()\n  for (var i = 0, len = arr.length; i < len; i++) {\n    res.set(arr[i], i)\n  }\n  return res\n}\n"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;;AAEAA,MAAM,CAACC,OAAO,GAAG,UAASC,KAAK,EAAE;EAC/B,OAAOC,QAAQ,CAACC,WAAW,CAACF,KAAK,CAAC,EAAEA,KAAK,CAAC;AAC5C,CAAC;AAEDF,MAAM,CAACC,OAAO,CAACI,KAAK,GAAGF,QAAQ;AAE/B,SAASA,QAAQA,CAACG,KAAK,EAAEJ,KAAK,EAAE;EAC9B,IAAIK,MAAM,GAAGD,KAAK,CAACE,MAAM;IACrBC,MAAM,GAAG,IAAIC,KAAK,CAACH,MAAM,CAAC;IAC1BI,OAAO,GAAG,CAAC,CAAC;IACZC,CAAC,GAAGL;IACN;IAAA;IACEM,aAAa,GAAGC,iBAAiB,CAACZ,KAAK,CAAC;IACxCa,SAAS,GAAGC,aAAa,CAACV,KAAK,CAAC;;EAEpC;EACAJ,KAAK,CAACe,OAAO,CAAC,UAASC,IAAI,EAAE;IAC3B,IAAI,CAACH,SAAS,CAACI,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAACH,SAAS,CAACI,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MACtD,MAAM,IAAIE,KAAK,CAAC,+DAA+D,CAAC;IAClF;EACF,CAAC,CAAC;EAEF,OAAOR,CAAC,EAAE,EAAE;IACV,IAAI,CAACD,OAAO,CAACC,CAAC,CAAC,EAAES,KAAK,CAACf,KAAK,CAACM,CAAC,CAAC,EAAEA,CAAC,EAAE,IAAIU,GAAG,CAAC,CAAC,CAAC;EAChD;EAEA,OAAOb,MAAM;EAEb,SAASY,KAAKA,CAACE,IAAI,EAAEX,CAAC,EAAEY,YAAY,EAAE;IACpC,IAAGA,YAAY,CAACL,GAAG,CAACI,IAAI,CAAC,EAAE;MACzB,IAAIE,OAAO;MACX,IAAI;QACFA,OAAO,GAAG,aAAa,GAAGC,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC;MAChD,CAAC,CAAC,OAAMK,CAAC,EAAE;QACTH,OAAO,GAAG,EAAE;MACd;MACA,MAAM,IAAIL,KAAK,CAAC,mBAAmB,GAAGK,OAAO,CAAC;IAChD;IAEA,IAAI,CAACV,SAAS,CAACI,GAAG,CAACI,IAAI,CAAC,EAAE;MACxB,MAAM,IAAIH,KAAK,CAAC,8EAA8E,GAACM,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,CAAC;IACtH;IAEA,IAAIZ,OAAO,CAACC,CAAC,CAAC,EAAE;IAChBD,OAAO,CAACC,CAAC,CAAC,GAAG,IAAI;IAEjB,IAAIiB,QAAQ,GAAGhB,aAAa,CAACiB,GAAG,CAACP,IAAI,CAAC,IAAI,IAAID,GAAG,CAAC,CAAC;IACnDO,QAAQ,GAAGnB,KAAK,CAACqB,IAAI,CAACF,QAAQ,CAAC;IAE/B,IAAIjB,CAAC,GAAGiB,QAAQ,CAACrB,MAAM,EAAE;MACvBgB,YAAY,CAACQ,GAAG,CAACT,IAAI,CAAC;MACtB,GAAG;QACD,IAAIU,KAAK,GAAGJ,QAAQ,CAAC,EAAEjB,CAAC,CAAC;QACzBS,KAAK,CAACY,KAAK,EAAElB,SAAS,CAACe,GAAG,CAACG,KAAK,CAAC,EAAET,YAAY,CAAC;MAClD,CAAC,QAAQZ,CAAC;MACVY,YAAY,CAACU,MAAM,CAACX,IAAI,CAAC;IAC3B;IAEAd,MAAM,CAAC,EAAEF,MAAM,CAAC,GAAGgB,IAAI;EACzB;AACF;AAEA,SAASnB,WAAWA,CAAC+B,GAAG,EAAC;EACvB,IAAIC,GAAG,GAAG,IAAId,GAAG,CAAC,CAAC;EACnB,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEyB,GAAG,GAAGF,GAAG,CAAC3B,MAAM,EAAEI,CAAC,GAAGyB,GAAG,EAAEzB,CAAC,EAAE,EAAE;IAC9C,IAAIM,IAAI,GAAGiB,GAAG,CAACvB,CAAC,CAAC;IACjBwB,GAAG,CAACJ,GAAG,CAACd,IAAI,CAAC,CAAC,CAAC,CAAC;IAChBkB,GAAG,CAACJ,GAAG,CAACd,IAAI,CAAC,CAAC,CAAC,CAAC;EAClB;EACA,OAAOR,KAAK,CAACqB,IAAI,CAACK,GAAG,CAAC;AACxB;AAEA,SAAStB,iBAAiBA,CAACqB,GAAG,EAAC;EAC7B,IAAIjC,KAAK,GAAG,IAAIoC,GAAG,CAAC,CAAC;EACrB,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEyB,GAAG,GAAGF,GAAG,CAAC3B,MAAM,EAAEI,CAAC,GAAGyB,GAAG,EAAEzB,CAAC,EAAE,EAAE;IAC9C,IAAIM,IAAI,GAAGiB,GAAG,CAACvB,CAAC,CAAC;IACjB,IAAI,CAACV,KAAK,CAACiB,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEhB,KAAK,CAACqC,GAAG,CAACrB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAII,GAAG,CAAC,CAAC,CAAC;IACtD,IAAI,CAACpB,KAAK,CAACiB,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEhB,KAAK,CAACqC,GAAG,CAACrB,IAAI,CAAC,CAAC,CAAC,EAAE,IAAII,GAAG,CAAC,CAAC,CAAC;IACtDpB,KAAK,CAAC4B,GAAG,CAACZ,IAAI,CAAC,CAAC,CAAC,CAAC,CAACc,GAAG,CAACd,IAAI,CAAC,CAAC,CAAC,CAAC;EACjC;EACA,OAAOhB,KAAK;AACd;AAEA,SAASc,aAAaA,CAACmB,GAAG,EAAC;EACzB,IAAIC,GAAG,GAAG,IAAIE,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEyB,GAAG,GAAGF,GAAG,CAAC3B,MAAM,EAAEI,CAAC,GAAGyB,GAAG,EAAEzB,CAAC,EAAE,EAAE;IAC9CwB,GAAG,CAACG,GAAG,CAACJ,GAAG,CAACvB,CAAC,CAAC,EAAEA,CAAC,CAAC;EACpB;EACA,OAAOwB,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}