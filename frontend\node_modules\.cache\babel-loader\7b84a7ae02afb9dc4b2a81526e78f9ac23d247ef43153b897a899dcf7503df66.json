{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n\n/**\n * Merges refs into a single memoized callback ref or `null`.\n *\n * ```tsx\n * const rootRef = React.useRef<Instance>(null);\n * const refFork = useForkRef(rootRef, props.ref);\n *\n * return (\n *   <Root {...props} ref={refFork} />\n * );\n * ```\n *\n * @param {Array<React.Ref<Instance> | undefined>} refs The ref array.\n * @returns {React.RefCallback<Instance> | null} The new ref callback.\n */\nexport default function useForkRef() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n  const cleanupRef = React.useRef(undefined);\n  const refEffect = React.useCallback(instance => {\n    const cleanups = refs.map(ref => {\n      if (ref == null) {\n        return null;\n      }\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return typeof refCleanup === 'function' ? refCleanup : () => {\n          refCallback(null);\n        };\n      }\n      ref.current = instance;\n      return () => {\n        ref.current = null;\n      };\n    });\n    return () => {\n      cleanups.forEach(refCleanup => refCleanup?.());\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (value != null) {\n        cleanupRef.current = refEffect(value);\n      }\n    };\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- intentionally ignoring that the dependency array must be an array literal\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}", "map": {"version": 3, "names": ["React", "useForkRef", "_len", "arguments", "length", "refs", "Array", "_key", "cleanupRef", "useRef", "undefined", "refEffect", "useCallback", "instance", "cleanups", "map", "ref", "refC<PERSON><PERSON>", "refCleanup", "current", "for<PERSON>ach", "useMemo", "every", "value"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/utils/esm/useForkRef/useForkRef.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * Merges refs into a single memoized callback ref or `null`.\n *\n * ```tsx\n * const rootRef = React.useRef<Instance>(null);\n * const refFork = useForkRef(rootRef, props.ref);\n *\n * return (\n *   <Root {...props} ref={refFork} />\n * );\n * ```\n *\n * @param {Array<React.Ref<Instance> | undefined>} refs The ref array.\n * @returns {React.RefCallback<Instance> | null} The new ref callback.\n */\nexport default function useForkRef(...refs) {\n  const cleanupRef = React.useRef(undefined);\n  const refEffect = React.useCallback(instance => {\n    const cleanups = refs.map(ref => {\n      if (ref == null) {\n        return null;\n      }\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return typeof refCleanup === 'function' ? refCleanup : () => {\n          refCallback(null);\n        };\n      }\n      ref.current = instance;\n      return () => {\n        ref.current = null;\n      };\n    });\n    return () => {\n      cleanups.forEach(refCleanup => refCleanup?.());\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (value != null) {\n        cleanupRef.current = refEffect(value);\n      }\n    };\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- intentionally ignoring that the dependency array must be an array literal\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAAA,EAAU;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACxC,MAAMC,UAAU,GAAGR,KAAK,CAACS,MAAM,CAACC,SAAS,CAAC;EAC1C,MAAMC,SAAS,GAAGX,KAAK,CAACY,WAAW,CAACC,QAAQ,IAAI;IAC9C,MAAMC,QAAQ,GAAGT,IAAI,CAACU,GAAG,CAACC,GAAG,IAAI;MAC/B,IAAIA,GAAG,IAAI,IAAI,EAAE;QACf,OAAO,IAAI;MACb;MACA,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;QAC7B,MAAMC,WAAW,GAAGD,GAAG;QACvB,MAAME,UAAU,GAAGD,WAAW,CAACJ,QAAQ,CAAC;QACxC,OAAO,OAAOK,UAAU,KAAK,UAAU,GAAGA,UAAU,GAAG,MAAM;UAC3DD,WAAW,CAAC,IAAI,CAAC;QACnB,CAAC;MACH;MACAD,GAAG,CAACG,OAAO,GAAGN,QAAQ;MACtB,OAAO,MAAM;QACXG,GAAG,CAACG,OAAO,GAAG,IAAI;MACpB,CAAC;IACH,CAAC,CAAC;IACF,OAAO,MAAM;MACXL,QAAQ,CAACM,OAAO,CAACF,UAAU,IAAIA,UAAU,GAAG,CAAC,CAAC;IAChD,CAAC;IACD;EACF,CAAC,EAAEb,IAAI,CAAC;EACR,OAAOL,KAAK,CAACqB,OAAO,CAAC,MAAM;IACzB,IAAIhB,IAAI,CAACiB,KAAK,CAACN,GAAG,IAAIA,GAAG,IAAI,IAAI,CAAC,EAAE;MAClC,OAAO,IAAI;IACb;IACA,OAAOO,KAAK,IAAI;MACd,IAAIf,UAAU,CAACW,OAAO,EAAE;QACtBX,UAAU,CAACW,OAAO,CAAC,CAAC;QACpBX,UAAU,CAACW,OAAO,GAAGT,SAAS;MAChC;MACA,IAAIa,KAAK,IAAI,IAAI,EAAE;QACjBf,UAAU,CAACW,OAAO,GAAGR,SAAS,CAACY,KAAK,CAAC;MACvC;IACF,CAAC;IACD;IACA;EACF,CAAC,EAAElB,IAAI,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}