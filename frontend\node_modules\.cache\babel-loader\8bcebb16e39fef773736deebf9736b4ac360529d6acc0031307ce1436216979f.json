{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport authService from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    initializeAuth();\n  }, []);\n  const initializeAuth = async () => {\n    try {\n      const storedUser = authService.getStoredUser();\n      const token = authService.getStoredToken();\n      if (storedUser && token) {\n        // Verify token is still valid by fetching current user\n        try {\n          const currentUser = await authService.getProfile();\n          setUser(currentUser);\n        } catch (error) {\n          // Token is invalid, clear stored data\n          authService.clearAuthData();\n          setUser(null);\n        }\n      }\n    } catch (error) {\n      console.error('Auth initialization error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = async credentials => {\n    setLoading(true);\n    try {\n      const response = await authService.login(credentials);\n      setUser(response.user);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const register = async data => {\n    setLoading(true);\n    try {\n      const response = await authService.register(data);\n      setUser(response.user);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const logout = async () => {\n    setLoading(true);\n    try {\n      await authService.logout();\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const updateProfile = async data => {\n    const updatedUser = await authService.updateProfile(data);\n    setUser(updatedUser);\n  };\n  const uploadAvatar = async file => {\n    const updatedUser = await authService.uploadAvatar(file);\n    setUser(updatedUser);\n  };\n  const refreshUser = async () => {\n    if (authService.isAuthenticated()) {\n      try {\n        const currentUser = await authService.getProfile();\n        setUser(currentUser);\n      } catch (error) {\n        console.error('Failed to refresh user:', error);\n        await logout();\n      }\n    }\n  };\n  const forgotPassword = async email => {\n    return authService.forgotPassword(email);\n  };\n  const resetPassword = async data => {\n    return authService.resetPassword(data);\n  };\n  const resendVerification = async () => {\n    return authService.resendVerification();\n  };\n  const value = {\n    user,\n    loading,\n    isAuthenticated: !!user,\n    isEmailVerified: !!(user !== null && user !== void 0 && user.email_verified_at),\n    login,\n    register,\n    logout,\n    updateProfile,\n    uploadAvatar,\n    refreshUser,\n    forgotPassword,\n    resetPassword,\n    resendVerification\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "initializeAuth", "storedUser", "getStoredUser", "token", "getStoredToken", "currentUser", "getProfile", "error", "clearAuthData", "console", "login", "credentials", "response", "register", "data", "logout", "updateProfile", "updatedUser", "uploadAvatar", "file", "refreshUser", "isAuthenticated", "forgotPassword", "email", "resetPassword", "resendVerification", "value", "isEmailVerified", "email_verified_at", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport authService, { User, LoginCredentials, RegisterData, UpdateProfileData } from '../services/authService';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  isAuthenticated: boolean;\n  isEmailVerified: boolean;\n  login: (credentials: LoginCredentials) => Promise<void>;\n  register: (data: RegisterData) => Promise<void>;\n  logout: () => Promise<void>;\n  updateProfile: (data: UpdateProfileData) => Promise<void>;\n  uploadAvatar: (file: File) => Promise<void>;\n  refreshUser: () => Promise<void>;\n  forgotPassword: (email: string) => Promise<{ message: string }>;\n  resetPassword: (data: {\n    token: string;\n    email: string;\n    password: string;\n    password_confirmation: string;\n  }) => Promise<{ message: string }>;\n  resendVerification: () => Promise<{ message: string }>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    initializeAuth();\n  }, []);\n\n  const initializeAuth = async () => {\n    try {\n      const storedUser = authService.getStoredUser();\n      const token = authService.getStoredToken();\n\n      if (storedUser && token) {\n        // Verify token is still valid by fetching current user\n        try {\n          const currentUser = await authService.getProfile();\n          setUser(currentUser);\n        } catch (error) {\n          // Token is invalid, clear stored data\n          authService.clearAuthData();\n          setUser(null);\n        }\n      }\n    } catch (error) {\n      console.error('Auth initialization error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (credentials: LoginCredentials) => {\n    setLoading(true);\n    try {\n      const response = await authService.login(credentials);\n      setUser(response.user);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    setLoading(true);\n    try {\n      const response = await authService.register(data);\n      setUser(response.user);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setLoading(true);\n    try {\n      await authService.logout();\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const updateProfile = async (data: UpdateProfileData) => {\n    const updatedUser = await authService.updateProfile(data);\n    setUser(updatedUser);\n  };\n\n  const uploadAvatar = async (file: File) => {\n    const updatedUser = await authService.uploadAvatar(file);\n    setUser(updatedUser);\n  };\n\n  const refreshUser = async () => {\n    if (authService.isAuthenticated()) {\n      try {\n        const currentUser = await authService.getProfile();\n        setUser(currentUser);\n      } catch (error) {\n        console.error('Failed to refresh user:', error);\n        await logout();\n      }\n    }\n  };\n\n  const forgotPassword = async (email: string) => {\n    return authService.forgotPassword(email);\n  };\n\n  const resetPassword = async (data: {\n    token: string;\n    email: string;\n    password: string;\n    password_confirmation: string;\n  }) => {\n    return authService.resetPassword(data);\n  };\n\n  const resendVerification = async () => {\n    return authService.resendVerification();\n  };\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    isAuthenticated: !!user,\n    isEmailVerified: !!user?.email_verified_at,\n    login,\n    register,\n    logout,\n    updateProfile,\n    uploadAvatar,\n    refreshUser,\n    forgotPassword,\n    resetPassword,\n    resendVerification,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AACxF,OAAOC,WAAW,MAAmE,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuB/G,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACM,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAYpB,OAAO,MAAMI,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,UAAU,GAAGjB,WAAW,CAACkB,aAAa,CAAC,CAAC;MAC9C,MAAMC,KAAK,GAAGnB,WAAW,CAACoB,cAAc,CAAC,CAAC;MAE1C,IAAIH,UAAU,IAAIE,KAAK,EAAE;QACvB;QACA,IAAI;UACF,MAAME,WAAW,GAAG,MAAMrB,WAAW,CAACsB,UAAU,CAAC,CAAC;UAClDT,OAAO,CAACQ,WAAW,CAAC;QACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACd;UACAvB,WAAW,CAACwB,aAAa,CAAC,CAAC;UAC3BX,OAAO,CAAC,IAAI,CAAC;QACf;MACF;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,KAAK,GAAG,MAAOC,WAA6B,IAAK;IACrDZ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAM5B,WAAW,CAAC0B,KAAK,CAACC,WAAW,CAAC;MACrDd,OAAO,CAACe,QAAQ,CAAChB,IAAI,CAAC;IACxB,CAAC,SAAS;MACRG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,QAAQ,GAAG,MAAOC,IAAkB,IAAK;IAC7Cf,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAM5B,WAAW,CAAC6B,QAAQ,CAACC,IAAI,CAAC;MACjDjB,OAAO,CAACe,QAAQ,CAAChB,IAAI,CAAC;IACxB,CAAC,SAAS;MACRG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzBhB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMf,WAAW,CAAC+B,MAAM,CAAC,CAAC;MAC1BlB,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,aAAa,GAAG,MAAOF,IAAuB,IAAK;IACvD,MAAMG,WAAW,GAAG,MAAMjC,WAAW,CAACgC,aAAa,CAACF,IAAI,CAAC;IACzDjB,OAAO,CAACoB,WAAW,CAAC;EACtB,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,IAAU,IAAK;IACzC,MAAMF,WAAW,GAAG,MAAMjC,WAAW,CAACkC,YAAY,CAACC,IAAI,CAAC;IACxDtB,OAAO,CAACoB,WAAW,CAAC;EACtB,CAAC;EAED,MAAMG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAIpC,WAAW,CAACqC,eAAe,CAAC,CAAC,EAAE;MACjC,IAAI;QACF,MAAMhB,WAAW,GAAG,MAAMrB,WAAW,CAACsB,UAAU,CAAC,CAAC;QAClDT,OAAO,CAACQ,WAAW,CAAC;MACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMQ,MAAM,CAAC,CAAC;MAChB;IACF;EACF,CAAC;EAED,MAAMO,cAAc,GAAG,MAAOC,KAAa,IAAK;IAC9C,OAAOvC,WAAW,CAACsC,cAAc,CAACC,KAAK,CAAC;EAC1C,CAAC;EAED,MAAMC,aAAa,GAAG,MAAOV,IAK5B,IAAK;IACJ,OAAO9B,WAAW,CAACwC,aAAa,CAACV,IAAI,CAAC;EACxC,CAAC;EAED,MAAMW,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,OAAOzC,WAAW,CAACyC,kBAAkB,CAAC,CAAC;EACzC,CAAC;EAED,MAAMC,KAAsB,GAAG;IAC7B9B,IAAI;IACJE,OAAO;IACPuB,eAAe,EAAE,CAAC,CAACzB,IAAI;IACvB+B,eAAe,EAAE,CAAC,EAAC/B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,iBAAiB;IAC1ClB,KAAK;IACLG,QAAQ;IACRE,MAAM;IACNC,aAAa;IACbE,YAAY;IACZE,WAAW;IACXE,cAAc;IACdE,aAAa;IACbC;EACF,CAAC;EAED,oBAAOvC,OAAA,CAACC,WAAW,CAAC0C,QAAQ;IAACH,KAAK,EAAEA,KAAM;IAAAhC,QAAA,EAAEA;EAAQ;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACtC,GAAA,CArHWF,YAAyC;AAAAyC,EAAA,GAAzCzC,YAAyC;AAAA,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}