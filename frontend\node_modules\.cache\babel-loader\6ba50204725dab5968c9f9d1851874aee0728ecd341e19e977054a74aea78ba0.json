{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FigureCaption = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'figcaption',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'figure-caption');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nFigureCaption.displayName = 'FigureCaption';\nexport default FigureCaption;", "map": {"version": 3, "names": ["React", "classNames", "useBootstrapPrefix", "jsx", "_jsx", "FigureCaption", "forwardRef", "className", "bsPrefix", "as", "Component", "props", "ref", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/FigureCaption.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FigureCaption = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'figcaption',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'figure-caption');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nFigureCaption.displayName = 'FigureCaption';\nexport default FigureCaption;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EACnDC,SAAS;EACTC,QAAQ;EACRC,EAAE,EAAEC,SAAS,GAAG,YAAY;EAC5B,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTJ,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,gBAAgB,CAAC;EACzD,OAAO,aAAaJ,IAAI,CAACM,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACRL,SAAS,EAAEN,UAAU,CAACM,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,aAAa,CAACQ,WAAW,GAAG,eAAe;AAC3C,eAAeR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}