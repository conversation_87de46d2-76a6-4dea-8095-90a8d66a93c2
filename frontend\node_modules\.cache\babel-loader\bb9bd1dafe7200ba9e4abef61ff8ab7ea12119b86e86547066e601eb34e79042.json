{"ast": null, "code": "import React,{useState}from'react';import{useNavigate,useLocation,Link}from'react-router-dom';import{Container,Row,Col,Card,Form,But<PERSON>,Alert,Spinner}from'react-bootstrap';import{useForm}from'react-hook-form';import{yupResolver}from'@hookform/resolvers/yup';import*as yup from'yup';import{useAuth}from'../../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const schema=yup.object({email:yup.string().email('Invalid email').required('Email is required'),password:yup.string().required('Password is required')});const Login=()=>{var _location$state,_location$state$from,_errors$email,_errors$password;const[error,setError]=useState('');const[loading,setLoading]=useState(false);const{login,isAuthenticated}=useAuth();const navigate=useNavigate();const location=useLocation();const from=((_location$state=location.state)===null||_location$state===void 0?void 0:(_location$state$from=_location$state.from)===null||_location$state$from===void 0?void 0:_location$state$from.pathname)||'/';const{register,handleSubmit,formState:{errors}}=useForm({resolver:yupResolver(schema)});// Redirect if already authenticated\nReact.useEffect(()=>{if(isAuthenticated){navigate(from,{replace:true});}},[isAuthenticated,navigate,from]);const onSubmit=async data=>{try{setError('');setLoading(true);await login(data);navigate(from,{replace:true});}catch(err){var _err$response,_err$response$data;setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'Login failed. Please try again.');}finally{setLoading(false);}};return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{md:6,lg:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{className:\"text-center mb-4\",children:\"Login\"}),error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error}),/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit(onSubmit),children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Email\"}),/*#__PURE__*/_jsx(Form.Control,{type:\"email\",...register('email'),isInvalid:!!errors.email,placeholder:\"Enter your email\"}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:(_errors$email=errors.email)===null||_errors$email===void 0?void 0:_errors$email.message})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Password\"}),/*#__PURE__*/_jsx(Form.Control,{type:\"password\",...register('password'),isInvalid:!!errors.password,placeholder:\"Enter your password\"}),/*#__PURE__*/_jsx(Form.Control.Feedback,{type:\"invalid\",children:(_errors$password=errors.password)===null||_errors$password===void 0?void 0:_errors$password.message})]}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",type:\"submit\",className:\"w-100 mb-3\",disabled:loading,children:loading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{as:\"span\",animation:\"border\",size:\"sm\",role:\"status\",\"aria-hidden\":\"true\",className:\"me-2\"}),\"Logging in...\"]}):'Login'})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsx(Link,{to:\"/forgot-password\",className:\"text-decoration-none\",children:\"Forgot your password?\"})}),/*#__PURE__*/_jsx(\"hr\",{}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Don't have an account? \"}),/*#__PURE__*/_jsx(Link,{to:\"/register\",className:\"text-decoration-none\",children:\"Register here\"})]})]})})})})});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "Link", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "useForm", "yupResolver", "yup", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "schema", "object", "email", "string", "required", "password", "<PERSON><PERSON>", "_location$state", "_location$state$from", "_errors$email", "_errors$password", "error", "setError", "loading", "setLoading", "login", "isAuthenticated", "navigate", "location", "from", "state", "pathname", "register", "handleSubmit", "formState", "errors", "resolver", "useEffect", "replace", "onSubmit", "data", "err", "_err$response", "_err$response$data", "response", "message", "children", "className", "md", "lg", "Body", "Title", "variant", "Group", "Label", "Control", "type", "isInvalid", "placeholder", "<PERSON><PERSON><PERSON>", "disabled", "as", "animation", "size", "role", "to"], "sources": ["C:/laragon/www/frontend/src/pages/auth/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation, Link } from 'react-router-dom';\nimport { Container, Row, Col, Card, Form, But<PERSON>, Alert, Spinner } from 'react-bootstrap';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { LoginCredentials } from '../../services/authService';\n\nconst schema = yup.object({\n  email: yup.string().email('Invalid email').required('Email is required'),\n  password: yup.string().required('Password is required'),\n});\n\nconst Login: React.FC = () => {\n  const [error, setError] = useState<string>('');\n  const [loading, setLoading] = useState(false);\n  const { login, isAuthenticated } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const from = (location.state as any)?.from?.pathname || '/';\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<LoginCredentials>({\n    resolver: yupResolver(schema),\n  });\n\n  // Redirect if already authenticated\n  React.useEffect(() => {\n    if (isAuthenticated) {\n      navigate(from, { replace: true });\n    }\n  }, [isAuthenticated, navigate, from]);\n\n  const onSubmit = async (data: LoginCredentials) => {\n    try {\n      setError('');\n      setLoading(true);\n      await login(data);\n      navigate(from, { replace: true });\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container>\n      <Row className=\"justify-content-center\">\n        <Col md={6} lg={4}>\n          <Card>\n            <Card.Body>\n              <Card.Title className=\"text-center mb-4\">Login</Card.Title>\n              \n              {error && <Alert variant=\"danger\">{error}</Alert>}\n              \n              <Form onSubmit={handleSubmit(onSubmit)}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Email</Form.Label>\n                  <Form.Control\n                    type=\"email\"\n                    {...register('email')}\n                    isInvalid={!!errors.email}\n                    placeholder=\"Enter your email\"\n                  />\n                  <Form.Control.Feedback type=\"invalid\">\n                    {errors.email?.message}\n                  </Form.Control.Feedback>\n                </Form.Group>\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Password</Form.Label>\n                  <Form.Control\n                    type=\"password\"\n                    {...register('password')}\n                    isInvalid={!!errors.password}\n                    placeholder=\"Enter your password\"\n                  />\n                  <Form.Control.Feedback type=\"invalid\">\n                    {errors.password?.message}\n                  </Form.Control.Feedback>\n                </Form.Group>\n\n                <Button\n                  variant=\"primary\"\n                  type=\"submit\"\n                  className=\"w-100 mb-3\"\n                  disabled={loading}\n                >\n                  {loading ? (\n                    <>\n                      <Spinner\n                        as=\"span\"\n                        animation=\"border\"\n                        size=\"sm\"\n                        role=\"status\"\n                        aria-hidden=\"true\"\n                        className=\"me-2\"\n                      />\n                      Logging in...\n                    </>\n                  ) : (\n                    'Login'\n                  )}\n                </Button>\n              </Form>\n\n              <div className=\"text-center\">\n                <Link to=\"/forgot-password\" className=\"text-decoration-none\">\n                  Forgot your password?\n                </Link>\n              </div>\n              \n              <hr />\n              \n              <div className=\"text-center\">\n                <span>Don't have an account? </span>\n                <Link to=\"/register\" className=\"text-decoration-none\">\n                  Register here\n                </Link>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default Login;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,CAAEC,WAAW,CAAEC,IAAI,KAAQ,kBAAkB,CACjE,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,IAAI,CAAEC,MAAM,CAAEC,KAAK,CAAEC,OAAO,KAAQ,iBAAiB,CACzF,OAASC,OAAO,KAAQ,iBAAiB,CACzC,OAASC,WAAW,KAAQ,yBAAyB,CACrD,MAAO,GAAK,CAAAC,GAAG,KAAM,KAAK,CAC1B,OAASC,OAAO,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAGrD,KAAM,CAAAC,MAAM,CAAGR,GAAG,CAACS,MAAM,CAAC,CACxBC,KAAK,CAAEV,GAAG,CAACW,MAAM,CAAC,CAAC,CAACD,KAAK,CAAC,eAAe,CAAC,CAACE,QAAQ,CAAC,mBAAmB,CAAC,CACxEC,QAAQ,CAAEb,GAAG,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,sBAAsB,CACxD,CAAC,CAAC,CAEF,KAAM,CAAAE,KAAe,CAAGA,CAAA,GAAM,KAAAC,eAAA,CAAAC,oBAAA,CAAAC,aAAA,CAAAC,gBAAA,CAC5B,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGlC,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACmC,OAAO,CAAEC,UAAU,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAEqC,KAAK,CAAEC,eAAgB,CAAC,CAAGvB,OAAO,CAAC,CAAC,CAC5C,KAAM,CAAAwB,QAAQ,CAAGtC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAuC,QAAQ,CAAGtC,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAuC,IAAI,CAAG,EAAAZ,eAAA,CAACW,QAAQ,CAACE,KAAK,UAAAb,eAAA,kBAAAC,oBAAA,CAAfD,eAAA,CAAyBY,IAAI,UAAAX,oBAAA,iBAA7BA,oBAAA,CAA+Ba,QAAQ,GAAI,GAAG,CAE3D,KAAM,CACJC,QAAQ,CACRC,YAAY,CACZC,SAAS,CAAE,CAAEC,MAAO,CACtB,CAAC,CAAGnC,OAAO,CAAmB,CAC5BoC,QAAQ,CAAEnC,WAAW,CAACS,MAAM,CAC9B,CAAC,CAAC,CAEF;AACAvB,KAAK,CAACkD,SAAS,CAAC,IAAM,CACpB,GAAIX,eAAe,CAAE,CACnBC,QAAQ,CAACE,IAAI,CAAE,CAAES,OAAO,CAAE,IAAK,CAAC,CAAC,CACnC,CACF,CAAC,CAAE,CAACZ,eAAe,CAAEC,QAAQ,CAAEE,IAAI,CAAC,CAAC,CAErC,KAAM,CAAAU,QAAQ,CAAG,KAAO,CAAAC,IAAsB,EAAK,CACjD,GAAI,CACFlB,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAC,KAAK,CAACe,IAAI,CAAC,CACjBb,QAAQ,CAACE,IAAI,CAAE,CAAES,OAAO,CAAE,IAAK,CAAC,CAAC,CACnC,CAAE,MAAOG,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjBrB,QAAQ,CAAC,EAAAoB,aAAA,CAAAD,GAAG,CAACG,QAAQ,UAAAF,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcF,IAAI,UAAAG,kBAAA,iBAAlBA,kBAAA,CAAoBE,OAAO,GAAI,iCAAiC,CAAC,CAC5E,CAAC,OAAS,CACRrB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACEnB,IAAA,CAACb,SAAS,EAAAsD,QAAA,cACRzC,IAAA,CAACZ,GAAG,EAACsD,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cACrCzC,IAAA,CAACX,GAAG,EAACsD,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAH,QAAA,cAChBzC,IAAA,CAACV,IAAI,EAAAmD,QAAA,cACHvC,KAAA,CAACZ,IAAI,CAACuD,IAAI,EAAAJ,QAAA,eACRzC,IAAA,CAACV,IAAI,CAACwD,KAAK,EAACJ,SAAS,CAAC,kBAAkB,CAAAD,QAAA,CAAC,OAAK,CAAY,CAAC,CAE1DzB,KAAK,eAAIhB,IAAA,CAACP,KAAK,EAACsD,OAAO,CAAC,QAAQ,CAAAN,QAAA,CAAEzB,KAAK,CAAQ,CAAC,cAEjDd,KAAA,CAACX,IAAI,EAAC2C,QAAQ,CAAEN,YAAY,CAACM,QAAQ,CAAE,CAAAO,QAAA,eACrCvC,KAAA,CAACX,IAAI,CAACyD,KAAK,EAACN,SAAS,CAAC,MAAM,CAAAD,QAAA,eAC1BzC,IAAA,CAACT,IAAI,CAAC0D,KAAK,EAAAR,QAAA,CAAC,OAAK,CAAY,CAAC,cAC9BzC,IAAA,CAACT,IAAI,CAAC2D,OAAO,EACXC,IAAI,CAAC,OAAO,IACRxB,QAAQ,CAAC,OAAO,CAAC,CACrByB,SAAS,CAAE,CAAC,CAACtB,MAAM,CAACvB,KAAM,CAC1B8C,WAAW,CAAC,kBAAkB,CAC/B,CAAC,cACFrD,IAAA,CAACT,IAAI,CAAC2D,OAAO,CAACI,QAAQ,EAACH,IAAI,CAAC,SAAS,CAAAV,QAAA,EAAA3B,aAAA,CAClCgB,MAAM,CAACvB,KAAK,UAAAO,aAAA,iBAAZA,aAAA,CAAc0B,OAAO,CACD,CAAC,EACd,CAAC,cAEbtC,KAAA,CAACX,IAAI,CAACyD,KAAK,EAACN,SAAS,CAAC,MAAM,CAAAD,QAAA,eAC1BzC,IAAA,CAACT,IAAI,CAAC0D,KAAK,EAAAR,QAAA,CAAC,UAAQ,CAAY,CAAC,cACjCzC,IAAA,CAACT,IAAI,CAAC2D,OAAO,EACXC,IAAI,CAAC,UAAU,IACXxB,QAAQ,CAAC,UAAU,CAAC,CACxByB,SAAS,CAAE,CAAC,CAACtB,MAAM,CAACpB,QAAS,CAC7B2C,WAAW,CAAC,qBAAqB,CAClC,CAAC,cACFrD,IAAA,CAACT,IAAI,CAAC2D,OAAO,CAACI,QAAQ,EAACH,IAAI,CAAC,SAAS,CAAAV,QAAA,EAAA1B,gBAAA,CAClCe,MAAM,CAACpB,QAAQ,UAAAK,gBAAA,iBAAfA,gBAAA,CAAiByB,OAAO,CACJ,CAAC,EACd,CAAC,cAEbxC,IAAA,CAACR,MAAM,EACLuD,OAAO,CAAC,SAAS,CACjBI,IAAI,CAAC,QAAQ,CACbT,SAAS,CAAC,YAAY,CACtBa,QAAQ,CAAErC,OAAQ,CAAAuB,QAAA,CAEjBvB,OAAO,cACNhB,KAAA,CAAAE,SAAA,EAAAqC,QAAA,eACEzC,IAAA,CAACN,OAAO,EACN8D,EAAE,CAAC,MAAM,CACTC,SAAS,CAAC,QAAQ,CAClBC,IAAI,CAAC,IAAI,CACTC,IAAI,CAAC,QAAQ,CACb,cAAY,MAAM,CAClBjB,SAAS,CAAC,MAAM,CACjB,CAAC,gBAEJ,EAAE,CAAC,CAEH,OACD,CACK,CAAC,EACL,CAAC,cAEP1C,IAAA,QAAK0C,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1BzC,IAAA,CAACd,IAAI,EAAC0E,EAAE,CAAC,kBAAkB,CAAClB,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,uBAE7D,CAAM,CAAC,CACJ,CAAC,cAENzC,IAAA,QAAK,CAAC,cAENE,KAAA,QAAKwC,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BzC,IAAA,SAAAyC,QAAA,CAAM,yBAAuB,CAAM,CAAC,cACpCzC,IAAA,CAACd,IAAI,EAAC0E,EAAE,CAAC,WAAW,CAAClB,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CAAC,eAEtD,CAAM,CAAC,EACJ,CAAC,EACG,CAAC,CACR,CAAC,CACJ,CAAC,CACH,CAAC,CACG,CAAC,CAEhB,CAAC,CAED,cAAe,CAAA9B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}