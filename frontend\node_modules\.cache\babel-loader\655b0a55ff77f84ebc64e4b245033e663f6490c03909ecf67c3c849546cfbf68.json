{"ast": null, "code": "export { default } from \"./TabScrollButton.js\";\nexport { default as tabScrollButtonClasses } from \"./tabScrollButtonClasses.js\";\nexport * from \"./tabScrollButtonClasses.js\";", "map": {"version": 3, "names": ["default", "tabScrollButtonClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/TabScrollButton/index.js"], "sourcesContent": ["export { default } from \"./TabScrollButton.js\";\nexport { default as tabScrollButtonClasses } from \"./tabScrollButtonClasses.js\";\nexport * from \"./tabScrollButtonClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,sBAAsB;AAC9C,SAASA,OAAO,IAAIC,sBAAsB,QAAQ,6BAA6B;AAC/E,cAAc,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}