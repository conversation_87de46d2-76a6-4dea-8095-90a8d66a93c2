{"ast": null, "code": "import React,{useState,useEffect,useCallback}from'react';import{Box,Typography,Paper,Button,LinearProgress,Alert,Chip,IconButton,Dialog,DialogTitle,DialogContent,DialogActions,FormControl,InputLabel,Select,MenuItem,Card,CardContent,CardActions,Tooltip}from'@mui/material';import{CloudUpload as UploadIcon,Delete as DeleteIcon,Warning as WarningIcon,CheckCircle as CheckIcon,Error as ErrorIcon,Image as ImageIcon,PictureAsPdf as PdfIcon,Description as FileIcon}from'@mui/icons-material';import{useDropzone}from'react-dropzone';import printingService from'../../services/printingService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FileUpload=_ref=>{let{orderId,onFilesUploaded,onError}=_ref;const[settings,setSettings]=useState(null);const[uploadedFiles,setUploadedFiles]=useState([]);const[uploadingFiles,setUploadingFiles]=useState([]);const[loading,setLoading]=useState(false);const[deleteDialogOpen,setDeleteDialogOpen]=useState(false);const[fileToDelete,setFileToDelete]=useState(null);const[selectedFileType,setSelectedFileType]=useState('artwork');// Load settings and files on component mount\nuseEffect(()=>{loadSettings();if(orderId){loadUploadedFiles();}},[orderId]);const loadSettings=async()=>{try{const uploadSettings=await printingService.getUploadSettings();setSettings(uploadSettings);}catch(error){console.error('Failed to load upload settings:',error);onError===null||onError===void 0?void 0:onError('Failed to load upload settings');}};const loadUploadedFiles=async()=>{if(!orderId)return;try{const files=await printingService.getOrderFiles(orderId);setUploadedFiles(files);}catch(error){console.error('Failed to load uploaded files:',error);}};// Validate files before upload\nconst validateFiles=async files=>{if(!settings)return{valid:[],invalid:[]};const valid=[];const invalid=[];// Check total file count\nconst totalFiles=uploadedFiles.length+files.length;if(totalFiles>settings.max_files_per_order){const excess=totalFiles-settings.max_files_per_order;for(let i=files.length-excess;i<files.length;i++){invalid.push({file:files[i],errors:[`Maximum ${settings.max_files_per_order} files allowed per order`]});}files=files.slice(0,files.length-excess);}// Check total upload size\nconst currentTotalSize=uploadedFiles.reduce((sum,file)=>sum+file.file_size,0);const newFilesSize=files.reduce((sum,file)=>sum+file.size,0);const totalSize=currentTotalSize+newFilesSize;const maxTotalSize=settings.max_total_upload_size_mb*1024*1024;if(totalSize>maxTotalSize){onError===null||onError===void 0?void 0:onError(`Total upload size would exceed ${settings.max_total_upload_size_mb}MB limit`);return{valid:[],invalid:files.map(file=>({file,errors:['Total size limit exceeded']}))};}// Validate each file\nfor(const file of files){const validation=await printingService.validateFile(file);if(validation.valid){valid.push(file);}else{invalid.push({file,errors:validation.message?[validation.message]:['Validation failed']});}}return{valid,invalid};};const onDrop=useCallback(async acceptedFiles=>{if(!orderId){onError===null||onError===void 0?void 0:onError('Please create an order first before uploading files');return;}const{valid,invalid}=await validateFiles(acceptedFiles);// Show errors for invalid files\nif(invalid.length>0){const errorMessages=invalid.map(_ref2=>{let{file,errors}=_ref2;return`${file.name}: ${errors.join(', ')}`;}).join('\\n');onError===null||onError===void 0?void 0:onError(errorMessages);}// Upload valid files\nif(valid.length>0){await uploadFiles(valid);}},[orderId,settings,uploadedFiles]);const uploadFiles=async files=>{setLoading(true);// Initialize uploading files state\nconst newUploadingFiles=files.map(file=>({file,progress:0,status:'uploading'}));setUploadingFiles(newUploadingFiles);try{const uploadedFileResults=await printingService.uploadFiles(orderId,files,selectedFileType);// Update uploading files to success\nsetUploadingFiles(prev=>prev.map(uf=>({...uf,progress:100,status:'success'})));// Add to uploaded files list\nsetUploadedFiles(prev=>[...prev,...uploadedFileResults]);onFilesUploaded===null||onFilesUploaded===void 0?void 0:onFilesUploaded(uploadedFileResults);// Clear uploading files after a delay\nsetTimeout(()=>{setUploadingFiles([]);},2000);}catch(error){// Update uploading files to error\nsetUploadingFiles(prev=>prev.map(uf=>({...uf,status:'error',error:error.message||'Upload failed'})));onError===null||onError===void 0?void 0:onError(error.message||'Failed to upload files');}finally{setLoading(false);}};const handleDeleteFile=async()=>{if(!fileToDelete||!orderId)return;try{await printingService.deleteFile(orderId,fileToDelete.id);setUploadedFiles(prev=>prev.filter(f=>f.id!==fileToDelete.id));setDeleteDialogOpen(false);setFileToDelete(null);}catch(error){onError===null||onError===void 0?void 0:onError(error.message||'Failed to delete file');}};const{getRootProps,getInputProps,isDragActive}=useDropzone({onDrop,accept:settings?{'image/*':settings.allowed_file_types.filter(type=>['png','jpg','jpeg','tiff','svg'].includes(type)).map(type=>`.${type}`),'application/pdf':settings.allowed_file_types.includes('pdf')?['.pdf']:[],'application/postscript':settings.allowed_file_types.includes('eps')?['.eps']:[],'application/illustrator':settings.allowed_file_types.includes('ai')?['.ai']:[]}:undefined,maxSize:settings?settings.max_file_size_mb*1024*1024:undefined,disabled:loading||!orderId});const getFileIcon=mimeType=>{if(mimeType.startsWith('image/'))return/*#__PURE__*/_jsx(ImageIcon,{});if(mimeType==='application/pdf')return/*#__PURE__*/_jsx(PdfIcon,{});return/*#__PURE__*/_jsx(FileIcon,{});};const getStatusIcon=file=>{if(file.dpi&&settings!==null&&settings!==void 0&&settings.enable_dpi_validation){if(file.dpi>=settings.min_dpi_requirement){return/*#__PURE__*/_jsx(CheckIcon,{color:\"success\"});}else if(file.dpi>=settings.dpi_warning_threshold){return/*#__PURE__*/_jsx(WarningIcon,{color:\"warning\"});}else{return/*#__PURE__*/_jsx(ErrorIcon,{color:\"error\"});}}return file.is_approved?/*#__PURE__*/_jsx(CheckIcon,{color:\"success\"}):/*#__PURE__*/_jsx(WarningIcon,{color:\"warning\"});};const formatFileSize=bytes=>{const units=['B','KB','MB','GB'];let size=bytes;let unitIndex=0;while(size>=1024&&unitIndex<units.length-1){size/=1024;unitIndex++;}return`${size.toFixed(1)} ${units[unitIndex]}`;};if(!settings){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"200px\",children:/*#__PURE__*/_jsx(Typography,{children:\"Loading upload settings...\"})});}return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"info\",sx:{mb:2},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Upload Limits:\"}),\" Max \",settings.max_files_per_order,\" files,\",settings.max_file_size_mb,\"MB per file, \",settings.max_total_upload_size_mb,\"MB total. Allowed types: \",settings.allowed_file_types.join(', ').toUpperCase()]})}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,sx:{mb:2},children:[/*#__PURE__*/_jsx(InputLabel,{children:\"File Type\"}),/*#__PURE__*/_jsxs(Select,{value:selectedFileType,onChange:e=>setSelectedFileType(e.target.value),label:\"File Type\",children:[/*#__PURE__*/_jsx(MenuItem,{value:\"artwork\",children:\"Artwork Files\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"reference\",children:\"Reference Files\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"proof\",children:\"Proof Files\"})]})]}),/*#__PURE__*/_jsxs(Paper,{...getRootProps(),sx:{p:4,border:'2px dashed',borderColor:isDragActive?'primary.main':'grey.300',backgroundColor:isDragActive?'action.hover':'background.paper',cursor:orderId?'pointer':'not-allowed',opacity:orderId?1:0.5,textAlign:'center',mb:3,transition:'all 0.2s ease-in-out'},children:[/*#__PURE__*/_jsx(\"input\",{...getInputProps()}),/*#__PURE__*/_jsx(UploadIcon,{sx:{fontSize:48,color:'text.secondary',mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:isDragActive?'Drop files here':'Drag & drop files here'}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:2},children:\"or click to browse files\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",disabled:!orderId,children:\"Browse Files\"}),!orderId&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"error\",sx:{display:'block',mt:1},children:\"Please create an order first\"})]}),uploadingFiles.length>0&&/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Uploading Files\"}),uploadingFiles.map((uploadingFile,index)=>/*#__PURE__*/_jsx(Card,{sx:{mb:1},children:/*#__PURE__*/_jsxs(CardContent,{sx:{py:2},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:2,children:[getFileIcon(uploadingFile.file.type),/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",noWrap:true,children:uploadingFile.file.name}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:formatFileSize(uploadingFile.file.size)})]}),/*#__PURE__*/_jsxs(Box,{sx:{width:100},children:[uploadingFile.status==='uploading'&&/*#__PURE__*/_jsx(LinearProgress,{variant:\"indeterminate\",sx:{height:6,borderRadius:3}}),uploadingFile.status==='success'&&/*#__PURE__*/_jsx(Chip,{icon:/*#__PURE__*/_jsx(CheckIcon,{}),label:\"Success\",color:\"success\",size:\"small\"}),uploadingFile.status==='error'&&/*#__PURE__*/_jsx(Chip,{icon:/*#__PURE__*/_jsx(ErrorIcon,{}),label:\"Error\",color:\"error\",size:\"small\"})]})]}),uploadingFile.error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mt:1},children:uploadingFile.error}),uploadingFile.warnings&&uploadingFile.warnings.length>0&&/*#__PURE__*/_jsx(Alert,{severity:\"warning\",sx:{mt:1},children:uploadingFile.warnings.join(', ')})]})},index))]}),uploadedFiles.length>0&&/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,children:[\"Uploaded Files (\",uploadedFiles.length,\"/\",settings.max_files_per_order,\")\"]}),/*#__PURE__*/_jsx(Box,{sx:{display:'grid',gridTemplateColumns:'repeat(auto-fill, minmax(300px, 1fr))',gap:2},children:uploadedFiles.map(file=>/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,mb:1,children:[getFileIcon(file.mime_type),/*#__PURE__*/_jsx(Tooltip,{title:getStatusIcon(file),children:getStatusIcon(file)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",noWrap:true,sx:{flex:1},children:file.original_name})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",display:\"block\",children:[\"Size: \",file.formatted_file_size]}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",display:\"block\",children:[\"Type: \",file.file_type_label]}),file.dimensions&&/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",display:\"block\",children:[\"Dimensions: \",file.dimensions.width,\"\\xD7\",file.dimensions.height,\"px\"]}),file.dpi&&/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:file.dpi>=(settings.min_dpi_requirement||300)?'success.main':file.dpi>=(settings.dpi_warning_threshold||150)?'warning.main':'error.main',display:\"block\",children:[\"DPI: \",file.dpi,\" \",file.dpi_status&&`(${file.dpi_status})`]}),file.notes&&/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"text.secondary\",display:\"block\",sx:{mt:1},children:[\"Notes: \",file.notes]}),/*#__PURE__*/_jsx(Box,{display:\"flex\",gap:1,mt:1,children:/*#__PURE__*/_jsx(Chip,{label:file.is_approved?'Approved':'Pending Review',color:file.is_approved?'success':'warning',size:\"small\"})})]}),/*#__PURE__*/_jsxs(CardActions,{children:[/*#__PURE__*/_jsx(Button,{size:\"small\",href:file.file_url,target:\"_blank\",rel:\"noopener noreferrer\",children:\"View\"}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",color:\"error\",onClick:()=>{setFileToDelete(file);setDeleteDialogOpen(true);},children:/*#__PURE__*/_jsx(DeleteIcon,{})})]})]},file.id))})]}),/*#__PURE__*/_jsxs(Dialog,{open:deleteDialogOpen,onClose:()=>setDeleteDialogOpen(false),children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Delete File\"}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsxs(Typography,{children:[\"Are you sure you want to delete \\\"\",fileToDelete===null||fileToDelete===void 0?void 0:fileToDelete.original_name,\"\\\"? This action cannot be undone.\"]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDeleteDialogOpen(false),children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleDeleteFile,color:\"error\",variant:\"contained\",children:\"Delete\"})]})]})]});};export default FileUpload;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Typography", "Paper", "<PERSON><PERSON>", "LinearProgress", "<PERSON><PERSON>", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON><PERSON>", "CloudUpload", "UploadIcon", "Delete", "DeleteIcon", "Warning", "WarningIcon", "CheckCircle", "CheckIcon", "Error", "ErrorIcon", "Image", "ImageIcon", "PictureAsPdf", "PdfIcon", "Description", "FileIcon", "useDropzone", "printingService", "jsx", "_jsx", "jsxs", "_jsxs", "FileUpload", "_ref", "orderId", "onFilesUploaded", "onError", "settings", "setSettings", "uploadedFiles", "setUploadedFiles", "uploadingFiles", "setUploadingFiles", "loading", "setLoading", "deleteDialogOpen", "setDeleteDialogOpen", "fileToDelete", "setFileToDelete", "selectedFileType", "setSelectedFileType", "loadSettings", "loadUploadedFiles", "uploadSettings", "getUploadSettings", "error", "console", "files", "getOrderFiles", "validateFiles", "valid", "invalid", "totalFiles", "length", "max_files_per_order", "excess", "i", "push", "file", "errors", "slice", "currentTotalSize", "reduce", "sum", "file_size", "newFilesSize", "size", "totalSize", "maxTotalSize", "max_total_upload_size_mb", "map", "validation", "validateFile", "message", "onDrop", "acceptedFiles", "errorMessages", "_ref2", "name", "join", "uploadFiles", "newUploadingFiles", "progress", "status", "uploadedFileResults", "prev", "uf", "setTimeout", "handleDeleteFile", "deleteFile", "id", "filter", "f", "getRootProps", "getInputProps", "isDragActive", "accept", "allowed_file_types", "type", "includes", "undefined", "maxSize", "max_file_size_mb", "disabled", "getFileIcon", "mimeType", "startsWith", "getStatusIcon", "dpi", "enable_dpi_validation", "min_dpi_requirement", "color", "dpi_warning_threshold", "is_approved", "formatFileSize", "bytes", "units", "unitIndex", "toFixed", "display", "justifyContent", "alignItems", "minHeight", "children", "severity", "sx", "mb", "variant", "toUpperCase", "fullWidth", "value", "onChange", "e", "target", "label", "p", "border", "borderColor", "backgroundColor", "cursor", "opacity", "textAlign", "transition", "fontSize", "gutterBottom", "mt", "uploadingFile", "index", "py", "gap", "flex", "noWrap", "width", "height", "borderRadius", "icon", "warnings", "gridTemplateColumns", "mime_type", "title", "original_name", "formatted_file_size", "file_type_label", "dimensions", "dpi_status", "notes", "href", "file_url", "rel", "onClick", "open", "onClose"], "sources": ["C:/laragon/www/frontend/src/components/dashboard/FileUpload.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  LinearProgress,\n  Alert,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n\n  Card,\n  CardContent,\n  CardActions,\n  Tooltip,\n} from '@mui/material';\nimport {\n  CloudUpload as UploadIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckIcon,\n  Error as ErrorIcon,\n  Image as ImageIcon,\n  PictureAsPdf as PdfIcon,\n  Description as FileIcon,\n} from '@mui/icons-material';\nimport { useDropzone } from 'react-dropzone';\nimport printingService, { OrderFile } from '../../services/printingService';\n\ninterface FileUploadSettings {\n  allowed_file_types: string[];\n  max_file_size_mb: number;\n  max_total_upload_size_mb: number;\n  max_files_per_order: number;\n  min_dpi_requirement: number;\n  dpi_warning_threshold: number;\n  enable_dpi_validation: boolean;\n  min_width_px: number;\n  min_height_px: number;\n  max_width_px: number;\n  max_height_px: number;\n  enable_dimension_validation: boolean;\n}\n\ninterface FileUploadProps {\n  orderId?: number;\n  onFilesUploaded?: (files: OrderFile[]) => void;\n  onError?: (error: string) => void;\n}\n\ninterface UploadingFile {\n  file: File;\n  progress: number;\n  status: 'uploading' | 'success' | 'error';\n  error?: string;\n  warnings?: string[];\n}\n\nconst FileUpload: React.FC<FileUploadProps> = ({ orderId, onFilesUploaded, onError }) => {\n  const [settings, setSettings] = useState<FileUploadSettings | null>(null);\n  const [uploadedFiles, setUploadedFiles] = useState<OrderFile[]>([]);\n  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [fileToDelete, setFileToDelete] = useState<OrderFile | null>(null);\n  const [selectedFileType, setSelectedFileType] = useState<string>('artwork');\n\n  // Load settings and files on component mount\n  useEffect(() => {\n    loadSettings();\n    if (orderId) {\n      loadUploadedFiles();\n    }\n  }, [orderId]);\n\n  const loadSettings = async () => {\n    try {\n      const uploadSettings = await printingService.getUploadSettings();\n      setSettings(uploadSettings);\n    } catch (error) {\n      console.error('Failed to load upload settings:', error);\n      onError?.('Failed to load upload settings');\n    }\n  };\n\n  const loadUploadedFiles = async () => {\n    if (!orderId) return;\n\n    try {\n      const files = await printingService.getOrderFiles(orderId);\n      setUploadedFiles(files);\n    } catch (error) {\n      console.error('Failed to load uploaded files:', error);\n    }\n  };\n\n  // Validate files before upload\n  const validateFiles = async (files: File[]): Promise<{ valid: File[]; invalid: { file: File; errors: string[] }[] }> => {\n    if (!settings) return { valid: [], invalid: [] };\n\n    const valid: File[] = [];\n    const invalid: { file: File; errors: string[] }[] = [];\n\n    // Check total file count\n    const totalFiles = uploadedFiles.length + files.length;\n    if (totalFiles > settings.max_files_per_order) {\n      const excess = totalFiles - settings.max_files_per_order;\n      for (let i = files.length - excess; i < files.length; i++) {\n        invalid.push({\n          file: files[i],\n          errors: [`Maximum ${settings.max_files_per_order} files allowed per order`]\n        });\n      }\n      files = files.slice(0, files.length - excess);\n    }\n\n    // Check total upload size\n    const currentTotalSize = uploadedFiles.reduce((sum, file) => sum + file.file_size, 0);\n    const newFilesSize = files.reduce((sum, file) => sum + file.size, 0);\n    const totalSize = currentTotalSize + newFilesSize;\n    const maxTotalSize = settings.max_total_upload_size_mb * 1024 * 1024;\n\n    if (totalSize > maxTotalSize) {\n      onError?.(`Total upload size would exceed ${settings.max_total_upload_size_mb}MB limit`);\n      return { valid: [], invalid: files.map(file => ({ file, errors: ['Total size limit exceeded'] })) };\n    }\n\n    // Validate each file\n    for (const file of files) {\n      const validation = await printingService.validateFile(file);\n      if (validation.valid) {\n        valid.push(file);\n      } else {\n        invalid.push({\n          file,\n          errors: validation.message ? [validation.message] : ['Validation failed']\n        });\n      }\n    }\n\n    return { valid, invalid };\n  };\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    if (!orderId) {\n      onError?.('Please create an order first before uploading files');\n      return;\n    }\n\n    const { valid, invalid } = await validateFiles(acceptedFiles);\n\n    // Show errors for invalid files\n    if (invalid.length > 0) {\n      const errorMessages = invalid.map(({ file, errors }) =>\n        `${file.name}: ${errors.join(', ')}`\n      ).join('\\n');\n      onError?.(errorMessages);\n    }\n\n    // Upload valid files\n    if (valid.length > 0) {\n      await uploadFiles(valid);\n    }\n  }, [orderId, settings, uploadedFiles]);\n\n  const uploadFiles = async (files: File[]) => {\n    setLoading(true);\n\n    // Initialize uploading files state\n    const newUploadingFiles: UploadingFile[] = files.map(file => ({\n      file,\n      progress: 0,\n      status: 'uploading'\n    }));\n    setUploadingFiles(newUploadingFiles);\n\n    try {\n      const uploadedFileResults = await printingService.uploadFiles(orderId!, files, selectedFileType);\n\n      // Update uploading files to success\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        progress: 100,\n        status: 'success'\n      })));\n\n      // Add to uploaded files list\n      setUploadedFiles(prev => [...prev, ...uploadedFileResults]);\n      onFilesUploaded?.(uploadedFileResults);\n\n      // Clear uploading files after a delay\n      setTimeout(() => {\n        setUploadingFiles([]);\n      }, 2000);\n\n    } catch (error: any) {\n      // Update uploading files to error\n      setUploadingFiles(prev => prev.map(uf => ({\n        ...uf,\n        status: 'error',\n        error: error.message || 'Upload failed'\n      })));\n\n      onError?.(error.message || 'Failed to upload files');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteFile = async () => {\n    if (!fileToDelete || !orderId) return;\n\n    try {\n      await printingService.deleteFile(orderId, fileToDelete.id);\n      setUploadedFiles(prev => prev.filter(f => f.id !== fileToDelete.id));\n      setDeleteDialogOpen(false);\n      setFileToDelete(null);\n    } catch (error: any) {\n      onError?.(error.message || 'Failed to delete file');\n    }\n  };\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: settings ? {\n      'image/*': settings.allowed_file_types.filter(type =>\n        ['png', 'jpg', 'jpeg', 'tiff', 'svg'].includes(type)\n      ).map(type => `.${type}`),\n      'application/pdf': settings.allowed_file_types.includes('pdf') ? ['.pdf'] : [],\n      'application/postscript': settings.allowed_file_types.includes('eps') ? ['.eps'] : [],\n      'application/illustrator': settings.allowed_file_types.includes('ai') ? ['.ai'] : [],\n    } : undefined,\n    maxSize: settings ? settings.max_file_size_mb * 1024 * 1024 : undefined,\n    disabled: loading || !orderId\n  });\n\n  const getFileIcon = (mimeType: string) => {\n    if (mimeType.startsWith('image/')) return <ImageIcon />;\n    if (mimeType === 'application/pdf') return <PdfIcon />;\n    return <FileIcon />;\n  };\n\n  const getStatusIcon = (file: OrderFile) => {\n    if (file.dpi && settings?.enable_dpi_validation) {\n      if (file.dpi >= settings.min_dpi_requirement) {\n        return <CheckIcon color=\"success\" />;\n      } else if (file.dpi >= settings.dpi_warning_threshold) {\n        return <WarningIcon color=\"warning\" />;\n      } else {\n        return <ErrorIcon color=\"error\" />;\n      }\n    }\n    return file.is_approved ? <CheckIcon color=\"success\" /> : <WarningIcon color=\"warning\" />;\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let size = bytes;\n    let unitIndex = 0;\n\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n\n    return `${size.toFixed(1)} ${units[unitIndex]}`;\n  };\n\n  if (!settings) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"200px\">\n        <Typography>Loading upload settings...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Upload Settings Info */}\n      <Alert severity=\"info\" sx={{ mb: 2 }}>\n        <Typography variant=\"body2\">\n          <strong>Upload Limits:</strong> Max {settings.max_files_per_order} files,\n          {settings.max_file_size_mb}MB per file, {settings.max_total_upload_size_mb}MB total.\n          Allowed types: {settings.allowed_file_types.join(', ').toUpperCase()}\n        </Typography>\n      </Alert>\n\n      {/* File Type Selection */}\n      <FormControl fullWidth sx={{ mb: 2 }}>\n        <InputLabel>File Type</InputLabel>\n        <Select\n          value={selectedFileType}\n          onChange={(e) => setSelectedFileType(e.target.value)}\n          label=\"File Type\"\n        >\n          <MenuItem value=\"artwork\">Artwork Files</MenuItem>\n          <MenuItem value=\"reference\">Reference Files</MenuItem>\n          <MenuItem value=\"proof\">Proof Files</MenuItem>\n        </Select>\n      </FormControl>\n\n      {/* Drag and Drop Upload Area */}\n      <Paper\n        {...getRootProps()}\n        sx={{\n          p: 4,\n          border: '2px dashed',\n          borderColor: isDragActive ? 'primary.main' : 'grey.300',\n          backgroundColor: isDragActive ? 'action.hover' : 'background.paper',\n          cursor: orderId ? 'pointer' : 'not-allowed',\n          opacity: orderId ? 1 : 0.5,\n          textAlign: 'center',\n          mb: 3,\n          transition: 'all 0.2s ease-in-out',\n        }}\n      >\n        <input {...getInputProps()} />\n        <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />\n        <Typography variant=\"h6\" gutterBottom>\n          {isDragActive ? 'Drop files here' : 'Drag & drop files here'}\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          or click to browse files\n        </Typography>\n        <Button variant=\"outlined\" disabled={!orderId}>\n          Browse Files\n        </Button>\n        {!orderId && (\n          <Typography variant=\"caption\" color=\"error\" sx={{ display: 'block', mt: 1 }}>\n            Please create an order first\n          </Typography>\n        )}\n      </Paper>\n\n      {/* Uploading Files */}\n      {uploadingFiles.length > 0 && (\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Uploading Files\n          </Typography>\n          {uploadingFiles.map((uploadingFile, index) => (\n            <Card key={index} sx={{ mb: 1 }}>\n              <CardContent sx={{ py: 2 }}>\n                <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                  {getFileIcon(uploadingFile.file.type)}\n                  <Box sx={{ flex: 1 }}>\n                    <Typography variant=\"body2\" noWrap>\n                      {uploadingFile.file.name}\n                    </Typography>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      {formatFileSize(uploadingFile.file.size)}\n                    </Typography>\n                  </Box>\n                  <Box sx={{ width: 100 }}>\n                    {uploadingFile.status === 'uploading' && (\n                      <LinearProgress\n                        variant=\"indeterminate\"\n                        sx={{ height: 6, borderRadius: 3 }}\n                      />\n                    )}\n                    {uploadingFile.status === 'success' && (\n                      <Chip\n                        icon={<CheckIcon />}\n                        label=\"Success\"\n                        color=\"success\"\n                        size=\"small\"\n                      />\n                    )}\n                    {uploadingFile.status === 'error' && (\n                      <Chip\n                        icon={<ErrorIcon />}\n                        label=\"Error\"\n                        color=\"error\"\n                        size=\"small\"\n                      />\n                    )}\n                  </Box>\n                </Box>\n                {uploadingFile.error && (\n                  <Alert severity=\"error\" sx={{ mt: 1 }}>\n                    {uploadingFile.error}\n                  </Alert>\n                )}\n                {uploadingFile.warnings && uploadingFile.warnings.length > 0 && (\n                  <Alert severity=\"warning\" sx={{ mt: 1 }}>\n                    {uploadingFile.warnings.join(', ')}\n                  </Alert>\n                )}\n              </CardContent>\n            </Card>\n          ))}\n        </Box>\n      )}\n\n      {/* Uploaded Files List */}\n      {uploadedFiles.length > 0 && (\n        <Box>\n          <Typography variant=\"h6\" gutterBottom>\n            Uploaded Files ({uploadedFiles.length}/{settings.max_files_per_order})\n          </Typography>\n          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: 2 }}>\n            {uploadedFiles.map((file) => (\n              <Card key={file.id}>\n                <CardContent>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1} mb={1}>\n                    {getFileIcon(file.mime_type)}\n                    <Tooltip title={getStatusIcon(file)}>\n                      {getStatusIcon(file)}\n                    </Tooltip>\n                    <Typography variant=\"body2\" noWrap sx={{ flex: 1 }}>\n                      {file.original_name}\n                    </Typography>\n                  </Box>\n\n                  <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                    Size: {file.formatted_file_size}\n                  </Typography>\n\n                  <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                    Type: {file.file_type_label}\n                  </Typography>\n\n                  {file.dimensions && (\n                    <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                      Dimensions: {file.dimensions.width}×{file.dimensions.height}px\n                    </Typography>\n                  )}\n\n                  {file.dpi && (\n                    <Typography\n                      variant=\"caption\"\n                      color={\n                        file.dpi >= (settings.min_dpi_requirement || 300) ? 'success.main' :\n                        file.dpi >= (settings.dpi_warning_threshold || 150) ? 'warning.main' : 'error.main'\n                      }\n                      display=\"block\"\n                    >\n                      DPI: {file.dpi} {file.dpi_status && `(${file.dpi_status})`}\n                    </Typography>\n                  )}\n\n                  {file.notes && (\n                    <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" sx={{ mt: 1 }}>\n                      Notes: {file.notes}\n                    </Typography>\n                  )}\n\n                  <Box display=\"flex\" gap={1} mt={1}>\n                    <Chip\n                      label={file.is_approved ? 'Approved' : 'Pending Review'}\n                      color={file.is_approved ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  </Box>\n                </CardContent>\n\n                <CardActions>\n                  <Button\n                    size=\"small\"\n                    href={file.file_url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    View\n                  </Button>\n                  <IconButton\n                    size=\"small\"\n                    color=\"error\"\n                    onClick={() => {\n                      setFileToDelete(file);\n                      setDeleteDialogOpen(true);\n                    }}\n                  >\n                    <DeleteIcon />\n                  </IconButton>\n                </CardActions>\n              </Card>\n            ))}\n          </Box>\n        </Box>\n      )}\n\n      {/* Delete Confirmation Dialog */}\n      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>\n        <DialogTitle>Delete File</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Are you sure you want to delete \"{fileToDelete?.original_name}\"? This action cannot be undone.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>\n          <Button onClick={handleDeleteFile} color=\"error\" variant=\"contained\">\n            Delete\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default FileUpload;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,cAAc,CACdC,KAAK,CACLC,IAAI,CACJC,UAAU,CACVC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CAERC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,OAAO,KACF,eAAe,CACtB,OACEC,WAAW,GAAI,CAAAC,UAAU,CACzBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,WAAW,GAAI,CAAAC,SAAS,CACxBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,YAAY,GAAI,CAAAC,OAAO,CACvBC,WAAW,GAAI,CAAAC,QAAQ,KAClB,qBAAqB,CAC5B,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,MAAO,CAAAC,eAAe,KAAqB,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA+B5E,KAAM,CAAAC,UAAqC,CAAGC,IAAA,EAA2C,IAA1C,CAAEC,OAAO,CAAEC,eAAe,CAAEC,OAAQ,CAAC,CAAAH,IAAA,CAClF,KAAM,CAACI,QAAQ,CAAEC,WAAW,CAAC,CAAGnD,QAAQ,CAA4B,IAAI,CAAC,CACzE,KAAM,CAACoD,aAAa,CAAEC,gBAAgB,CAAC,CAAGrD,QAAQ,CAAc,EAAE,CAAC,CACnE,KAAM,CAACsD,cAAc,CAAEC,iBAAiB,CAAC,CAAGvD,QAAQ,CAAkB,EAAE,CAAC,CACzE,KAAM,CAACwD,OAAO,CAAEC,UAAU,CAAC,CAAGzD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC0D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3D,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC4D,YAAY,CAAEC,eAAe,CAAC,CAAG7D,QAAQ,CAAmB,IAAI,CAAC,CACxE,KAAM,CAAC8D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG/D,QAAQ,CAAS,SAAS,CAAC,CAE3E;AACAC,SAAS,CAAC,IAAM,CACd+D,YAAY,CAAC,CAAC,CACd,GAAIjB,OAAO,CAAE,CACXkB,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAAE,CAAClB,OAAO,CAAC,CAAC,CAEb,KAAM,CAAAiB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAE,cAAc,CAAG,KAAM,CAAA1B,eAAe,CAAC2B,iBAAiB,CAAC,CAAC,CAChEhB,WAAW,CAACe,cAAc,CAAC,CAC7B,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDnB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAG,gCAAgC,CAAC,CAC7C,CACF,CAAC,CAED,KAAM,CAAAgB,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAAClB,OAAO,CAAE,OAEd,GAAI,CACF,KAAM,CAAAuB,KAAK,CAAG,KAAM,CAAA9B,eAAe,CAAC+B,aAAa,CAACxB,OAAO,CAAC,CAC1DM,gBAAgB,CAACiB,KAAK,CAAC,CACzB,CAAE,MAAOF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACxD,CACF,CAAC,CAED;AACA,KAAM,CAAAI,aAAa,CAAG,KAAO,CAAAF,KAAa,EAA8E,CACtH,GAAI,CAACpB,QAAQ,CAAE,MAAO,CAAEuB,KAAK,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAEhD,KAAM,CAAAD,KAAa,CAAG,EAAE,CACxB,KAAM,CAAAC,OAA2C,CAAG,EAAE,CAEtD;AACA,KAAM,CAAAC,UAAU,CAAGvB,aAAa,CAACwB,MAAM,CAAGN,KAAK,CAACM,MAAM,CACtD,GAAID,UAAU,CAAGzB,QAAQ,CAAC2B,mBAAmB,CAAE,CAC7C,KAAM,CAAAC,MAAM,CAAGH,UAAU,CAAGzB,QAAQ,CAAC2B,mBAAmB,CACxD,IAAK,GAAI,CAAAE,CAAC,CAAGT,KAAK,CAACM,MAAM,CAAGE,MAAM,CAAEC,CAAC,CAAGT,KAAK,CAACM,MAAM,CAAEG,CAAC,EAAE,CAAE,CACzDL,OAAO,CAACM,IAAI,CAAC,CACXC,IAAI,CAAEX,KAAK,CAACS,CAAC,CAAC,CACdG,MAAM,CAAE,CAAC,WAAWhC,QAAQ,CAAC2B,mBAAmB,0BAA0B,CAC5E,CAAC,CAAC,CACJ,CACAP,KAAK,CAAGA,KAAK,CAACa,KAAK,CAAC,CAAC,CAAEb,KAAK,CAACM,MAAM,CAAGE,MAAM,CAAC,CAC/C,CAEA;AACA,KAAM,CAAAM,gBAAgB,CAAGhC,aAAa,CAACiC,MAAM,CAAC,CAACC,GAAG,CAAEL,IAAI,GAAKK,GAAG,CAAGL,IAAI,CAACM,SAAS,CAAE,CAAC,CAAC,CACrF,KAAM,CAAAC,YAAY,CAAGlB,KAAK,CAACe,MAAM,CAAC,CAACC,GAAG,CAAEL,IAAI,GAAKK,GAAG,CAAGL,IAAI,CAACQ,IAAI,CAAE,CAAC,CAAC,CACpE,KAAM,CAAAC,SAAS,CAAGN,gBAAgB,CAAGI,YAAY,CACjD,KAAM,CAAAG,YAAY,CAAGzC,QAAQ,CAAC0C,wBAAwB,CAAG,IAAI,CAAG,IAAI,CAEpE,GAAIF,SAAS,CAAGC,YAAY,CAAE,CAC5B1C,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAG,kCAAkCC,QAAQ,CAAC0C,wBAAwB,UAAU,CAAC,CACxF,MAAO,CAAEnB,KAAK,CAAE,EAAE,CAAEC,OAAO,CAAEJ,KAAK,CAACuB,GAAG,CAACZ,IAAI,GAAK,CAAEA,IAAI,CAAEC,MAAM,CAAE,CAAC,2BAA2B,CAAE,CAAC,CAAC,CAAE,CAAC,CACrG,CAEA;AACA,IAAK,KAAM,CAAAD,IAAI,GAAI,CAAAX,KAAK,CAAE,CACxB,KAAM,CAAAwB,UAAU,CAAG,KAAM,CAAAtD,eAAe,CAACuD,YAAY,CAACd,IAAI,CAAC,CAC3D,GAAIa,UAAU,CAACrB,KAAK,CAAE,CACpBA,KAAK,CAACO,IAAI,CAACC,IAAI,CAAC,CAClB,CAAC,IAAM,CACLP,OAAO,CAACM,IAAI,CAAC,CACXC,IAAI,CACJC,MAAM,CAAEY,UAAU,CAACE,OAAO,CAAG,CAACF,UAAU,CAACE,OAAO,CAAC,CAAG,CAAC,mBAAmB,CAC1E,CAAC,CAAC,CACJ,CACF,CAEA,MAAO,CAAEvB,KAAK,CAAEC,OAAQ,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAuB,MAAM,CAAG/F,WAAW,CAAC,KAAO,CAAAgG,aAAqB,EAAK,CAC1D,GAAI,CAACnD,OAAO,CAAE,CACZE,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAG,qDAAqD,CAAC,CAChE,OACF,CAEA,KAAM,CAAEwB,KAAK,CAAEC,OAAQ,CAAC,CAAG,KAAM,CAAAF,aAAa,CAAC0B,aAAa,CAAC,CAE7D;AACA,GAAIxB,OAAO,CAACE,MAAM,CAAG,CAAC,CAAE,CACtB,KAAM,CAAAuB,aAAa,CAAGzB,OAAO,CAACmB,GAAG,CAACO,KAAA,MAAC,CAAEnB,IAAI,CAAEC,MAAO,CAAC,CAAAkB,KAAA,OACjD,GAAGnB,IAAI,CAACoB,IAAI,KAAKnB,MAAM,CAACoB,IAAI,CAAC,IAAI,CAAC,EAAE,EACtC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CACZrD,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAGkD,aAAa,CAAC,CAC1B,CAEA;AACA,GAAI1B,KAAK,CAACG,MAAM,CAAG,CAAC,CAAE,CACpB,KAAM,CAAA2B,WAAW,CAAC9B,KAAK,CAAC,CAC1B,CACF,CAAC,CAAE,CAAC1B,OAAO,CAAEG,QAAQ,CAAEE,aAAa,CAAC,CAAC,CAEtC,KAAM,CAAAmD,WAAW,CAAG,KAAO,CAAAjC,KAAa,EAAK,CAC3Cb,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAA+C,iBAAkC,CAAGlC,KAAK,CAACuB,GAAG,CAACZ,IAAI,GAAK,CAC5DA,IAAI,CACJwB,QAAQ,CAAE,CAAC,CACXC,MAAM,CAAE,WACV,CAAC,CAAC,CAAC,CACHnD,iBAAiB,CAACiD,iBAAiB,CAAC,CAEpC,GAAI,CACF,KAAM,CAAAG,mBAAmB,CAAG,KAAM,CAAAnE,eAAe,CAAC+D,WAAW,CAACxD,OAAO,CAAGuB,KAAK,CAAER,gBAAgB,CAAC,CAEhG;AACAP,iBAAiB,CAACqD,IAAI,EAAIA,IAAI,CAACf,GAAG,CAACgB,EAAE,GAAK,CACxC,GAAGA,EAAE,CACLJ,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAE,SACV,CAAC,CAAC,CAAC,CAAC,CAEJ;AACArD,gBAAgB,CAACuD,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAGD,mBAAmB,CAAC,CAAC,CAC3D3D,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAG2D,mBAAmB,CAAC,CAEtC;AACAG,UAAU,CAAC,IAAM,CACfvD,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAC,CAAE,IAAI,CAAC,CAEV,CAAE,MAAOa,KAAU,CAAE,CACnB;AACAb,iBAAiB,CAACqD,IAAI,EAAIA,IAAI,CAACf,GAAG,CAACgB,EAAE,GAAK,CACxC,GAAGA,EAAE,CACLH,MAAM,CAAE,OAAO,CACftC,KAAK,CAAEA,KAAK,CAAC4B,OAAO,EAAI,eAC1B,CAAC,CAAC,CAAC,CAAC,CAEJ/C,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAGmB,KAAK,CAAC4B,OAAO,EAAI,wBAAwB,CAAC,CACtD,CAAC,OAAS,CACRvC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAsD,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CAACnD,YAAY,EAAI,CAACb,OAAO,CAAE,OAE/B,GAAI,CACF,KAAM,CAAAP,eAAe,CAACwE,UAAU,CAACjE,OAAO,CAAEa,YAAY,CAACqD,EAAE,CAAC,CAC1D5D,gBAAgB,CAACuD,IAAI,EAAIA,IAAI,CAACM,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACF,EAAE,GAAKrD,YAAY,CAACqD,EAAE,CAAC,CAAC,CACpEtD,mBAAmB,CAAC,KAAK,CAAC,CAC1BE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAE,MAAOO,KAAU,CAAE,CACnBnB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAGmB,KAAK,CAAC4B,OAAO,EAAI,uBAAuB,CAAC,CACrD,CACF,CAAC,CAED,KAAM,CAAEoB,YAAY,CAAEC,aAAa,CAAEC,YAAa,CAAC,CAAG/E,WAAW,CAAC,CAChE0D,MAAM,CACNsB,MAAM,CAAErE,QAAQ,CAAG,CACjB,SAAS,CAAEA,QAAQ,CAACsE,kBAAkB,CAACN,MAAM,CAACO,IAAI,EAChD,CAAC,KAAK,CAAE,KAAK,CAAE,MAAM,CAAE,MAAM,CAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,IAAI,CACrD,CAAC,CAAC5B,GAAG,CAAC4B,IAAI,EAAI,IAAIA,IAAI,EAAE,CAAC,CACzB,iBAAiB,CAAEvE,QAAQ,CAACsE,kBAAkB,CAACE,QAAQ,CAAC,KAAK,CAAC,CAAG,CAAC,MAAM,CAAC,CAAG,EAAE,CAC9E,wBAAwB,CAAExE,QAAQ,CAACsE,kBAAkB,CAACE,QAAQ,CAAC,KAAK,CAAC,CAAG,CAAC,MAAM,CAAC,CAAG,EAAE,CACrF,yBAAyB,CAAExE,QAAQ,CAACsE,kBAAkB,CAACE,QAAQ,CAAC,IAAI,CAAC,CAAG,CAAC,KAAK,CAAC,CAAG,EACpF,CAAC,CAAGC,SAAS,CACbC,OAAO,CAAE1E,QAAQ,CAAGA,QAAQ,CAAC2E,gBAAgB,CAAG,IAAI,CAAG,IAAI,CAAGF,SAAS,CACvEG,QAAQ,CAAEtE,OAAO,EAAI,CAACT,OACxB,CAAC,CAAC,CAEF,KAAM,CAAAgF,WAAW,CAAIC,QAAgB,EAAK,CACxC,GAAIA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAE,mBAAOvF,IAAA,CAACR,SAAS,GAAE,CAAC,CACvD,GAAI8F,QAAQ,GAAK,iBAAiB,CAAE,mBAAOtF,IAAA,CAACN,OAAO,GAAE,CAAC,CACtD,mBAAOM,IAAA,CAACJ,QAAQ,GAAE,CAAC,CACrB,CAAC,CAED,KAAM,CAAA4F,aAAa,CAAIjD,IAAe,EAAK,CACzC,GAAIA,IAAI,CAACkD,GAAG,EAAIjF,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAEkF,qBAAqB,CAAE,CAC/C,GAAInD,IAAI,CAACkD,GAAG,EAAIjF,QAAQ,CAACmF,mBAAmB,CAAE,CAC5C,mBAAO3F,IAAA,CAACZ,SAAS,EAACwG,KAAK,CAAC,SAAS,CAAE,CAAC,CACtC,CAAC,IAAM,IAAIrD,IAAI,CAACkD,GAAG,EAAIjF,QAAQ,CAACqF,qBAAqB,CAAE,CACrD,mBAAO7F,IAAA,CAACd,WAAW,EAAC0G,KAAK,CAAC,SAAS,CAAE,CAAC,CACxC,CAAC,IAAM,CACL,mBAAO5F,IAAA,CAACV,SAAS,EAACsG,KAAK,CAAC,OAAO,CAAE,CAAC,CACpC,CACF,CACA,MAAO,CAAArD,IAAI,CAACuD,WAAW,cAAG9F,IAAA,CAACZ,SAAS,EAACwG,KAAK,CAAC,SAAS,CAAE,CAAC,cAAG5F,IAAA,CAACd,WAAW,EAAC0G,KAAK,CAAC,SAAS,CAAE,CAAC,CAC3F,CAAC,CAED,KAAM,CAAAG,cAAc,CAAIC,KAAa,EAAa,CAChD,KAAM,CAAAC,KAAK,CAAG,CAAC,GAAG,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACrC,GAAI,CAAAlD,IAAI,CAAGiD,KAAK,CAChB,GAAI,CAAAE,SAAS,CAAG,CAAC,CAEjB,MAAOnD,IAAI,EAAI,IAAI,EAAImD,SAAS,CAAGD,KAAK,CAAC/D,MAAM,CAAG,CAAC,CAAE,CACnDa,IAAI,EAAI,IAAI,CACZmD,SAAS,EAAE,CACb,CAEA,MAAO,GAAGnD,IAAI,CAACoD,OAAO,CAAC,CAAC,CAAC,IAAIF,KAAK,CAACC,SAAS,CAAC,EAAE,CACjD,CAAC,CAED,GAAI,CAAC1F,QAAQ,CAAE,CACb,mBACER,IAAA,CAACvC,GAAG,EAAC2I,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAACC,SAAS,CAAC,OAAO,CAAAC,QAAA,cAC/ExG,IAAA,CAACtC,UAAU,EAAA8I,QAAA,CAAC,4BAA0B,CAAY,CAAC,CAChD,CAAC,CAEV,CAEA,mBACEtG,KAAA,CAACzC,GAAG,EAAA+I,QAAA,eAEFxG,IAAA,CAAClC,KAAK,EAAC2I,QAAQ,CAAC,MAAM,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,cACnCtG,KAAA,CAACxC,UAAU,EAACkJ,OAAO,CAAC,OAAO,CAAAJ,QAAA,eACzBxG,IAAA,WAAAwG,QAAA,CAAQ,gBAAc,CAAQ,CAAC,QAAK,CAAChG,QAAQ,CAAC2B,mBAAmB,CAAC,SAClE,CAAC3B,QAAQ,CAAC2E,gBAAgB,CAAC,eAAa,CAAC3E,QAAQ,CAAC0C,wBAAwB,CAAC,2BAC5D,CAAC1C,QAAQ,CAACsE,kBAAkB,CAAClB,IAAI,CAAC,IAAI,CAAC,CAACiD,WAAW,CAAC,CAAC,EAC1D,CAAC,CACR,CAAC,cAGR3G,KAAA,CAAC7B,WAAW,EAACyI,SAAS,MAACJ,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACnCxG,IAAA,CAAC1B,UAAU,EAAAkI,QAAA,CAAC,WAAS,CAAY,CAAC,cAClCtG,KAAA,CAAC3B,MAAM,EACLwI,KAAK,CAAE3F,gBAAiB,CACxB4F,QAAQ,CAAGC,CAAC,EAAK5F,mBAAmB,CAAC4F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACrDI,KAAK,CAAC,WAAW,CAAAX,QAAA,eAEjBxG,IAAA,CAACxB,QAAQ,EAACuI,KAAK,CAAC,SAAS,CAAAP,QAAA,CAAC,eAAa,CAAU,CAAC,cAClDxG,IAAA,CAACxB,QAAQ,EAACuI,KAAK,CAAC,WAAW,CAAAP,QAAA,CAAC,iBAAe,CAAU,CAAC,cACtDxG,IAAA,CAACxB,QAAQ,EAACuI,KAAK,CAAC,OAAO,CAAAP,QAAA,CAAC,aAAW,CAAU,CAAC,EACxC,CAAC,EACE,CAAC,cAGdtG,KAAA,CAACvC,KAAK,KACA+G,YAAY,CAAC,CAAC,CAClBgC,EAAE,CAAE,CACFU,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,YAAY,CACpBC,WAAW,CAAE1C,YAAY,CAAG,cAAc,CAAG,UAAU,CACvD2C,eAAe,CAAE3C,YAAY,CAAG,cAAc,CAAG,kBAAkB,CACnE4C,MAAM,CAAEnH,OAAO,CAAG,SAAS,CAAG,aAAa,CAC3CoH,OAAO,CAAEpH,OAAO,CAAG,CAAC,CAAG,GAAG,CAC1BqH,SAAS,CAAE,QAAQ,CACnBf,EAAE,CAAE,CAAC,CACLgB,UAAU,CAAE,sBACd,CAAE,CAAAnB,QAAA,eAEFxG,IAAA,aAAW2E,aAAa,CAAC,CAAC,CAAG,CAAC,cAC9B3E,IAAA,CAAClB,UAAU,EAAC4H,EAAE,CAAE,CAAEkB,QAAQ,CAAE,EAAE,CAAEhC,KAAK,CAAE,gBAAgB,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cACpE3G,IAAA,CAACtC,UAAU,EAACkJ,OAAO,CAAC,IAAI,CAACiB,YAAY,MAAArB,QAAA,CAClC5B,YAAY,CAAG,iBAAiB,CAAG,wBAAwB,CAClD,CAAC,cACb5E,IAAA,CAACtC,UAAU,EAACkJ,OAAO,CAAC,OAAO,CAAChB,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CAAC,0BAElE,CAAY,CAAC,cACbxG,IAAA,CAACpC,MAAM,EAACgJ,OAAO,CAAC,UAAU,CAACxB,QAAQ,CAAE,CAAC/E,OAAQ,CAAAmG,QAAA,CAAC,cAE/C,CAAQ,CAAC,CACR,CAACnG,OAAO,eACPL,IAAA,CAACtC,UAAU,EAACkJ,OAAO,CAAC,SAAS,CAAChB,KAAK,CAAC,OAAO,CAACc,EAAE,CAAE,CAAEN,OAAO,CAAE,OAAO,CAAE0B,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,CAAC,8BAE7E,CAAY,CACb,EACI,CAAC,CAGP5F,cAAc,CAACsB,MAAM,CAAG,CAAC,eACxBhC,KAAA,CAACzC,GAAG,EAACiJ,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjBxG,IAAA,CAACtC,UAAU,EAACkJ,OAAO,CAAC,IAAI,CAACiB,YAAY,MAAArB,QAAA,CAAC,iBAEtC,CAAY,CAAC,CACZ5F,cAAc,CAACuC,GAAG,CAAC,CAAC4E,aAAa,CAAEC,KAAK,gBACvChI,IAAA,CAACvB,IAAI,EAAaiI,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtG,KAAA,CAACxB,WAAW,EAACgI,EAAE,CAAE,CAAEuB,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,eACzBtG,KAAA,CAACzC,GAAG,EAAC2I,OAAO,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAAC4B,GAAG,CAAE,CAAE,CAAA1B,QAAA,EAC5CnB,WAAW,CAAC0C,aAAa,CAACxF,IAAI,CAACwC,IAAI,CAAC,cACrC7E,KAAA,CAACzC,GAAG,EAACiJ,EAAE,CAAE,CAAEyB,IAAI,CAAE,CAAE,CAAE,CAAA3B,QAAA,eACnBxG,IAAA,CAACtC,UAAU,EAACkJ,OAAO,CAAC,OAAO,CAACwB,MAAM,MAAA5B,QAAA,CAC/BuB,aAAa,CAACxF,IAAI,CAACoB,IAAI,CACd,CAAC,cACb3D,IAAA,CAACtC,UAAU,EAACkJ,OAAO,CAAC,SAAS,CAAChB,KAAK,CAAC,gBAAgB,CAAAY,QAAA,CACjDT,cAAc,CAACgC,aAAa,CAACxF,IAAI,CAACQ,IAAI,CAAC,CAC9B,CAAC,EACV,CAAC,cACN7C,KAAA,CAACzC,GAAG,EAACiJ,EAAE,CAAE,CAAE2B,KAAK,CAAE,GAAI,CAAE,CAAA7B,QAAA,EACrBuB,aAAa,CAAC/D,MAAM,GAAK,WAAW,eACnChE,IAAA,CAACnC,cAAc,EACb+I,OAAO,CAAC,eAAe,CACvBF,EAAE,CAAE,CAAE4B,MAAM,CAAE,CAAC,CAAEC,YAAY,CAAE,CAAE,CAAE,CACpC,CACF,CACAR,aAAa,CAAC/D,MAAM,GAAK,SAAS,eACjChE,IAAA,CAACjC,IAAI,EACHyK,IAAI,cAAExI,IAAA,CAACZ,SAAS,GAAE,CAAE,CACpB+H,KAAK,CAAC,SAAS,CACfvB,KAAK,CAAC,SAAS,CACf7C,IAAI,CAAC,OAAO,CACb,CACF,CACAgF,aAAa,CAAC/D,MAAM,GAAK,OAAO,eAC/BhE,IAAA,CAACjC,IAAI,EACHyK,IAAI,cAAExI,IAAA,CAACV,SAAS,GAAE,CAAE,CACpB6H,KAAK,CAAC,OAAO,CACbvB,KAAK,CAAC,OAAO,CACb7C,IAAI,CAAC,OAAO,CACb,CACF,EACE,CAAC,EACH,CAAC,CACLgF,aAAa,CAACrG,KAAK,eAClB1B,IAAA,CAAClC,KAAK,EAAC2I,QAAQ,CAAC,OAAO,CAACC,EAAE,CAAE,CAAEoB,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,CACnCuB,aAAa,CAACrG,KAAK,CACf,CACR,CACAqG,aAAa,CAACU,QAAQ,EAAIV,aAAa,CAACU,QAAQ,CAACvG,MAAM,CAAG,CAAC,eAC1DlC,IAAA,CAAClC,KAAK,EAAC2I,QAAQ,CAAC,SAAS,CAACC,EAAE,CAAE,CAAEoB,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,CACrCuB,aAAa,CAACU,QAAQ,CAAC7E,IAAI,CAAC,IAAI,CAAC,CAC7B,CACR,EACU,CAAC,EA/CLoE,KAgDL,CACP,CAAC,EACC,CACN,CAGAtH,aAAa,CAACwB,MAAM,CAAG,CAAC,eACvBhC,KAAA,CAACzC,GAAG,EAAA+I,QAAA,eACFtG,KAAA,CAACxC,UAAU,EAACkJ,OAAO,CAAC,IAAI,CAACiB,YAAY,MAAArB,QAAA,EAAC,kBACpB,CAAC9F,aAAa,CAACwB,MAAM,CAAC,GAAC,CAAC1B,QAAQ,CAAC2B,mBAAmB,CAAC,GACvE,EAAY,CAAC,cACbnC,IAAA,CAACvC,GAAG,EAACiJ,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAEsC,mBAAmB,CAAE,uCAAuC,CAAER,GAAG,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAChG9F,aAAa,CAACyC,GAAG,CAAEZ,IAAI,eACtBrC,KAAA,CAACzB,IAAI,EAAA+H,QAAA,eACHtG,KAAA,CAACxB,WAAW,EAAA8H,QAAA,eACVtG,KAAA,CAACzC,GAAG,EAAC2I,OAAO,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAAC4B,GAAG,CAAE,CAAE,CAACvB,EAAE,CAAE,CAAE,CAAAH,QAAA,EACnDnB,WAAW,CAAC9C,IAAI,CAACoG,SAAS,CAAC,cAC5B3I,IAAA,CAACpB,OAAO,EAACgK,KAAK,CAAEpD,aAAa,CAACjD,IAAI,CAAE,CAAAiE,QAAA,CACjChB,aAAa,CAACjD,IAAI,CAAC,CACb,CAAC,cACVvC,IAAA,CAACtC,UAAU,EAACkJ,OAAO,CAAC,OAAO,CAACwB,MAAM,MAAC1B,EAAE,CAAE,CAAEyB,IAAI,CAAE,CAAE,CAAE,CAAA3B,QAAA,CAChDjE,IAAI,CAACsG,aAAa,CACT,CAAC,EACV,CAAC,cAEN3I,KAAA,CAACxC,UAAU,EAACkJ,OAAO,CAAC,SAAS,CAAChB,KAAK,CAAC,gBAAgB,CAACQ,OAAO,CAAC,OAAO,CAAAI,QAAA,EAAC,QAC7D,CAACjE,IAAI,CAACuG,mBAAmB,EACrB,CAAC,cAEb5I,KAAA,CAACxC,UAAU,EAACkJ,OAAO,CAAC,SAAS,CAAChB,KAAK,CAAC,gBAAgB,CAACQ,OAAO,CAAC,OAAO,CAAAI,QAAA,EAAC,QAC7D,CAACjE,IAAI,CAACwG,eAAe,EACjB,CAAC,CAEZxG,IAAI,CAACyG,UAAU,eACd9I,KAAA,CAACxC,UAAU,EAACkJ,OAAO,CAAC,SAAS,CAAChB,KAAK,CAAC,gBAAgB,CAACQ,OAAO,CAAC,OAAO,CAAAI,QAAA,EAAC,cACvD,CAACjE,IAAI,CAACyG,UAAU,CAACX,KAAK,CAAC,MAAC,CAAC9F,IAAI,CAACyG,UAAU,CAACV,MAAM,CAAC,IAC9D,EAAY,CACb,CAEA/F,IAAI,CAACkD,GAAG,eACPvF,KAAA,CAACxC,UAAU,EACTkJ,OAAO,CAAC,SAAS,CACjBhB,KAAK,CACHrD,IAAI,CAACkD,GAAG,GAAKjF,QAAQ,CAACmF,mBAAmB,EAAI,GAAG,CAAC,CAAG,cAAc,CAClEpD,IAAI,CAACkD,GAAG,GAAKjF,QAAQ,CAACqF,qBAAqB,EAAI,GAAG,CAAC,CAAG,cAAc,CAAG,YACxE,CACDO,OAAO,CAAC,OAAO,CAAAI,QAAA,EAChB,OACM,CAACjE,IAAI,CAACkD,GAAG,CAAC,GAAC,CAAClD,IAAI,CAAC0G,UAAU,EAAI,IAAI1G,IAAI,CAAC0G,UAAU,GAAG,EAChD,CACb,CAEA1G,IAAI,CAAC2G,KAAK,eACThJ,KAAA,CAACxC,UAAU,EAACkJ,OAAO,CAAC,SAAS,CAAChB,KAAK,CAAC,gBAAgB,CAACQ,OAAO,CAAC,OAAO,CAACM,EAAE,CAAE,CAAEoB,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,EAAC,SAC3E,CAACjE,IAAI,CAAC2G,KAAK,EACR,CACb,cAEDlJ,IAAA,CAACvC,GAAG,EAAC2I,OAAO,CAAC,MAAM,CAAC8B,GAAG,CAAE,CAAE,CAACJ,EAAE,CAAE,CAAE,CAAAtB,QAAA,cAChCxG,IAAA,CAACjC,IAAI,EACHoJ,KAAK,CAAE5E,IAAI,CAACuD,WAAW,CAAG,UAAU,CAAG,gBAAiB,CACxDF,KAAK,CAAErD,IAAI,CAACuD,WAAW,CAAG,SAAS,CAAG,SAAU,CAChD/C,IAAI,CAAC,OAAO,CACb,CAAC,CACC,CAAC,EACK,CAAC,cAEd7C,KAAA,CAACvB,WAAW,EAAA6H,QAAA,eACVxG,IAAA,CAACpC,MAAM,EACLmF,IAAI,CAAC,OAAO,CACZoG,IAAI,CAAE5G,IAAI,CAAC6G,QAAS,CACpBlC,MAAM,CAAC,QAAQ,CACfmC,GAAG,CAAC,qBAAqB,CAAA7C,QAAA,CAC1B,MAED,CAAQ,CAAC,cACTxG,IAAA,CAAChC,UAAU,EACT+E,IAAI,CAAC,OAAO,CACZ6C,KAAK,CAAC,OAAO,CACb0D,OAAO,CAAEA,CAAA,GAAM,CACbnI,eAAe,CAACoB,IAAI,CAAC,CACrBtB,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAE,CAAAuF,QAAA,cAEFxG,IAAA,CAAChB,UAAU,GAAE,CAAC,CACJ,CAAC,EACF,CAAC,GAzELuD,IAAI,CAACgC,EA0EV,CACP,CAAC,CACC,CAAC,EACH,CACN,cAGDrE,KAAA,CAACjC,MAAM,EAACsL,IAAI,CAAEvI,gBAAiB,CAACwI,OAAO,CAAEA,CAAA,GAAMvI,mBAAmB,CAAC,KAAK,CAAE,CAAAuF,QAAA,eACxExG,IAAA,CAAC9B,WAAW,EAAAsI,QAAA,CAAC,aAAW,CAAa,CAAC,cACtCxG,IAAA,CAAC7B,aAAa,EAAAqI,QAAA,cACZtG,KAAA,CAACxC,UAAU,EAAA8I,QAAA,EAAC,oCACuB,CAACtF,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE2H,aAAa,CAAC,mCAChE,EAAY,CAAC,CACA,CAAC,cAChB3I,KAAA,CAAC9B,aAAa,EAAAoI,QAAA,eACZxG,IAAA,CAACpC,MAAM,EAAC0L,OAAO,CAAEA,CAAA,GAAMrI,mBAAmB,CAAC,KAAK,CAAE,CAAAuF,QAAA,CAAC,QAAM,CAAQ,CAAC,cAClExG,IAAA,CAACpC,MAAM,EAAC0L,OAAO,CAAEjF,gBAAiB,CAACuB,KAAK,CAAC,OAAO,CAACgB,OAAO,CAAC,WAAW,CAAAJ,QAAA,CAAC,QAErE,CAAQ,CAAC,EACI,CAAC,EACV,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAArG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}