# Database Schema Documentation

## Overview

This `schema.sql` file contains the complete database structure for the wallet-based application, reflecting the current state after migrating from a credit-based system to a wallet-based monetary system using Malaysian Ringgit (MYR).

## Key Features

### 🏦 Wallet-Based Monetary System
- **Currency**: Malaysian Ringgit (MYR) with `DECIMAL(10,2)` precision
- **User Balance**: `wallet_balance` field in users table (migrated from `credit_balance`)
- **Transaction Types**: `top_up`, `payment`, `withdrawal`, `refund`, `bonus`, `adjustment`
- **1:1 Conversion**: Seamless migration from credit system with no data loss

### 💳 Payment Integration
- **Billplz Gateway**: Full integration with payment reference tracking
- **Payment Status**: Comprehensive status tracking (`pending`, `completed`, `failed`, etc.)
- **Withdrawal Support**: Complete withdrawal functionality with processing tracking

### 🖨️ Printing Order Management
- **Categories & Products**: Hierarchical product catalog
- **Order Processing**: Full order lifecycle management
- **File Management**: Upload and approval system for artwork files
- **Pricing**: Flexible pricing with specifications and options

### 📧 Communication System
- **Email Templates**: Customizable email templates for notifications
- **Notification Tracking**: Complete email delivery tracking
- **CMS Integration**: Content management for pages and announcements

## Usage Instructions

### 1. Fresh Installation

```bash
# Import the complete schema
mysql -u username -p database_name < schema.sql
```

### 2. Validation

```bash
# Run the validation test
node test-schema.js
```

### 3. Seeding Data

After importing the schema, run the Laravel seeders:

```bash
cd backend
php artisan db:seed
```

## Table Structure

### Core Tables
- **`users`** - User accounts with wallet balance
- **`wallet_transactions`** - All monetary transactions
- **`credit_packages`** - Top-up packages (legacy name maintained for compatibility)
- **`payment_settings`** - Payment gateway configuration

### Printing System
- **`printing_categories`** - Service categories
- **`printing_products`** - Available products/services
- **`printing_orders`** - Customer orders
- **`order_items`** - Individual order line items
- **`order_files`** - Uploaded artwork and files

### Content & Communication
- **`cms_pages`** - Website content pages
- **`email_templates`** - Email notification templates
- **`email_notifications`** - Email delivery tracking
- **`file_upload_settings`** - File upload configuration

### Authentication
- **`password_reset_tokens`** - Password reset functionality
- **`sessions`** - User session management

## Migration Notes

### From Credit System
- ✅ `credit_balance` → `wallet_balance` (DECIMAL precision)
- ✅ `credit_transactions` → `wallet_transactions` (enhanced features)
- ✅ Transaction types updated for wallet terminology
- ✅ All existing data preserved with 1:1 conversion

### Currency Handling
- **Precision**: All monetary fields use `DECIMAL(10,2)`
- **Currency**: Malaysian Ringgit (MYR)
- **Range**: Supports up to RM 99,999,999.99
- **Validation**: Application-level overdraft prevention

## Performance Optimizations

### Indexes
- **Composite Indexes**: Multi-column indexes for common query patterns
- **Foreign Keys**: Proper relationships with cascade rules
- **Balance Queries**: Optimized for admin dashboard queries
- **Transaction History**: Efficient pagination and filtering

### Recommended Queries
```sql
-- User wallet balance summary
SELECT id, name, email, wallet_balance 
FROM users 
WHERE is_active = 1 
ORDER BY wallet_balance DESC;

-- Recent transactions
SELECT wt.*, u.name as user_name 
FROM wallet_transactions wt 
JOIN users u ON wt.user_id = u.id 
WHERE wt.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY wt.created_at DESC;

-- Order revenue summary
SELECT DATE(created_at) as date, 
       COUNT(*) as orders, 
       SUM(total_amount) as revenue
FROM printing_orders 
WHERE payment_status = 'paid'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

## Backup Recommendations

### Critical Tables (High Priority)
- `wallet_transactions` - Financial transaction history
- `users` - User accounts and wallet balances
- `printing_orders` - Customer orders
- `payment_settings` - Payment configuration

### Backup Strategy
```bash
# Daily backup of financial data
mysqldump -u username -p database_name wallet_transactions users > backup_financial_$(date +%Y%m%d).sql

# Weekly full backup
mysqldump -u username -p database_name > backup_full_$(date +%Y%m%d).sql
```

## Security Considerations

### Data Protection
- **Encrypted Settings**: Payment gateway credentials encrypted
- **Foreign Keys**: Cascade deletes prevent orphaned records
- **Constraints**: Database-level data integrity
- **Audit Trail**: Complete transaction history preservation

### Access Control
- **Admin Users**: Role-based access control
- **User Isolation**: Users can only access their own data
- **Payment Security**: Secure payment reference handling

## Troubleshooting

### Common Issues

1. **Foreign Key Errors**
   ```sql
   SET FOREIGN_KEY_CHECKS = 0;
   -- Run your operations
   SET FOREIGN_KEY_CHECKS = 1;
   ```

2. **Decimal Precision**
   - Always use `DECIMAL(10,2)` for monetary values
   - Avoid `FLOAT` or `DOUBLE` for currency

3. **Character Encoding**
   - Ensure `utf8mb4` collation for proper Unicode support
   - Required for emoji and international characters

### Validation Commands
```sql
-- Check table structure
DESCRIBE users;
DESCRIBE wallet_transactions;

-- Verify foreign keys
SELECT * FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'your_database_name';

-- Check indexes
SHOW INDEX FROM wallet_transactions;
```

## Support

For issues or questions regarding the database schema:
1. Run the validation test: `node test-schema.js`
2. Check Laravel migration files in `backend/database/migrations/`
3. Review model relationships in `backend/app/Models/`

---

**Generated**: 2025-07-18  
**Version**: 1.0 (Wallet System)  
**Compatibility**: Laravel 11, MySQL 8.0+
