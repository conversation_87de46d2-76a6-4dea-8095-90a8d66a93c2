{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport { createStack } from '@mui/system';\nimport styled from \"../styles/styled.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nconst Stack = createStack({\n  createStyledComponent: styled('div', {\n    name: '<PERSON><PERSON><PERSON>ta<PERSON>',\n    slot: 'Root'\n  }),\n  useThemeProps: inProps => useDefaultProps({\n    props: inProps,\n    name: '<PERSON><PERSON><PERSON>ta<PERSON>'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Stack.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'column'\n   */\n  direction: PropTypes.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Add an element between each child.\n   */\n  divider: PropTypes.node,\n  /**\n   * Defines the space between immediate children.\n   * @default 0\n   */\n  spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * The system prop, which allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * If `true`, the CSS flexbox `gap` is used instead of applying `margin` to children.\n   *\n   * While CSS `gap` removes the [known limitations](https://mui.com/joy-ui/react-stack/#limitations),\n   * it is not fully supported in some browsers. We recommend checking https://caniuse.com/?search=flex%20gap before using this flag.\n   *\n   * To enable this flag globally, follow the [theme's default props](https://mui.com/material-ui/customization/theme-components/#default-props) configuration.\n   * @default false\n   */\n  useFlexGap: PropTypes.bool\n} : void 0;\nexport default Stack;", "map": {"version": 3, "names": ["PropTypes", "createStack", "styled", "useDefaultProps", "<PERSON><PERSON>", "createStyledComponent", "name", "slot", "useThemeProps", "inProps", "props", "process", "env", "NODE_ENV", "propTypes", "children", "node", "component", "elementType", "direction", "oneOfType", "oneOf", "arrayOf", "object", "divider", "spacing", "number", "string", "sx", "func", "bool", "useFlexGap"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Stack/Stack.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport { createStack } from '@mui/system';\nimport styled from \"../styles/styled.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nconst Stack = createStack({\n  createStyledComponent: styled('div', {\n    name: '<PERSON><PERSON><PERSON>ta<PERSON>',\n    slot: 'Root'\n  }),\n  useThemeProps: inProps => useDefaultProps({\n    props: inProps,\n    name: '<PERSON><PERSON><PERSON>ta<PERSON>'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Stack.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'column'\n   */\n  direction: PropTypes.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Add an element between each child.\n   */\n  divider: PropTypes.node,\n  /**\n   * Defines the space between immediate children.\n   * @default 0\n   */\n  spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * The system prop, which allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * If `true`, the CSS flexbox `gap` is used instead of applying `margin` to children.\n   *\n   * While CSS `gap` removes the [known limitations](https://mui.com/joy-ui/react-stack/#limitations),\n   * it is not fully supported in some browsers. We recommend checking https://caniuse.com/?search=flex%20gap before using this flag.\n   *\n   * To enable this flag globally, follow the [theme's default props](https://mui.com/material-ui/customization/theme-components/#default-props) configuration.\n   * @default false\n   */\n  useFlexGap: PropTypes.bool\n} : void 0;\nexport default Stack;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,eAAe,QAAQ,kCAAkC;AAClE,MAAMC,KAAK,GAAGH,WAAW,CAAC;EACxBI,qBAAqB,EAAEH,MAAM,CAAC,KAAK,EAAE;IACnCI,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;EACR,CAAC,CAAC;EACFC,aAAa,EAAEC,OAAO,IAAIN,eAAe,CAAC;IACxCO,KAAK,EAAED,OAAO;IACdH,IAAI,EAAE;EACR,CAAC;AACH,CAAC,CAAC;AACFK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGT,KAAK,CAACU,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEf,SAAS,CAACgB,IAAI;EACxB;AACF;AACA;AACA;EACEC,SAAS,EAAEjB,SAAS,CAACkB,WAAW;EAChC;AACF;AACA;AACA;AACA;EACEC,SAAS,EAAEnB,SAAS,CAACoB,SAAS,CAAC,CAACpB,SAAS,CAACqB,KAAK,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,EAAErB,SAAS,CAACsB,OAAO,CAACtB,SAAS,CAACqB,KAAK,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC,EAAErB,SAAS,CAACuB,MAAM,CAAC,CAAC;EAC/M;AACF;AACA;EACEC,OAAO,EAAExB,SAAS,CAACgB,IAAI;EACvB;AACF;AACA;AACA;EACES,OAAO,EAAEzB,SAAS,CAACoB,SAAS,CAAC,CAACpB,SAAS,CAACsB,OAAO,CAACtB,SAAS,CAACoB,SAAS,CAAC,CAACpB,SAAS,CAAC0B,MAAM,EAAE1B,SAAS,CAAC2B,MAAM,CAAC,CAAC,CAAC,EAAE3B,SAAS,CAAC0B,MAAM,EAAE1B,SAAS,CAACuB,MAAM,EAAEvB,SAAS,CAAC2B,MAAM,CAAC,CAAC;EAClK;AACF;AACA;EACEC,EAAE,EAAE5B,SAAS,CAACoB,SAAS,CAAC,CAACpB,SAAS,CAACsB,OAAO,CAACtB,SAAS,CAACoB,SAAS,CAAC,CAACpB,SAAS,CAAC6B,IAAI,EAAE7B,SAAS,CAACuB,MAAM,EAAEvB,SAAS,CAAC8B,IAAI,CAAC,CAAC,CAAC,EAAE9B,SAAS,CAAC6B,IAAI,EAAE7B,SAAS,CAACuB,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEQ,UAAU,EAAE/B,SAAS,CAAC8B;AACxB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe1B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}