{"ast": null, "code": "import React,{useState}from'react';import{Box,Typography,Paper,Grid2 as <PERSON>rid,TextField,Button,Switch,FormControlLabel,<PERSON><PERSON><PERSON>,<PERSON><PERSON>}from'@mui/material';import{Save}from'@mui/icons-material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Settings=()=>{const[settings,setSettings]=useState({siteName:'Material Dashboard',siteDescription:'A modern dashboard built with Material-UI',emailNotifications:true,pushNotifications:false,darkMode:false,language:'en'});const[saved,setSaved]=useState(false);const handleInputChange=field=>event=>{setSettings({...settings,[field]:event.target.value});};const handleSwitchChange=field=>event=>{setSettings({...settings,[field]:event.target.checked});};const handleSave=()=>{// Here you would typically save to your backend\nconsole.log('Saving settings:',settings);setSaved(true);setTimeout(()=>setSaved(false),3000);};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:\"Settings\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"textSecondary\",paragraph:true,children:\"Manage your application settings and preferences.\"}),saved&&/*#__PURE__*/_jsx(Alert,{severity:\"success\",sx:{mb:3},children:\"Settings saved successfully!\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"General Settings\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:2},children:[/*#__PURE__*/_jsx(TextField,{label:\"Site Name\",value:settings.siteName,onChange:handleInputChange('siteName'),fullWidth:true}),/*#__PURE__*/_jsx(TextField,{label:\"Site Description\",value:settings.siteDescription,onChange:handleInputChange('siteDescription'),multiline:true,rows:3,fullWidth:true}),/*#__PURE__*/_jsxs(TextField,{label:\"Language\",value:settings.language,onChange:handleInputChange('language'),fullWidth:true,select:true,SelectProps:{native:true},children:[/*#__PURE__*/_jsx(\"option\",{value:\"en\",children:\"English\"}),/*#__PURE__*/_jsx(\"option\",{value:\"es\",children:\"Spanish\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fr\",children:\"French\"}),/*#__PURE__*/_jsx(\"option\",{value:\"de\",children:\"German\"})]})]})]})}),/*#__PURE__*/_jsx(Grid,{xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Notifications\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:2},children:[/*#__PURE__*/_jsx(FormControlLabel,{control:/*#__PURE__*/_jsx(Switch,{checked:settings.emailNotifications,onChange:handleSwitchChange('emailNotifications')}),label:\"Email Notifications\"}),/*#__PURE__*/_jsx(FormControlLabel,{control:/*#__PURE__*/_jsx(Switch,{checked:settings.pushNotifications,onChange:handleSwitchChange('pushNotifications')}),label:\"Push Notifications\"}),/*#__PURE__*/_jsx(Divider,{sx:{my:1}}),/*#__PURE__*/_jsx(FormControlLabel,{control:/*#__PURE__*/_jsx(Switch,{checked:settings.darkMode,onChange:handleSwitchChange('darkMode')}),label:\"Dark Mode\"})]})]})}),/*#__PURE__*/_jsx(Grid,{xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Security Settings\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",paragraph:true,children:\"Manage your security preferences and authentication settings.\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2},children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",children:\"Change Password\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",children:\"Two-Factor Authentication\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",children:\"API Keys\"})]})]})}),/*#__PURE__*/_jsx(Grid,{xs:12,children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'flex-end',gap:2},children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",children:\"Reset to Defaults\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Save,{}),onClick:handleSave,children:\"Save Settings\"})]})})]})]});};export default Settings;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "Grid2", "Grid", "TextField", "<PERSON><PERSON>", "Switch", "FormControlLabel", "Divider", "<PERSON><PERSON>", "Save", "jsx", "_jsx", "jsxs", "_jsxs", "Settings", "settings", "setSettings", "siteName", "siteDescription", "emailNotifications", "pushNotifications", "darkMode", "language", "saved", "setSaved", "handleInputChange", "field", "event", "target", "value", "handleSwitchChange", "checked", "handleSave", "console", "log", "setTimeout", "children", "variant", "component", "gutterBottom", "color", "paragraph", "severity", "sx", "mb", "container", "spacing", "xs", "md", "p", "display", "flexDirection", "gap", "label", "onChange", "fullWidth", "multiline", "rows", "select", "SelectProps", "native", "control", "my", "justifyContent", "startIcon", "onClick"], "sources": ["C:/laragon/www/frontend/src/pages/dashboard/Settings.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid2 as <PERSON><PERSON>,\n  TextField,\n  Button,\n  Switch,\n  FormControlLabel,\n  <PERSON><PERSON>r,\n  <PERSON><PERSON>,\n} from '@mui/material';\nimport { Save } from '@mui/icons-material';\n\nconst Settings: React.FC = () => {\n  const [settings, setSettings] = useState({\n    siteName: 'Material Dashboard',\n    siteDescription: 'A modern dashboard built with Material-UI',\n    emailNotifications: true,\n    pushNotifications: false,\n    darkMode: false,\n    language: 'en',\n  });\n\n  const [saved, setSaved] = useState(false);\n\n  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {\n    setSettings({\n      ...settings,\n      [field]: event.target.value,\n    });\n  };\n\n  const handleSwitchChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {\n    setSettings({\n      ...settings,\n      [field]: event.target.checked,\n    });\n  };\n\n  const handleSave = () => {\n    // Here you would typically save to your backend\n    console.log('Saving settings:', settings);\n    setSaved(true);\n    setTimeout(() => setSaved(false), 3000);\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n        Settings\n      </Typography>\n      <Typography variant=\"body1\" color=\"textSecondary\" paragraph>\n        Manage your application settings and preferences.\n      </Typography>\n\n      {saved && (\n        <Alert severity=\"success\" sx={{ mb: 3 }}>\n          Settings saved successfully!\n        </Alert>\n      )}\n\n      <Grid container spacing={3}>\n        <Grid xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              General Settings\n            </Typography>\n            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n              <TextField\n                label=\"Site Name\"\n                value={settings.siteName}\n                onChange={handleInputChange('siteName')}\n                fullWidth\n              />\n              <TextField\n                label=\"Site Description\"\n                value={settings.siteDescription}\n                onChange={handleInputChange('siteDescription')}\n                multiline\n                rows={3}\n                fullWidth\n              />\n              <TextField\n                label=\"Language\"\n                value={settings.language}\n                onChange={handleInputChange('language')}\n                fullWidth\n                select\n                SelectProps={{\n                  native: true,\n                }}\n              >\n                <option value=\"en\">English</option>\n                <option value=\"es\">Spanish</option>\n                <option value=\"fr\">French</option>\n                <option value=\"de\">German</option>\n              </TextField>\n            </Box>\n          </Paper>\n        </Grid>\n\n        <Grid xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Notifications\n            </Typography>\n            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={settings.emailNotifications}\n                    onChange={handleSwitchChange('emailNotifications')}\n                  />\n                }\n                label=\"Email Notifications\"\n              />\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={settings.pushNotifications}\n                    onChange={handleSwitchChange('pushNotifications')}\n                  />\n                }\n                label=\"Push Notifications\"\n              />\n              <Divider sx={{ my: 1 }} />\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={settings.darkMode}\n                    onChange={handleSwitchChange('darkMode')}\n                  />\n                }\n                label=\"Dark Mode\"\n              />\n            </Box>\n          </Paper>\n        </Grid>\n\n        <Grid xs={12}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Security Settings\n            </Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\" paragraph>\n              Manage your security preferences and authentication settings.\n            </Typography>\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <Button variant=\"outlined\">\n                Change Password\n              </Button>\n              <Button variant=\"outlined\">\n                Two-Factor Authentication\n              </Button>\n              <Button variant=\"outlined\">\n                API Keys\n              </Button>\n            </Box>\n          </Paper>\n        </Grid>\n\n        <Grid xs={12}>\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n            <Button variant=\"outlined\">\n              Reset to Defaults\n            </Button>\n            <Button\n              variant=\"contained\"\n              startIcon={<Save />}\n              onClick={handleSave}\n            >\n              Save Settings\n            </Button>\n          </Box>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default Settings;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,KAAK,GAAI,CAAAC,IAAI,CACbC,SAAS,CACTC,MAAM,CACNC,MAAM,CACNC,gBAAgB,CAChBC,OAAO,CACPC,KAAK,KACA,eAAe,CACtB,OAASC,IAAI,KAAQ,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3C,KAAM,CAAAC,QAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGnB,QAAQ,CAAC,CACvCoB,QAAQ,CAAE,oBAAoB,CAC9BC,eAAe,CAAE,2CAA2C,CAC5DC,kBAAkB,CAAE,IAAI,CACxBC,iBAAiB,CAAE,KAAK,CACxBC,QAAQ,CAAE,KAAK,CACfC,QAAQ,CAAE,IACZ,CAAC,CAAC,CAEF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAEzC,KAAM,CAAA4B,iBAAiB,CAAIC,KAAa,EAAMC,KAA0C,EAAK,CAC3FX,WAAW,CAAC,CACV,GAAGD,QAAQ,CACX,CAACW,KAAK,EAAGC,KAAK,CAACC,MAAM,CAACC,KACxB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAIJ,KAAa,EAAMC,KAA0C,EAAK,CAC5FX,WAAW,CAAC,CACV,GAAGD,QAAQ,CACX,CAACW,KAAK,EAAGC,KAAK,CAACC,MAAM,CAACG,OACxB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB;AACAC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEnB,QAAQ,CAAC,CACzCS,QAAQ,CAAC,IAAI,CAAC,CACdW,UAAU,CAAC,IAAMX,QAAQ,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CACzC,CAAC,CAED,mBACEX,KAAA,CAACf,GAAG,EAAAsC,QAAA,eACFzB,IAAA,CAACZ,UAAU,EAACsC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAH,QAAA,CAAC,UAErD,CAAY,CAAC,cACbzB,IAAA,CAACZ,UAAU,EAACsC,OAAO,CAAC,OAAO,CAACG,KAAK,CAAC,eAAe,CAACC,SAAS,MAAAL,QAAA,CAAC,mDAE5D,CAAY,CAAC,CAEZb,KAAK,eACJZ,IAAA,CAACH,KAAK,EAACkC,QAAQ,CAAC,SAAS,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,CAAC,8BAEzC,CAAO,CACR,cAEDvB,KAAA,CAACX,IAAI,EAAC2C,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAV,QAAA,eACzBzB,IAAA,CAACT,IAAI,EAAC6C,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cAClBvB,KAAA,CAACb,KAAK,EAAC2C,EAAE,CAAE,CAAEM,CAAC,CAAE,CAAE,CAAE,CAAAb,QAAA,eAClBzB,IAAA,CAACZ,UAAU,EAACsC,OAAO,CAAC,IAAI,CAACE,YAAY,MAAAH,QAAA,CAAC,kBAEtC,CAAY,CAAC,cACbvB,KAAA,CAACf,GAAG,EAAC6C,EAAE,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAhB,QAAA,eAC5DzB,IAAA,CAACR,SAAS,EACRkD,KAAK,CAAC,WAAW,CACjBxB,KAAK,CAAEd,QAAQ,CAACE,QAAS,CACzBqC,QAAQ,CAAE7B,iBAAiB,CAAC,UAAU,CAAE,CACxC8B,SAAS,MACV,CAAC,cACF5C,IAAA,CAACR,SAAS,EACRkD,KAAK,CAAC,kBAAkB,CACxBxB,KAAK,CAAEd,QAAQ,CAACG,eAAgB,CAChCoC,QAAQ,CAAE7B,iBAAiB,CAAC,iBAAiB,CAAE,CAC/C+B,SAAS,MACTC,IAAI,CAAE,CAAE,CACRF,SAAS,MACV,CAAC,cACF1C,KAAA,CAACV,SAAS,EACRkD,KAAK,CAAC,UAAU,CAChBxB,KAAK,CAAEd,QAAQ,CAACO,QAAS,CACzBgC,QAAQ,CAAE7B,iBAAiB,CAAC,UAAU,CAAE,CACxC8B,SAAS,MACTG,MAAM,MACNC,WAAW,CAAE,CACXC,MAAM,CAAE,IACV,CAAE,CAAAxB,QAAA,eAEFzB,IAAA,WAAQkB,KAAK,CAAC,IAAI,CAAAO,QAAA,CAAC,SAAO,CAAQ,CAAC,cACnCzB,IAAA,WAAQkB,KAAK,CAAC,IAAI,CAAAO,QAAA,CAAC,SAAO,CAAQ,CAAC,cACnCzB,IAAA,WAAQkB,KAAK,CAAC,IAAI,CAAAO,QAAA,CAAC,QAAM,CAAQ,CAAC,cAClCzB,IAAA,WAAQkB,KAAK,CAAC,IAAI,CAAAO,QAAA,CAAC,QAAM,CAAQ,CAAC,EACzB,CAAC,EACT,CAAC,EACD,CAAC,CACJ,CAAC,cAEPzB,IAAA,CAACT,IAAI,EAAC6C,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cAClBvB,KAAA,CAACb,KAAK,EAAC2C,EAAE,CAAE,CAAEM,CAAC,CAAE,CAAE,CAAE,CAAAb,QAAA,eAClBzB,IAAA,CAACZ,UAAU,EAACsC,OAAO,CAAC,IAAI,CAACE,YAAY,MAAAH,QAAA,CAAC,eAEtC,CAAY,CAAC,cACbvB,KAAA,CAACf,GAAG,EAAC6C,EAAE,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAhB,QAAA,eAC5DzB,IAAA,CAACL,gBAAgB,EACfuD,OAAO,cACLlD,IAAA,CAACN,MAAM,EACL0B,OAAO,CAAEhB,QAAQ,CAACI,kBAAmB,CACrCmC,QAAQ,CAAExB,kBAAkB,CAAC,oBAAoB,CAAE,CACpD,CACF,CACDuB,KAAK,CAAC,qBAAqB,CAC5B,CAAC,cACF1C,IAAA,CAACL,gBAAgB,EACfuD,OAAO,cACLlD,IAAA,CAACN,MAAM,EACL0B,OAAO,CAAEhB,QAAQ,CAACK,iBAAkB,CACpCkC,QAAQ,CAAExB,kBAAkB,CAAC,mBAAmB,CAAE,CACnD,CACF,CACDuB,KAAK,CAAC,oBAAoB,CAC3B,CAAC,cACF1C,IAAA,CAACJ,OAAO,EAACoC,EAAE,CAAE,CAAEmB,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1BnD,IAAA,CAACL,gBAAgB,EACfuD,OAAO,cACLlD,IAAA,CAACN,MAAM,EACL0B,OAAO,CAAEhB,QAAQ,CAACM,QAAS,CAC3BiC,QAAQ,CAAExB,kBAAkB,CAAC,UAAU,CAAE,CAC1C,CACF,CACDuB,KAAK,CAAC,WAAW,CAClB,CAAC,EACC,CAAC,EACD,CAAC,CACJ,CAAC,cAEP1C,IAAA,CAACT,IAAI,EAAC6C,EAAE,CAAE,EAAG,CAAAX,QAAA,cACXvB,KAAA,CAACb,KAAK,EAAC2C,EAAE,CAAE,CAAEM,CAAC,CAAE,CAAE,CAAE,CAAAb,QAAA,eAClBzB,IAAA,CAACZ,UAAU,EAACsC,OAAO,CAAC,IAAI,CAACE,YAAY,MAAAH,QAAA,CAAC,mBAEtC,CAAY,CAAC,cACbzB,IAAA,CAACZ,UAAU,EAACsC,OAAO,CAAC,OAAO,CAACG,KAAK,CAAC,eAAe,CAACC,SAAS,MAAAL,QAAA,CAAC,+DAE5D,CAAY,CAAC,cACbvB,KAAA,CAACf,GAAG,EAAC6C,EAAE,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEE,GAAG,CAAE,CAAE,CAAE,CAAAhB,QAAA,eACnCzB,IAAA,CAACP,MAAM,EAACiC,OAAO,CAAC,UAAU,CAAAD,QAAA,CAAC,iBAE3B,CAAQ,CAAC,cACTzB,IAAA,CAACP,MAAM,EAACiC,OAAO,CAAC,UAAU,CAAAD,QAAA,CAAC,2BAE3B,CAAQ,CAAC,cACTzB,IAAA,CAACP,MAAM,EAACiC,OAAO,CAAC,UAAU,CAAAD,QAAA,CAAC,UAE3B,CAAQ,CAAC,EACN,CAAC,EACD,CAAC,CACJ,CAAC,cAEPzB,IAAA,CAACT,IAAI,EAAC6C,EAAE,CAAE,EAAG,CAAAX,QAAA,cACXvB,KAAA,CAACf,GAAG,EAAC6C,EAAE,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEa,cAAc,CAAE,UAAU,CAAEX,GAAG,CAAE,CAAE,CAAE,CAAAhB,QAAA,eAC/DzB,IAAA,CAACP,MAAM,EAACiC,OAAO,CAAC,UAAU,CAAAD,QAAA,CAAC,mBAE3B,CAAQ,CAAC,cACTzB,IAAA,CAACP,MAAM,EACLiC,OAAO,CAAC,WAAW,CACnB2B,SAAS,cAAErD,IAAA,CAACF,IAAI,GAAE,CAAE,CACpBwD,OAAO,CAAEjC,UAAW,CAAAI,QAAA,CACrB,eAED,CAAQ,CAAC,EACN,CAAC,CACF,CAAC,EACH,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}