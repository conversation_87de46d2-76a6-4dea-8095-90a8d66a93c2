{"ast": null, "code": "import { useState, useEffect } from 'react';\n/**\n * Fetch and load an image for programatic use such as in a `<canvas>` element.\n *\n * @param imageOrUrl The `HtmlImageElement` or image url to load\n * @param crossOrigin The `crossorigin` attribute to set\n *\n * ```ts\n * const { image, error } = useImage('/static/kittens.png')\n * const ref = useRef<HTMLCanvasElement>()\n *\n * useEffect(() => {\n *   const ctx = ref.current.getContext('2d')\n *\n *   if (image) {\n *     ctx.drawImage(image, 0, 0)\n *   }\n * }, [ref, image])\n *\n * return (\n *   <>\n *     {error && \"there was a problem loading the image\"}\n *     <canvas ref={ref} />\n *   </>\n * ```\n */\nexport default function useImage(imageOrUrl, crossOrigin) {\n  const [state, setState] = useState({\n    image: null,\n    error: null\n  });\n  useEffect(() => {\n    if (!imageOrUrl) return undefined;\n    let image;\n    if (typeof imageOrUrl === 'string') {\n      image = new Image();\n      if (crossOrigin) image.crossOrigin = crossOrigin;\n      image.src = imageOrUrl;\n    } else {\n      image = imageOrUrl;\n      if (image.complete && image.naturalHeight > 0) {\n        setState({\n          image,\n          error: null\n        });\n        return;\n      }\n    }\n    function onLoad() {\n      setState({\n        image,\n        error: null\n      });\n    }\n    function onError(error) {\n      setState({\n        image,\n        error\n      });\n    }\n    image.addEventListener('load', onLoad);\n    image.addEventListener('error', onError);\n    return () => {\n      image.removeEventListener('load', onLoad);\n      image.removeEventListener('error', onError);\n    };\n  }, [imageOrUrl, crossOrigin]);\n  return state;\n}", "map": {"version": 3, "names": ["useState", "useEffect", "useImage", "imageOrUrl", "crossOrigin", "state", "setState", "image", "error", "undefined", "Image", "src", "complete", "naturalHeight", "onLoad", "onError", "addEventListener", "removeEventListener"], "sources": ["C:/laragon/www/frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useImage.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\n/**\n * Fetch and load an image for programatic use such as in a `<canvas>` element.\n *\n * @param imageOrUrl The `HtmlImageElement` or image url to load\n * @param crossOrigin The `crossorigin` attribute to set\n *\n * ```ts\n * const { image, error } = useImage('/static/kittens.png')\n * const ref = useRef<HTMLCanvasElement>()\n *\n * useEffect(() => {\n *   const ctx = ref.current.getContext('2d')\n *\n *   if (image) {\n *     ctx.drawImage(image, 0, 0)\n *   }\n * }, [ref, image])\n *\n * return (\n *   <>\n *     {error && \"there was a problem loading the image\"}\n *     <canvas ref={ref} />\n *   </>\n * ```\n */\nexport default function useImage(imageOrUrl, crossOrigin) {\n  const [state, setState] = useState({\n    image: null,\n    error: null\n  });\n  useEffect(() => {\n    if (!imageOrUrl) return undefined;\n    let image;\n    if (typeof imageOrUrl === 'string') {\n      image = new Image();\n      if (crossOrigin) image.crossOrigin = crossOrigin;\n      image.src = imageOrUrl;\n    } else {\n      image = imageOrUrl;\n      if (image.complete && image.naturalHeight > 0) {\n        setState({\n          image,\n          error: null\n        });\n        return;\n      }\n    }\n    function onLoad() {\n      setState({\n        image,\n        error: null\n      });\n    }\n    function onError(error) {\n      setState({\n        image,\n        error\n      });\n    }\n    image.addEventListener('load', onLoad);\n    image.addEventListener('error', onError);\n    return () => {\n      image.removeEventListener('load', onLoad);\n      image.removeEventListener('error', onError);\n    };\n  }, [imageOrUrl, crossOrigin]);\n  return state;\n}"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,QAAQA,CAACC,UAAU,EAAEC,WAAW,EAAE;EACxD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGN,QAAQ,CAAC;IACjCO,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE;EACT,CAAC,CAAC;EACFP,SAAS,CAAC,MAAM;IACd,IAAI,CAACE,UAAU,EAAE,OAAOM,SAAS;IACjC,IAAIF,KAAK;IACT,IAAI,OAAOJ,UAAU,KAAK,QAAQ,EAAE;MAClCI,KAAK,GAAG,IAAIG,KAAK,CAAC,CAAC;MACnB,IAAIN,WAAW,EAAEG,KAAK,CAACH,WAAW,GAAGA,WAAW;MAChDG,KAAK,CAACI,GAAG,GAAGR,UAAU;IACxB,CAAC,MAAM;MACLI,KAAK,GAAGJ,UAAU;MAClB,IAAII,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACM,aAAa,GAAG,CAAC,EAAE;QAC7CP,QAAQ,CAAC;UACPC,KAAK;UACLC,KAAK,EAAE;QACT,CAAC,CAAC;QACF;MACF;IACF;IACA,SAASM,MAAMA,CAAA,EAAG;MAChBR,QAAQ,CAAC;QACPC,KAAK;QACLC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;IACA,SAASO,OAAOA,CAACP,KAAK,EAAE;MACtBF,QAAQ,CAAC;QACPC,KAAK;QACLC;MACF,CAAC,CAAC;IACJ;IACAD,KAAK,CAACS,gBAAgB,CAAC,MAAM,EAAEF,MAAM,CAAC;IACtCP,KAAK,CAACS,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;IACxC,OAAO,MAAM;MACXR,KAAK,CAACU,mBAAmB,CAAC,MAAM,EAAEH,MAAM,CAAC;MACzCP,KAAK,CAACU,mBAAmB,CAAC,OAAO,EAAEF,OAAO,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,CAACZ,UAAU,EAAEC,WAAW,CAAC,CAAC;EAC7B,OAAOC,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}