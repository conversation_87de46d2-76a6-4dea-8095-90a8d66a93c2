{"ast": null, "code": "import api, { endpoints } from './api';\nclass PrintingService {\n  // Categories\n  async getCategories() {\n    const response = await api.get(`${endpoints.printing}/categories`);\n    return response.data.data;\n  }\n\n  // Products\n  async getProducts(categorySlug) {\n    const url = categorySlug ? `${endpoints.printing}/products/category/${categorySlug}` : `${endpoints.printing}/products`;\n    const response = await api.get(url);\n    return response.data.data;\n  }\n  async getProduct(slug) {\n    const response = await api.get(`${endpoints.printing}/product/${slug}`);\n    return response.data.data;\n  }\n  async calculatePrice(productId, quantity, options) {\n    const response = await api.post(`${endpoints.printing}/calculate-price`, {\n      product_id: productId,\n      quantity,\n      options: options || {}\n    });\n    return response.data.data;\n  }\n\n  // Orders\n  async getOrders(page = 1) {\n    const response = await api.get(`${endpoints.orders}?page=${page}`);\n    return response.data;\n  }\n  async getOrder(id) {\n    const response = await api.get(`${endpoints.orders}/${id}`);\n    return response.data.data;\n  }\n  async createOrder(orderData) {\n    const response = await api.post(endpoints.orders, orderData);\n    return response.data.data;\n  }\n  async cancelOrder(id) {\n    const response = await api.post(`${endpoints.orders}/${id}/cancel`);\n    return response.data.data;\n  }\n  async reorder(id) {\n    const response = await api.post(`${endpoints.orders}/${id}/reorder`);\n    return response.data.data;\n  }\n\n  // File uploads\n  async uploadFiles(orderId, files, fileType = 'artwork') {\n    const formData = new FormData();\n    files.forEach(file => {\n      formData.append('files[]', file);\n    });\n    formData.append('file_type', fileType);\n    const response = await api.post(`${endpoints.orders}/${orderId}/files`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data.data;\n  }\n  async getOrderFiles(orderId) {\n    const response = await api.get(`${endpoints.orders}/${orderId}/files`);\n    return response.data.data;\n  }\n  async deleteFile(orderId, fileId) {\n    await api.delete(`${endpoints.orders}/${orderId}/files/${fileId}`);\n  }\n\n  // File validation helpers\n  validateFile(file) {\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml', 'application/pdf'];\n    if (file.size > maxSize) {\n      return {\n        valid: false,\n        message: 'File size must be less than 50MB'\n      };\n    }\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        message: 'File must be PNG, JPG, SVG, or PDF format'\n      };\n    }\n    return {\n      valid: true\n    };\n  }\n  async validateImageDPI(file) {\n    return new Promise(resolve => {\n      if (!file.type.startsWith('image/')) {\n        resolve({});\n        return;\n      }\n      const img = new Image();\n      img.onload = () => {\n        const dimensions = {\n          width: img.naturalWidth,\n          height: img.naturalHeight\n        };\n\n        // Estimate DPI based on common print sizes\n        const estimatedDPI = this.estimateDPI(dimensions);\n        resolve({\n          dimensions,\n          dpi: estimatedDPI\n        });\n      };\n      img.onerror = () => resolve({});\n      img.src = URL.createObjectURL(file);\n    });\n  }\n  estimateDPI(dimensions) {\n    // Business card at 300 DPI: ~1050x600px\n    // A5 flyer at 300 DPI: ~1240x1754px\n    // This is a rough estimation\n    const {\n      width,\n      height\n    } = dimensions;\n\n    // Common print sizes at 300 DPI\n    const printSizes = [{\n      width: 1050,\n      height: 600,\n      dpi: 300\n    },\n    // Business card\n    {\n      width: 1240,\n      height: 1754,\n      dpi: 300\n    },\n    // A5\n    {\n      width: 2480,\n      height: 3508,\n      dpi: 300\n    } // A4\n    ];\n    for (const size of printSizes) {\n      const widthRatio = width / size.width;\n      const heightRatio = height / size.height;\n      if (Math.abs(widthRatio - heightRatio) < 0.1) {\n        return Math.round(size.dpi * widthRatio);\n      }\n    }\n\n    // Default estimation\n    return Math.round(72 * Math.min(width / 800, height / 600));\n  }\n}\nexport default new PrintingService();", "map": {"version": 3, "names": ["api", "endpoints", "PrintingService", "getCategories", "response", "get", "printing", "data", "getProducts", "categorySlug", "url", "getProduct", "slug", "calculatePrice", "productId", "quantity", "options", "post", "product_id", "getOrders", "page", "orders", "getOrder", "id", "createOrder", "orderData", "cancelOrder", "reorder", "uploadFiles", "orderId", "files", "fileType", "formData", "FormData", "for<PERSON>ach", "file", "append", "headers", "getOrderFiles", "deleteFile", "fileId", "delete", "validateFile", "maxSize", "allowedTypes", "size", "valid", "message", "includes", "type", "validateImageDPI", "Promise", "resolve", "startsWith", "img", "Image", "onload", "dimensions", "width", "naturalWidth", "height", "naturalHeight", "estimatedDPI", "estimateDPI", "dpi", "onerror", "src", "URL", "createObjectURL", "printSizes", "widthRatio", "heightRatio", "Math", "abs", "round", "min"], "sources": ["C:/laragon/www/frontend/src/services/printingService.ts"], "sourcesContent": ["import api, { endpoints } from './api';\n\nexport interface PrintingCategory {\n  id: number;\n  name: string;\n  description: string;\n  slug: string;\n  image: string | null;\n  is_active: boolean;\n  sort_order: number;\n  products_count?: number;\n}\n\nexport interface PrintingProduct {\n  id: number;\n  printing_category_id: number;\n  name: string;\n  description: string;\n  slug: string;\n  base_price: number;\n  formatted_base_price: string;\n  image: string | null;\n  specifications: Record<string, any>;\n  options: Record<string, any>;\n  is_active: boolean;\n  min_quantity: number;\n  max_quantity: number | null;\n  production_time_days: number;\n  category?: PrintingCategory;\n}\n\nexport interface OrderItem {\n  product_id: number;\n  quantity: number;\n  specifications?: Record<string, any>;\n  selected_options?: Record<string, any>;\n  notes?: string;\n}\n\nexport interface PrintingOrder {\n  id: number;\n  user_id: number;\n  order_number: string;\n  status: string;\n  status_label: string;\n  total_amount: number;\n  formatted_total_amount: string;\n  payment_status: string;\n  payment_status_label: string;\n  payment_method: string | null;\n  payment_reference: string | null;\n  special_instructions: string | null;\n  delivery_address: Record<string, any> | null;\n  delivery_method: string | null;\n  estimated_completion_date: string | null;\n  completed_at: string | null;\n  shipped_at: string | null;\n  notes: string | null;\n  items: OrderItemDetail[];\n  files: OrderFile[];\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface OrderItemDetail {\n  id: number;\n  printing_order_id: number;\n  printing_product_id: number;\n  quantity: number;\n  unit_price: number;\n  total_price: number;\n  formatted_unit_price: string;\n  formatted_total_price: string;\n  specifications: Record<string, any>;\n  selected_options: Record<string, any>;\n  notes: string | null;\n  product: PrintingProduct;\n}\n\nexport interface OrderFile {\n  id: number;\n  printing_order_id: number;\n  original_name: string;\n  file_name: string;\n  file_path: string;\n  file_size: number;\n  formatted_file_size: string;\n  mime_type: string;\n  file_type: string;\n  file_type_label: string;\n  dimensions: { width: number; height: number } | null;\n  dpi: number | null;\n  dpi_status: string;\n  is_approved: boolean;\n  notes: string | null;\n  file_url: string;\n  is_image: boolean;\n  uploaded_by: number;\n  uploader?: { id: number; name: string };\n  created_at: string;\n}\n\nexport interface PriceCalculation {\n  product_id: number;\n  quantity: number;\n  unit_price: number;\n  total_price: number;\n  formatted_total_price: string;\n  production_time_days: number;\n}\n\nclass PrintingService {\n  // Categories\n  async getCategories(): Promise<PrintingCategory[]> {\n    const response = await api.get(`${endpoints.printing}/categories`);\n    return response.data.data;\n  }\n\n  // Products\n  async getProducts(categorySlug?: string): Promise<PrintingProduct[]> {\n    const url = categorySlug\n      ? `${endpoints.printing}/products/category/${categorySlug}`\n      : `${endpoints.printing}/products`;\n    const response = await api.get(url);\n    return response.data.data;\n  }\n\n  async getProduct(slug: string): Promise<PrintingProduct> {\n    const response = await api.get(`${endpoints.printing}/product/${slug}`);\n    return response.data.data;\n  }\n\n  async calculatePrice(productId: number, quantity: number, options?: Record<string, any>): Promise<PriceCalculation> {\n    const response = await api.post(`${endpoints.printing}/calculate-price`, {\n      product_id: productId,\n      quantity,\n      options: options || {}\n    });\n    return response.data.data;\n  }\n\n  // Orders\n  async getOrders(page: number = 1): Promise<{ data: PrintingOrder[]; meta: any }> {\n    const response = await api.get(`${endpoints.orders}?page=${page}`);\n    return response.data;\n  }\n\n  async getOrder(id: number): Promise<PrintingOrder> {\n    const response = await api.get(`${endpoints.orders}/${id}`);\n    return response.data.data;\n  }\n\n  async createOrder(orderData: {\n    items: OrderItem[];\n    special_instructions?: string;\n    delivery_address?: Record<string, any>;\n    delivery_method?: string;\n  }): Promise<PrintingOrder> {\n    const response = await api.post(endpoints.orders, orderData);\n    return response.data.data;\n  }\n\n  async cancelOrder(id: number): Promise<PrintingOrder> {\n    const response = await api.post(`${endpoints.orders}/${id}/cancel`);\n    return response.data.data;\n  }\n\n  async reorder(id: number): Promise<PrintingOrder> {\n    const response = await api.post(`${endpoints.orders}/${id}/reorder`);\n    return response.data.data;\n  }\n\n  // File uploads\n  async uploadFiles(orderId: number, files: File[], fileType: string = 'artwork'): Promise<OrderFile[]> {\n    const formData = new FormData();\n    files.forEach(file => {\n      formData.append('files[]', file);\n    });\n    formData.append('file_type', fileType);\n\n    const response = await api.post(`${endpoints.orders}/${orderId}/files`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data.data;\n  }\n\n  async getOrderFiles(orderId: number): Promise<OrderFile[]> {\n    const response = await api.get(`${endpoints.orders}/${orderId}/files`);\n    return response.data.data;\n  }\n\n  async deleteFile(orderId: number, fileId: number): Promise<void> {\n    await api.delete(`${endpoints.orders}/${orderId}/files/${fileId}`);\n  }\n\n  // File validation helpers\n  validateFile(file: File): { valid: boolean; message?: string } {\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml', 'application/pdf'];\n\n    if (file.size > maxSize) {\n      return { valid: false, message: 'File size must be less than 50MB' };\n    }\n\n    if (!allowedTypes.includes(file.type)) {\n      return { valid: false, message: 'File must be PNG, JPG, SVG, or PDF format' };\n    }\n\n    return { valid: true };\n  }\n\n  async validateImageDPI(file: File): Promise<{ dpi?: number; dimensions?: { width: number; height: number } }> {\n    return new Promise((resolve) => {\n      if (!file.type.startsWith('image/')) {\n        resolve({});\n        return;\n      }\n\n      const img = new Image();\n      img.onload = () => {\n        const dimensions = {\n          width: img.naturalWidth,\n          height: img.naturalHeight\n        };\n\n        // Estimate DPI based on common print sizes\n        const estimatedDPI = this.estimateDPI(dimensions);\n\n        resolve({ dimensions, dpi: estimatedDPI });\n      };\n      img.onerror = () => resolve({});\n      img.src = URL.createObjectURL(file);\n    });\n  }\n\n  private estimateDPI(dimensions: { width: number; height: number }): number {\n    // Business card at 300 DPI: ~1050x600px\n    // A5 flyer at 300 DPI: ~1240x1754px\n    // This is a rough estimation\n    const { width, height } = dimensions;\n\n    // Common print sizes at 300 DPI\n    const printSizes = [\n      { width: 1050, height: 600, dpi: 300 }, // Business card\n      { width: 1240, height: 1754, dpi: 300 }, // A5\n      { width: 2480, height: 3508, dpi: 300 }, // A4\n    ];\n\n    for (const size of printSizes) {\n      const widthRatio = width / size.width;\n      const heightRatio = height / size.height;\n\n      if (Math.abs(widthRatio - heightRatio) < 0.1) {\n        return Math.round(size.dpi * widthRatio);\n      }\n    }\n\n    // Default estimation\n    return Math.round(72 * Math.min(width / 800, height / 600));\n  }\n}\n\nexport default new PrintingService();\n"], "mappings": "AAAA,OAAOA,GAAG,IAAIC,SAAS,QAAQ,OAAO;AA+GtC,MAAMC,eAAe,CAAC;EACpB;EACA,MAAMC,aAAaA,CAAA,EAAgC;IACjD,MAAMC,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,GAAGJ,SAAS,CAACK,QAAQ,aAAa,CAAC;IAClE,OAAOF,QAAQ,CAACG,IAAI,CAACA,IAAI;EAC3B;;EAEA;EACA,MAAMC,WAAWA,CAACC,YAAqB,EAA8B;IACnE,MAAMC,GAAG,GAAGD,YAAY,GACpB,GAAGR,SAAS,CAACK,QAAQ,sBAAsBG,YAAY,EAAE,GACzD,GAAGR,SAAS,CAACK,QAAQ,WAAW;IACpC,MAAMF,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAACK,GAAG,CAAC;IACnC,OAAON,QAAQ,CAACG,IAAI,CAACA,IAAI;EAC3B;EAEA,MAAMI,UAAUA,CAACC,IAAY,EAA4B;IACvD,MAAMR,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,GAAGJ,SAAS,CAACK,QAAQ,YAAYM,IAAI,EAAE,CAAC;IACvE,OAAOR,QAAQ,CAACG,IAAI,CAACA,IAAI;EAC3B;EAEA,MAAMM,cAAcA,CAACC,SAAiB,EAAEC,QAAgB,EAAEC,OAA6B,EAA6B;IAClH,MAAMZ,QAAQ,GAAG,MAAMJ,GAAG,CAACiB,IAAI,CAAC,GAAGhB,SAAS,CAACK,QAAQ,kBAAkB,EAAE;MACvEY,UAAU,EAAEJ,SAAS;MACrBC,QAAQ;MACRC,OAAO,EAAEA,OAAO,IAAI,CAAC;IACvB,CAAC,CAAC;IACF,OAAOZ,QAAQ,CAACG,IAAI,CAACA,IAAI;EAC3B;;EAEA;EACA,MAAMY,SAASA,CAACC,IAAY,GAAG,CAAC,EAAiD;IAC/E,MAAMhB,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,GAAGJ,SAAS,CAACoB,MAAM,SAASD,IAAI,EAAE,CAAC;IAClE,OAAOhB,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAMe,QAAQA,CAACC,EAAU,EAA0B;IACjD,MAAMnB,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,GAAGJ,SAAS,CAACoB,MAAM,IAAIE,EAAE,EAAE,CAAC;IAC3D,OAAOnB,QAAQ,CAACG,IAAI,CAACA,IAAI;EAC3B;EAEA,MAAMiB,WAAWA,CAACC,SAKjB,EAA0B;IACzB,MAAMrB,QAAQ,GAAG,MAAMJ,GAAG,CAACiB,IAAI,CAAChB,SAAS,CAACoB,MAAM,EAAEI,SAAS,CAAC;IAC5D,OAAOrB,QAAQ,CAACG,IAAI,CAACA,IAAI;EAC3B;EAEA,MAAMmB,WAAWA,CAACH,EAAU,EAA0B;IACpD,MAAMnB,QAAQ,GAAG,MAAMJ,GAAG,CAACiB,IAAI,CAAC,GAAGhB,SAAS,CAACoB,MAAM,IAAIE,EAAE,SAAS,CAAC;IACnE,OAAOnB,QAAQ,CAACG,IAAI,CAACA,IAAI;EAC3B;EAEA,MAAMoB,OAAOA,CAACJ,EAAU,EAA0B;IAChD,MAAMnB,QAAQ,GAAG,MAAMJ,GAAG,CAACiB,IAAI,CAAC,GAAGhB,SAAS,CAACoB,MAAM,IAAIE,EAAE,UAAU,CAAC;IACpE,OAAOnB,QAAQ,CAACG,IAAI,CAACA,IAAI;EAC3B;;EAEA;EACA,MAAMqB,WAAWA,CAACC,OAAe,EAAEC,KAAa,EAAEC,QAAgB,GAAG,SAAS,EAAwB;IACpG,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BH,KAAK,CAACI,OAAO,CAACC,IAAI,IAAI;MACpBH,QAAQ,CAACI,MAAM,CAAC,SAAS,EAAED,IAAI,CAAC;IAClC,CAAC,CAAC;IACFH,QAAQ,CAACI,MAAM,CAAC,WAAW,EAAEL,QAAQ,CAAC;IAEtC,MAAM3B,QAAQ,GAAG,MAAMJ,GAAG,CAACiB,IAAI,CAAC,GAAGhB,SAAS,CAACoB,MAAM,IAAIQ,OAAO,QAAQ,EAAEG,QAAQ,EAAE;MAChFK,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOjC,QAAQ,CAACG,IAAI,CAACA,IAAI;EAC3B;EAEA,MAAM+B,aAAaA,CAACT,OAAe,EAAwB;IACzD,MAAMzB,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,GAAGJ,SAAS,CAACoB,MAAM,IAAIQ,OAAO,QAAQ,CAAC;IACtE,OAAOzB,QAAQ,CAACG,IAAI,CAACA,IAAI;EAC3B;EAEA,MAAMgC,UAAUA,CAACV,OAAe,EAAEW,MAAc,EAAiB;IAC/D,MAAMxC,GAAG,CAACyC,MAAM,CAAC,GAAGxC,SAAS,CAACoB,MAAM,IAAIQ,OAAO,UAAUW,MAAM,EAAE,CAAC;EACpE;;EAEA;EACAE,YAAYA,CAACP,IAAU,EAAwC;IAC7D,MAAMQ,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,MAAMC,YAAY,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,EAAE,iBAAiB,CAAC;IAEjG,IAAIT,IAAI,CAACU,IAAI,GAAGF,OAAO,EAAE;MACvB,OAAO;QAAEG,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAmC,CAAC;IACtE;IAEA,IAAI,CAACH,YAAY,CAACI,QAAQ,CAACb,IAAI,CAACc,IAAI,CAAC,EAAE;MACrC,OAAO;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAA4C,CAAC;IAC/E;IAEA,OAAO;MAAED,KAAK,EAAE;IAAK,CAAC;EACxB;EAEA,MAAMI,gBAAgBA,CAACf,IAAU,EAA6E;IAC5G,OAAO,IAAIgB,OAAO,CAAEC,OAAO,IAAK;MAC9B,IAAI,CAACjB,IAAI,CAACc,IAAI,CAACI,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnCD,OAAO,CAAC,CAAC,CAAC,CAAC;QACX;MACF;MAEA,MAAME,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;QACjB,MAAMC,UAAU,GAAG;UACjBC,KAAK,EAAEJ,GAAG,CAACK,YAAY;UACvBC,MAAM,EAAEN,GAAG,CAACO;QACd,CAAC;;QAED;QACA,MAAMC,YAAY,GAAG,IAAI,CAACC,WAAW,CAACN,UAAU,CAAC;QAEjDL,OAAO,CAAC;UAAEK,UAAU;UAAEO,GAAG,EAAEF;QAAa,CAAC,CAAC;MAC5C,CAAC;MACDR,GAAG,CAACW,OAAO,GAAG,MAAMb,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/BE,GAAG,CAACY,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACjC,IAAI,CAAC;IACrC,CAAC,CAAC;EACJ;EAEQ4B,WAAWA,CAACN,UAA6C,EAAU;IACzE;IACA;IACA;IACA,MAAM;MAAEC,KAAK;MAAEE;IAAO,CAAC,GAAGH,UAAU;;IAEpC;IACA,MAAMY,UAAU,GAAG,CACjB;MAAEX,KAAK,EAAE,IAAI;MAAEE,MAAM,EAAE,GAAG;MAAEI,GAAG,EAAE;IAAI,CAAC;IAAE;IACxC;MAAEN,KAAK,EAAE,IAAI;MAAEE,MAAM,EAAE,IAAI;MAAEI,GAAG,EAAE;IAAI,CAAC;IAAE;IACzC;MAAEN,KAAK,EAAE,IAAI;MAAEE,MAAM,EAAE,IAAI;MAAEI,GAAG,EAAE;IAAI,CAAC,CAAE;IAAA,CAC1C;IAED,KAAK,MAAMnB,IAAI,IAAIwB,UAAU,EAAE;MAC7B,MAAMC,UAAU,GAAGZ,KAAK,GAAGb,IAAI,CAACa,KAAK;MACrC,MAAMa,WAAW,GAAGX,MAAM,GAAGf,IAAI,CAACe,MAAM;MAExC,IAAIY,IAAI,CAACC,GAAG,CAACH,UAAU,GAAGC,WAAW,CAAC,GAAG,GAAG,EAAE;QAC5C,OAAOC,IAAI,CAACE,KAAK,CAAC7B,IAAI,CAACmB,GAAG,GAAGM,UAAU,CAAC;MAC1C;IACF;;IAEA;IACA,OAAOE,IAAI,CAACE,KAAK,CAAC,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACjB,KAAK,GAAG,GAAG,EAAEE,MAAM,GAAG,GAAG,CAAC,CAAC;EAC7D;AACF;AAEA,eAAe,IAAI1D,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}